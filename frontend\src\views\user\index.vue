<template>
  <div class="profile h-full p-6 bg-white rounded-t-xl">
    <div class="w-1/2">
      <div class="border-b text-center">
        <DynamicAvatar :id="userStore.userId" :data-info="userStore.userInfo" :relation-name="userInfo.name" :type-avatar="0" :size="60" />
        <div class="mb-6 text-2xl font-bold text-[#0A0A0A] text-center">{{ userInfo.name }}</div>
      </div>
      <div class="pt-6">
        <div class="text-base mb-6 flex">
          <span class="w-20 text-[#6B7280] pt-1">组织</span>
          <el-input v-model="userInfo.orgName" readonly />
        </div>
        <div class="text-base mb-6 flex">
          <span class="w-20 text-[#6B7280] pt-1">电话</span>
          <el-input v-model="otel" readonly v-if="editOtel">
            <template #suffix>
              <el-button link icon="edit" @click="editOtel = false"></el-button>
            </template>
          </el-input>
          <el-input v-model="otel" v-else>
            <template #suffix>
              <el-button link icon="check" @click="save(1)"></el-button>
            </template>
          </el-input>
        </div>
        <div class="text-base mb-6 flex">
          <span class="w-20 text-[#6B7280] pt-1">邮箱</span>
          <el-input v-model="oemail" readonly v-if="editOemail">
            <template #suffix>
              <el-button link icon="edit" @click="editOemail = false"></el-button>
            </template>
          </el-input>
          <el-input v-model="oemail" v-else>
            <template #suffix>
              <el-button link icon="check" @click="save(2)"></el-button>
            </template>
          </el-input>
        </div>
        <div class="text-base mb-6 flex">
          <span class="w-20 text-[#6B7280] pt-1">个性签名</span>
          <el-input v-model="description" readonly v-if="editDesc">
            <template #suffix>
              <el-button link icon="edit" @click="editDesc = false"></el-button>
            </template>
          </el-input>
          <el-input v-model="description" v-else>
            <template #suffix>
              <el-button link icon="check" @click="save(3)"></el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="user">
import { ref } from "vue";
import { useUserStore } from "@/stores/modules/user";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import { ElMessage } from "element-plus";

const userStore = useUserStore();
const userInfo = ref(userStore.userInfo);
const otel = ref(userStore.userInfo.otel);
const oemail = ref(userStore.userInfo.oemail);
const description = ref(userStore.userInfo.description);

const editOtel = ref(true);
const editOemail = ref(true);
const editDesc = ref(true);

const save = async (num: number) => {
  let params: any = {};
  if (num == 1) {
    if (!new RegExp(/^1[3-9]\\d{9}$/).test(otel.value) && !new RegExp(/^(?:\d{3}-|\d{4}-)?\d{7,8}(?:-\d+)?$/).test(otel.value)) {
      ElMessage.error("电话号码错误");
      return;
    }
    params = {
      id: userStore.userId,
      otel: otel.value
    };
  } else if (num == 2) {
    if (!new RegExp(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/).test(oemail.value)) {
      ElMessage.error("邮箱错误");
      return;
    }
    params = {
      id: userStore.userId,
      oemail: oemail.value
    };
  } else {
    if (description.value?.length > 20) {
      ElMessage.error("个性签名长度不能超过20");
      return;
    }
    params = {
      id: userStore.userId,
      description: description.value
    };
  }
  await userStore.updateUserInfoFc(params, num);
  ElMessage.success("操作成功");
  if (num == 1) editOtel.value = true;
  if (num == 2) editOemail.value = true;
  if (num == 3) editDesc.value = true;
};
</script>

<style scoped lang="scss">
.profile {
  :deep(.el-input--default) {
    @apply flex-1;

    .el-input__suffix {
      @apply cursor-pointer;
    }
  }
}
</style>
