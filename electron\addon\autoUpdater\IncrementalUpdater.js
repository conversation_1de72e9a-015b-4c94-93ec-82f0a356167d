const { autoUpdater } = require("electron-updater");
const fs = require("fs");
const os = require("os");
const path = require("path");
const Log = require("ee-core/log");
const is = require("ee-core/utils/is");
const { app: electronApp } = require("electron");
const Conf = require("ee-core/config");
const LoadingController = require("./loading");

/**
 * 增量更新类
 * 功能逻辑：
 * 1. 软件启动时检查更新，若有更新则下载到内部下载目录；
 * 2. 下载完成后模拟将更新文件复制到系统临时目录；
 * 3. 软件退出时，检测临时目录中是否存在更新文件，若存在则替换当前可执行文件，并删除临时文件。
 *
 * 注意：
 * - 实际环境中，electron-updater 内部管理下载文件，并不一定能直接获取更新文件路径，需根据具体实现调整；
 * - 替换当前可执行文件可能涉及权限问题，生产环境需谨慎处理。
 */
class IncrementalUpdater {
  constructor() {
    // 临时更新文件的路径
    this.tempUpdateFile = null;
    // 初始化事件监听
    this.initEventListeners();
    // 初始化自动更新配置
    this.initAutoUpdaterConfig();
    // 初始化退出加载框
    this.loading = new LoadingController();
  }

  /**
   * 初始化自动更新配置
   */
  initAutoUpdaterConfig() {
    autoUpdater.forceDevUpdateConfig = true;
  }

  /**
   * 初始化自动更新事件监听
   */
  initEventListeners() {
    if (is.windows() || is.macOS() || is.linux()) {
      // 当更新下载完成时，触发该事件
      autoUpdater.on("update-downloaded", (info) => {
        Log.info("[IncrementalUpdater] 更新下载完成，开始复制到临时目录");
        this.handleUpdateDownloaded(info);
      });

      autoUpdater.on("error", (err) => {
        Log.error("[IncrementalUpdater] 更新过程中出现错误:", err);
      });

      autoUpdater.on("update-available", (info) => {
        Log.info("[IncrementalUpdater] 发现可用更新:", info);
      });

      autoUpdater.on("update-not-available", (info) => {
        Log.info("[IncrementalUpdater] 没有可用更新:", info);
      });
    } else {
      Log.error("[IncrementalUpdater] 更新初始化异常:", "不支持的操作系统");
    }
  }

  /**
   * 启动时检测更新
   */
  checkForUpdates() {
    autoUpdater.checkForUpdates().catch((err) => {
      Log.error("[IncrementalUpdater] 检查更新异常:", err);
    });
  }

  /**
   * 处理更新文件下载完成
   * 将下载完成的更新文件复制到系统临时目录中
   * @param {Object} info 更新信息，其中假设包含 filePath 属性
   */
  async handleUpdateDownloaded(info) {
    try {
      Log.info("[IncrementalUpdater] 更新下载完成，准备复制到临时目录:", info);
      if (info.downloadedFile) {
        const tempDir = os.tmpdir();
        const updateFileName = info.path;
        this.tempUpdateFile = path.join(tempDir, updateFileName);
        await fs.promises.copyFile(info.downloadedFile, this.tempUpdateFile);
        Log.info(
          "[IncrementalUpdater] 更新文件已成功复制到临时目录:",
          this.tempUpdateFile
        );
      } else {
        Log.warn(
          "[IncrementalUpdater] 未获取到更新文件路径，无法复制到临时目录"
        );
      }
    } catch (err) {
      Log.error("[IncrementalUpdater] 将更新文件复制到临时目录时失败:", err);
    }
  }

  /**
   * 软件退出时调用
   * 检测临时目录中是否存在更新文件，若存在则替换当前可执行文件并删除临时更新文件
   */
  async replaceExecutableOnShutdown() {
    if (this.tempUpdateFile && fs.existsSync(this.tempUpdateFile)) {
      // 展示加载框
      await this.loading.show();

      // 假设这里更新的是 asar 文件
      const currentAsar = path.join(path.dirname(process.execPath), "app.asar");
      Log.info(
        "[IncrementalUpdater] 检测到临时更新文件，开始替换当前 asar 文件:",
        currentAsar
      );

      try {
        await this.loading.toggleText("正在替换旧版本文件...");
        await fs.promises.copyFile(this.tempUpdateFile, currentAsar);
        Log.info("[IncrementalUpdater] 替换当前 asar 文件成功");

        await this.loading.toggleText("正在清理临时文件...");
        await fs.promises.unlink(this.tempUpdateFile);
        Log.info("[IncrementalUpdater] 删除临时更新文件成功");

        await this.loading.toggleText("版本更新完成");
      } catch (err) {
        Log.error("[IncrementalUpdater] 替换 asar 文件失败:", err);
        await this.loading.toggleText("版本更新失败，请稍后重试");
      }
    } else {
      Log.info("[IncrementalUpdater] 未检测到临时更新文件，无需替换");
    }
  }
}

IncrementalUpdater.toString = () => "[class IncrementalUpdater]";
module.exports = IncrementalUpdater;
