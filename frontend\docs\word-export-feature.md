# Tiptap编辑器Word导出功能

## 功能概述

本项目已成功实现了基于Tiptap编辑器的Word文档导出功能，用户可以将笔记内容导出为Word格式(.docx)或Markdown格式(.md)。

## 技术实现

### 依赖包
- `prosemirror-docx`: 用于将ProseMirror文档转换为Word格式
- `file-saver`: 用于在浏览器中下载文件
- `@types/file-saver`: TypeScript类型定义

### 核心功能

1. **导出格式选择**: 用户点击导出按钮后，会弹出对话框让用户选择导出格式
   - Word文档 (.docx)
   - Markdown (.md)

2. **Word导出流程**:
   - 创建临时Tiptap编辑器实例
   - 使用prosemirror-docx的DocxSerializer序列化文档
   - 配置节点序列化器支持各种格式（标题、列表、代码块等）
   - 生成Word文档并触发下载

3. **Markdown导出流程**:
   - 直接使用笔记的纯文本内容
   - 添加元数据（创建时间、更新时间）
   - 生成.md文件并触发下载

## 使用方法

1. 在笔记管理界面选择一条笔记
2. 点击右上角的"导出"按钮（下载图标）
3. 在弹出的对话框中选择导出格式：
   - 点击"Word文档"按钮导出为.docx格式
   - 点击"Markdown"按钮导出为.md格式
4. 文件将自动下载到浏览器的默认下载目录

## 支持的格式

Word导出功能支持以下Tiptap编辑器格式：
- 标题 (H1-H6)
- 段落
- 粗体、斜体、下划线、删除线
- 有序列表和无序列表
- 引用块
- 代码块和行内代码
- 水平分割线
- 表格
- 上标和下标

## 注意事项

- 图片暂时不支持导出到Word文档中
- 导出的Word文档保持了原有的格式结构
- 文件名使用笔记标题，如果没有标题则使用"笔记"作为默认名称

## 错误处理

- 如果导出过程中出现错误，会显示相应的错误提示
- 用户取消导出操作不会显示错误信息
- 网络问题或文件生成失败会提示用户重试
