<template>
  <div class="shortcut-entrances">
    <div class="entrance-list">
      <el-button v-for="(entrance, index) in entrances" :key="index" :type="entrance.type" @click="openEntrance(entrance)">
        {{ entrance.name }}
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { ElButton } from "element-plus";
interface Entrance {
  name: string;
  type: string;
  path: string;
}

const entrances = ref<Entrance[]>([
  { name: "首页", type: "primary", path: "/home" },
  { name: "文档中心", type: "success", path: "/docs" },
  { name: "帮助中心", type: "warning", path: "/help" },
  { name: "关于我们", type: "info", path: "/about" }
]);

const openEntrance = (entrance: Entrance) => {
  // 打开入口逻辑
};
</script>

<style scoped>
.shortcut-entrances {
  padding: 20px;
  background-color: #fff;
  min-height: 100vh;
}

.entrance-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  padding: 20px;
}

.el-button {
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
