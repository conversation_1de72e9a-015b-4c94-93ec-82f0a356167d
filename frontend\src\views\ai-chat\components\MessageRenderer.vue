<template>
  <div class="message-renderer" :data-message-type="messageType">
    <!-- 文件类型渲染 -->
    <div v-if="hasFiles" class="files-section">
      <div v-for="(file, index) in props.files" :key="index" class="file-item">
        <!-- 图片文件 -->
        <div v-if="isImageFile(file)" class="image-file">
          <div class="image-preview" @click="previewImage(file)">
            <img 
              v-if="file.url" 
              :src="file.url" 
              :alt="file.fileName"
              class="preview-image"
            />
            <Icon v-else :icon="imageIcon" width="48" height="48" class="image-placeholder" />
            <div class="image-overlay">
              <Icon icon="mdi:magnify" width="24" height="24" />
            </div>
          </div>
          <div class="file-info">
            <span class="file-name">{{ file.fileName }}</span>
            <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
          </div>
        </div>
        
        <!-- 普通文件 -->
        <div v-else class="regular-file">
          <div class="file-icon">
            <Icon :icon="getFileIcon(file.fileType)" width="32" height="32" />
          </div>
          <div class="file-details">
            <div class="file-name">{{ file.fileName }}</div>
            <div class="file-meta">
              <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
              <span class="file-type">{{ getFileTypeLabel(file.fileType) }}</span>
            </div>
          </div>
          <div v-if="file.url" class="file-actions">
            <button class="download-btn" @click="downloadFile(file)">
              <Icon :icon="downloadIcon" width="16" height="16" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div v-if="props.content.trim()" class="content-section">
      <template v-for="(segment, index) in parsedSegments" :key="index">
        <!-- 卡片类型 -->
        <div v-if="segment.type === 'card'" class="card-container">
          <div class="card-content">
            <div class="card-header">
              <Icon :icon="cardIcon" width="18" height="18" />
              <span class="card-title">信息卡片</span>
            </div>
            <div class="card-body">
              <div
                v-html="renderMarkdown(segment.content)"
                class="markdown-content"
              ></div>
            </div>
          </div>
        </div>

        <!-- 链接预览类型 -->
        <div v-else-if="segment.type === 'link'" class="link-container">
          <LinkPreview :url="segment.url!" />
        </div>

        <!-- Markdown类型 -->
        <div v-else class="markdown-container">
          <div
            v-html="renderMarkdown(segment.content)"
            class="markdown-content"
          ></div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import LinkPreview from './LinkPreview.vue'
import { parseTextWithLinks, shouldShowPreview } from '@/utils/linkUtils'

// 导入图标
import cardIcon from '@iconify-icons/mdi/card-text'
import downloadIcon from '@iconify-icons/mdi/download'
import imageIcon from '@iconify-icons/mdi/image'

interface Props {
  content: string
  isStreaming?: boolean
  messageType?: 'text' | 'file' | 'image' | 'mixed'
  files?: Array<{
    fileId: string | number
    fileName: string
    fileSize: number
    fileType: string
    url?: string
    uploadFileId?: string
  }>
}

interface MessageSegment {
  type: 'card' | 'markdown' | 'file' | 'image' | 'link'
  content: string
  fileInfo?: any
  url?: string
}

const props = withDefaults(defineProps<Props>(), {
  isStreaming: false,
  messageType: 'text',
  files: () => []
})

// 简单的文本渲染函数
const renderText = (content: string): string => {
  if (!content) return ''

  // 预处理：清理多余的换行符
  let processed = content
    // 将多个连续的换行符合并为最多两个（保留段落分隔）
    .replace(/\n{3,}/g, '\n\n')
    // 将单个换行符转为空格（除非前后是段落分隔）
    .replace(/(?<!\n)\n(?!\n)/g, ' ')
    // 清理首尾空白
    .trim()

  // 转义HTML特殊字符
  processed = processed
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')

  // 将双换行转为段落
  processed = processed
    .split('\n\n')
    .map(paragraph => paragraph.trim())
    .filter(paragraph => paragraph.length > 0)
    .map(paragraph => `<p>${paragraph}</p>`)
    .join('')

  // 支持一些基本格式（可选）
  processed = processed
    // 粗体
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // 斜体
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // 代码
    .replace(/`(.*?)`/g, '<code>$1</code>')
    // 简单链接检测（不包含预览的链接）
    .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>')

  return processed
}

// 计算属性
const hasFiles = computed(() => {
  return props.files && props.files.length > 0
})

const messageType = computed(() => {
  return props.messageType || 'text'
})

// 文件处理方法
const isImageFile = (file: any): boolean => {
  return file.fileType && file.fileType.startsWith('image/')
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 添加更多的文件类型图标支持
const getFileIcon = (mimeType: string): string => {
  // 图片类型
  if (mimeType.startsWith('image/')) {
    if (mimeType.includes('svg')) return 'mdi:file-image-outline'
    return 'mdi:file-image'
  }
  
  // 视频类型
  if (mimeType.startsWith('video/')) {
    if (mimeType.includes('mp4')) return 'mdi:video'
    if (mimeType.includes('avi')) return 'mdi:video-vintage'
    return 'mdi:file-video'
  }
  
  // 音频类型
  if (mimeType.startsWith('audio/')) {
    if (mimeType.includes('mp3')) return 'mdi:music'
    if (mimeType.includes('wav')) return 'mdi:waveform'
    return 'mdi:file-music'
  }
  
  // 文档类型
  if (mimeType.includes('pdf')) return 'mdi:file-pdf-box'
  if (mimeType.includes('word') || mimeType.includes('document')) return 'mdi:file-word'
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'mdi:file-excel'
  if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'mdi:file-powerpoint'
  
  // 压缩文件
  if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('7z')) return 'mdi:archive'
  
  // 代码文件
  if (mimeType.includes('json')) return 'mdi:code-json'
  if (mimeType.includes('xml')) return 'mdi:file-xml'
  if (mimeType.includes('html')) return 'mdi:language-html5'
  if (mimeType.includes('css')) return 'mdi:language-css3'
  if (mimeType.includes('javascript')) return 'mdi:language-javascript'
  if (mimeType.includes('typescript')) return 'mdi:language-typescript'
  
  // 文本文件
  if (mimeType.includes('text/')) return 'mdi:file-document-outline'
  
  // 默认文件图标
  return 'mdi:file'
}

const getFileTypeLabel = (mimeType: string): string => {
  // 图片类型
  if (mimeType.startsWith('image/')) {
    if (mimeType.includes('jpeg') || mimeType.includes('jpg')) return 'JPEG图片'
    if (mimeType.includes('png')) return 'PNG图片'
    if (mimeType.includes('gif')) return 'GIF图片'
    if (mimeType.includes('svg')) return 'SVG图片'
    if (mimeType.includes('webp')) return 'WebP图片'
    return '图片'
  }
  
  // 视频类型
  if (mimeType.startsWith('video/')) {
    if (mimeType.includes('mp4')) return 'MP4视频'
    if (mimeType.includes('avi')) return 'AVI视频'
    if (mimeType.includes('mov')) return 'MOV视频'
    if (mimeType.includes('wmv')) return 'WMV视频'
    return '视频'
  }
  
  // 音频类型
  if (mimeType.startsWith('audio/')) {
    if (mimeType.includes('mp3')) return 'MP3音频'
    if (mimeType.includes('wav')) return 'WAV音频'
    if (mimeType.includes('flac')) return 'FLAC音频'
    if (mimeType.includes('m4a')) return 'M4A音频'
    return '音频'
  }
  
  // 文档类型
  if (mimeType.includes('pdf')) return 'PDF文档'
  if (mimeType.includes('word') || mimeType.includes('document')) return 'Word文档'
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'Excel表格'
  if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'PPT演示'
  
  // 压缩文件
  if (mimeType.includes('zip')) return 'ZIP压缩包'
  if (mimeType.includes('rar')) return 'RAR压缩包'
  if (mimeType.includes('7z')) return '7Z压缩包'
  
  // 代码文件
  if (mimeType.includes('json')) return 'JSON文件'
  if (mimeType.includes('xml')) return 'XML文件'
  if (mimeType.includes('html')) return 'HTML文件'
  if (mimeType.includes('css')) return 'CSS文件'
  if (mimeType.includes('javascript')) return 'JavaScript文件'
  if (mimeType.includes('typescript')) return 'TypeScript文件'
  
  // 文本文件
  if (mimeType.includes('text/')) return '文本文件'
  
  return '文件'
}

const downloadFile = (file: any) => {
  if (!file.url) return
  
  const link = document.createElement('a')
  link.href = file.url
  link.download = file.fileName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 图片预览功能
const previewImage = (file: any) => {
  if (!file.url || !file.fileType.startsWith('image/')) return
  
  // 创建预览模态窗口
  const modal = document.createElement('div')
  modal.className = 'image-preview-modal'
  modal.innerHTML = `
    <div class="modal-backdrop">
      <div class="modal-content">
        <button class="close-btn">&times;</button>
        <img src="${file.url}" alt="${file.fileName}" class="preview-img" />
        <div class="image-info">
          <p class="image-name">${file.fileName}</p>
          <p class="image-size">${formatFileSize(file.fileSize)}</p>
        </div>
      </div>
    </div>
  `
  
  // 添加样式
  const style = document.createElement('style')
  style.textContent = `
    .image-preview-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .modal-backdrop {
      background: rgba(0, 0, 0, 0.8);
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }
    .modal-content {
      background: white;
      border-radius: 8px;
      padding: 20px;
      max-width: 90vw;
      max-height: 90vh;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .close-btn {
      position: absolute;
      top: 10px;
      right: 15px;
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }
    .preview-img {
      max-width: 100%;
      max-height: 70vh;
      object-fit: contain;
      border-radius: 4px;
    }
    .image-info {
      margin-top: 15px;
      text-align: center;
    }
    .image-name {
      font-weight: bold;
      margin: 0 0 5px 0;
    }
    .image-size {
      color: #666;
      font-size: 14px;
      margin: 0;
    }
  `
  
  document.head.appendChild(style)
  document.body.appendChild(modal)
  
  // 点击关闭
  const closeModal = () => {
    document.body.removeChild(modal)
    document.head.removeChild(style)
  }
  
  modal.querySelector('.close-btn')?.addEventListener('click', closeModal)
  modal.querySelector('.modal-backdrop')?.addEventListener('click', (e) => {
    if (e.target === e.currentTarget) closeModal()
  })
  
  // ESC键关闭
  const handleKeydown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      closeModal()
      document.removeEventListener('keydown', handleKeydown)
    }
  }
  document.addEventListener('keydown', handleKeydown)
}

// 清理标识符的函数 - 简化版（只处理卡片）
const cleanIdentifiers = (text: string): string => {
  return text
    // 清理完整的卡片标识符
    .replace(/\[lark-app-card\]/g, '')
    .replace(/\[lark-app-card-end\]/g, '')
    // 清理可能的部分卡片标识符（防止流式输出时的不完整标识符）
    .replace(/\[lark-app-car[d]?$/g, '')
    .replace(/\[lark-app-card-en[d]?$/g, '')
    // 清理任何以 [lark-app-card 开头的不完整标识符
    .replace(/\[lark-app-card[^\]]*$/g, '')
    // 清理只包含卡片标识符的空行
    .replace(/^\s*\[lark-app-card[^\]]*\]\s*$/gm, '')
    // 清理多余的空行
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    .trim()
}

// 处理流式内容，在流式输出时临时清理可能出现的部分标识符
const processedContent = computed(() => {
  if (!props.content) return ''
  
  if (props.isStreaming) {
    // 流式输出时，可能会有不完整的卡片标识符，我们需要特殊处理
    let content = props.content
    
    // 移除完整但没有对应结束标识符的开始标识符
    content = content.replace(/\[lark-app-card\]([^[]*?)(?!\[lark-app-card-end\])/g, '$1')
    
    // 清理所有卡片标识符
    content = cleanIdentifiers(content)
    
    return content
  }
  
  return props.content
})

// 解析消息内容
const parsedSegments = computed<MessageSegment[]>(() => {
  const content = processedContent.value
  if (!content) return []

  // 检查内容是否只包含标识符或空白字符
  const cleanedForCheck = cleanIdentifiers(content)
  if (!cleanedForCheck.trim()) {
    // 如果清理后没有实际内容，返回空数组（不显示任何内容）
    return []
  }

  // 如果是流式输出，直接作为markdown处理，避免复杂的解析
  if (props.isStreaming) {
    return [{
      type: 'markdown',
      content: cleanedForCheck
    }]
  }
  
  // 解析函数：处理卡片标识符和链接
  const parseContent = (inputContent: string): MessageSegment[] => {
    const result: MessageSegment[] = []
    let current = inputContent

    while (current.length > 0) {
      // 查找卡片标识符
      const cardStartIndex = current.indexOf('[lark-app-card]')

      if (cardStartIndex === -1) {
        // 没有更多卡片标识符，处理剩余内容（包括链接）
        const cleanedContent = cleanIdentifiers(current).trim()
        if (cleanedContent) {
          // 解析文本中的链接
          const textSegments = parseTextWithLinks(cleanedContent)
          for (const segment of textSegments) {
            if (segment.type === 'link' && shouldShowPreview(segment.url!)) {
              // 添加链接预览
              result.push({
                type: 'link',
                content: segment.content,
                url: segment.url
              })
            } else {
              // 添加为markdown内容
              result.push({
                type: 'markdown',
                content: segment.content
              })
            }
          }
        }
        break
      }
      
      // 添加卡片标识符前的内容
      if (cardStartIndex > 0) {
        const beforeText = current.substring(0, cardStartIndex)
        const cleanedBeforeText = cleanIdentifiers(beforeText).trim()
        if (cleanedBeforeText) {
          // 解析文本中的链接
          const textSegments = parseTextWithLinks(cleanedBeforeText)
          for (const segment of textSegments) {
            if (segment.type === 'link' && shouldShowPreview(segment.url!)) {
              // 添加链接预览
              result.push({
                type: 'link',
                content: segment.content,
                url: segment.url
              })
            } else {
              // 添加为markdown内容
              result.push({
                type: 'markdown',
                content: segment.content
              })
            }
          }
        }
      }
      
      // 处理卡片标识符
      const cardEndIndex = current.indexOf('[lark-app-card-end]', cardStartIndex)
      if (cardEndIndex !== -1) {
        const cardContent = current.substring(
          cardStartIndex + '[lark-app-card]'.length,
          cardEndIndex
        ).trim()
        
        if (cardContent) {
          result.push({
            type: 'card',
            content: cardContent
          })
        }
        
        current = current.substring(cardEndIndex + '[lark-app-card-end]'.length)
      } else {
        // 没有找到结束标识符，将剩余内容处理（包括链接）
        const cleanedContent = cleanIdentifiers(current).trim()
        if (cleanedContent) {
          // 解析文本中的链接
          const textSegments = parseTextWithLinks(cleanedContent)
          for (const segment of textSegments) {
            if (segment.type === 'link' && shouldShowPreview(segment.url!)) {
              // 添加链接预览
              result.push({
                type: 'link',
                content: segment.content,
                url: segment.url
              })
            } else {
              // 添加为markdown内容
              result.push({
                type: 'markdown',
                content: segment.content
              })
            }
          }
        }
        break
      }
    }
    
    return result
  }
  
  return parseContent(content)
})

// 渲染文本内容
const renderMarkdown = (content: string): string => {
  try {
    return renderText(content)
  } catch (error) {
    console.error('文本渲染错误:', error)
    // 如果渲染失败，至少做基本的HTML转义
    return content
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/\n/g, '<br>')
  }
}
</script>

<style scoped lang="scss">
.message-renderer {
  line-height: 1.6;
  word-break: break-word;
  transition: opacity 0.15s ease-in-out;

  // 为流式内容添加平滑过渡效果
  .content-section {
    transition: all 0.1s ease-in-out;
  }
  
  // 根据消息类型添加不同的样式
  &[data-message-type="file"] {
    .files-section {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 8px;
      padding: 12px;
      border: 1px solid #dee2e6;
    }
  }
  
  &[data-message-type="image"] {
    .files-section {
      background: linear-gradient(135deg, #fff5f5 0%, #ffeaa7 100%);
      border-radius: 8px;
      padding: 12px;
      border: 1px solid #ffd93d;
    }
  }
  
  &[data-message-type="mixed"] {
    .files-section {
      background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
      border-radius: 8px;
      padding: 12px;
      border: 1px solid #b3d9ff;
    }
  }
  
  // 文件区域样式
  .files-section {
    margin-bottom: 12px;
    
    .file-item {
      margin-bottom: 8px;
      
      // 图片文件样式
      .image-file {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 12px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        
        .image-preview {
          position: relative;
          max-width: 200px;
          max-height: 200px;
          border-radius: 6px;
          overflow: hidden;
          background: #ffffff;
          border: 1px solid #dee2e6;
          cursor: pointer;
          transition: all 0.2s ease;
          
          &:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            
            .image-overlay {
              opacity: 1;
            }
          }
          
          .preview-image {
            width: 100%;
            height: auto;
            max-height: 200px;
            object-fit: cover;
            display: block;
          }
          
          .image-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100px;
            height: 100px;
            color: #6c757d;
          }
          
          .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            opacity: 0;
            transition: opacity 0.2s ease;
          }
        }
        
        .file-info {
          display: flex;
          flex-direction: column;
          gap: 2px;
          
          .file-name {
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
            word-break: break-all;
          }
          
          .file-size {
            font-size: 12px;
            color: #6c757d;
          }
        }
      }
      
      // 普通文件样式
      .regular-file {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: #ffffff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        transition: all 0.2s ease;
        
        &:hover {
          background: #f8f9fa;
          border-color: #dee2e6;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .file-icon {
          flex-shrink: 0;
          color: #6c757d;
        }
        
        .file-details {
          flex: 1;
          min-width: 0;
          
          .file-name {
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
            word-break: break-all;
            margin-bottom: 2px;
          }
          
          .file-meta {
            display: flex;
            gap: 8px;
            font-size: 12px;
            color: #6c757d;
            
            .file-size, .file-type {
              white-space: nowrap;
            }
          }
        }
        
        .file-actions {
          flex-shrink: 0;
          
          .download-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: var(--theme-color, #8957e5);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            
            &:hover {
              background: var(--theme-color-dark, #7146d1);
              transform: scale(1.05);
            }
            
            &:active {
              transform: scale(0.95);
            }
          }
        }
      }
    }
  }
  
  // 链接容器样式
  .link-container {
    margin: 8px 0;
  }

  // 卡片容器样式
  .card-container {
    margin: 12px 0;
    
    .card-content {
      background: #ffffff;
      border: 2px solid #e6e1f4;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transition: all 0.2s ease;
      
      &:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
      }
      
      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background: linear-gradient(135deg, #f8f6fd 0%, #f0f8ff 100%);
        border-bottom: 1px solid #e6e1f4;
        
        .card-title {
          font-weight: 600;
          color: #2c3e50;
          font-size: 14px;
        }
      }
      
      .card-body {
        padding: 16px;
        
        // 嵌套的MessageRenderer不再有边距
        .message-renderer {
          margin: 0;
        }
      }
    }
  }
  
  // Markdown容器样式
  .markdown-container {
    .markdown-content {
      color: #2c3e50;
      
      // 标题样式
      :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
        margin: 16px 0 8px 0;
        font-weight: 600;
        color: #1a1a1a;
      }
      
      :deep(h1) { font-size: 1.5em; }
      :deep(h2) { font-size: 1.3em; }
      :deep(h3) { font-size: 1.1em; }
      :deep(h4) { font-size: 1em; }
      
      // 段落样式
      :deep(p) {
        margin: 4px 0;
        line-height: 1.6;
      }
      
      // 列表样式
      :deep(ul), :deep(ol) {
        margin: 4px 0;
        padding-left: 20px;
      }

      :deep(li) {
        margin: 2px 0;
        line-height: 1.5;
      }
      
      // 代码样式
      :deep(code) {
        background: #f5f5f5;
        color: #d14;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
      }
      
      :deep(pre) {
        background: #f8f8f8;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 12px;
        overflow-x: auto;
        margin: 8px 0;
        
        code {
          background: none;
          color: #333;
          padding: 0;
        }
      }
      
      // 引用样式
      :deep(blockquote) {
        border-left: 4px solid var(--theme-color, #8957e5);
        margin: 8px 0;
        padding-left: 12px;
        color: #666;
        font-style: italic;
      }
      
      // 链接样式 - 增强markdown链接显示
      :deep(a) {
        color: var(--theme-color, #8957e5);
        text-decoration: none;
        font-weight: 500;
        padding: 2px 4px;
        border-radius: 4px;
        transition: all 0.2s ease;
        
        &:hover {
          background: rgba(137, 87, 229, 0.1);
          text-decoration: underline;
          transform: translateY(-1px);
        }
        
        &:active {
          transform: translateY(0);
        }
      }
      
      // 表格样式
      :deep(table) {
        width: 100%;
        border-collapse: collapse;
        margin: 8px 0;
        
        th, td {
          border: 1px solid #e0e0e0;
          padding: 8px 12px;
          text-align: left;
        }
        
        th {
          background: #f5f5f5;
          font-weight: 600;
        }
      }
      
      // 分隔线样式
      :deep(hr) {
        border: none;
        border-top: 1px solid #e0e0e0;
        margin: 16px 0;
      }
      
      // 强调样式
      :deep(strong) {
        font-weight: 600;
        color: #1a1a1a;
      }
      
      :deep(em) {
        font-style: italic;
        color: #666;
      }
    }
  }
}
</style>
