<template>
  <div class="file-upload-component">
    <div class="file-upload-wrapper">
      <input
        ref="fileInput"
        type="file"
        class="file-input"
        :accept="accept"
        :multiple="multiple"
        @change="handleFileChange"
        style="display: none;"
      />
      
      <button
        @click="triggerFileSelect"
        class="file-upload-btn"
        :disabled="uploading"
      >
        <Icon 
          :icon="uploading ? 'mdi:loading' : 'mdi:upload'" 
          width="16" 
          height="16" 
          :class="{ 'animate-spin': uploading }"
        />
        {{ uploading ? '上传中...' : '选择文件' }}
      </button>
      
      <!-- 选择的文件列表 -->
      <div v-if="selectedFiles.length > 0 && !uploading" class="file-list">
        <div v-for="(file, index) in selectedFiles" :key="index" class="file-item">
          <span class="file-name">{{ file.name }}</span>
          <button @click="removeFile(index)" class="remove-btn">
            ×
          </button>
        </div>
      </div>
      
      <div v-if="!selectedFiles.length && placeholder" class="file-placeholder">
        {{ placeholder }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps } from 'vue'
import { Icon } from '@iconify/vue'
import { getFilePresignedUrl, createFile } from '@/api/infra/file'

interface FileUploadProps {
  /** 接受的文件类型 */
  accept?: string
  /** 是否支持多文件 */
  multiple?: boolean
  /** 占位符文本 */
  placeholder?: string
  /** 最大文件大小（字节） */
  maxSize?: number
  /** 最大文件数量 */
  maxFiles?: number
}

interface FileUploadEmits {
  /** 文件选择事件 */
  (e: 'file-selected', files: File[]): void
  /** 文件上传事件 */
  (e: 'file-uploaded', result: any): void
  /** 错误事件 */
  (e: 'error', error: string): void
}

const props = withDefaults(defineProps<FileUploadProps>(), {
  accept: '*',
  multiple: false,
  placeholder: '未选择文件',
  maxSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 5
})

const emit = defineEmits<FileUploadEmits>()

const fileInput = ref<HTMLInputElement>()
const selectedFiles = ref<File[]>([])
const uploading = ref(false)
const uploadedFiles = ref<any[]>([])
const isUploadCompleted = ref(false)

const triggerFileSelect = () => {
  fileInput.value?.click()
}

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = Array.from(target.files || [])
  
  if (files.length === 0) return
  
  // 验证文件
  const validFiles = validateFiles(files)
  if (validFiles.length === 0) return
  
  if (props.multiple) {
    // 多文件模式：添加到现有文件列表
    const totalFiles = selectedFiles.value.length + validFiles.length
    if (totalFiles > props.maxFiles) {
      emit('error', `最多只能选择 ${props.maxFiles} 个文件`)
      return
    }
    selectedFiles.value.push(...validFiles)
  } else {
    // 单文件模式：替换现有文件
    selectedFiles.value = [validFiles[0]]
  }
  
  emit('file-selected', selectedFiles.value)
  
  // 清空input值，允许重复选择同一文件
  if (target) {
    target.value = ''
  }
}

const validateFiles = (files: File[]): File[] => {
  const validFiles: File[] = []

  for (const file of files) {
    // 检查文件类型不能为空
    if (!file.type) {
      emit('error', `文件 "${file.name}" 类型不能为空，请选择有效的文件`)
      continue
    }

    // 检查文件大小
    if (file.size > props.maxSize) {
      emit('error', `文件 "${file.name}" 超过最大大小限制 ${formatFileSize(props.maxSize)}`)
      continue
    }

    // 检查文件大小不能为0
    if (file.size === 0) {
      emit('error', `文件 "${file.name}" 为空文件，请选择有效的文件`)
      continue
    }

    // 检查文件类型（如果指定了accept）
    if (props.accept !== '*' && !isFileTypeAccepted(file, props.accept)) {
      emit('error', `文件 "${file.name}" 类型不被支持，支持的类型：${props.accept}`)
      continue
    }

    validFiles.push(file)
  }

  return validFiles
}

const isFileTypeAccepted = (file: File, accept: string): boolean => {
  const acceptTypes = accept.split(',').map(type => type.trim())
  
  for (const acceptType of acceptTypes) {
    if (acceptType.startsWith('.')) {
      // 扩展名匹配
      if (file.name.toLowerCase().endsWith(acceptType.toLowerCase())) {
        return true
      }
    } else if (acceptType.includes('/')) {
      // MIME类型匹配
      if (file.type === acceptType || file.type.startsWith(acceptType.replace('*', ''))) {
        return true
      }
    }
  }
  
  return false
}

const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
  emit('file-selected', selectedFiles.value)
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const getFileIcon = (mimeType: string): string => {
  if (mimeType.startsWith('image/')) return 'mdi:file-image'
  if (mimeType.startsWith('video/')) return 'mdi:file-video'
  if (mimeType.startsWith('audio/')) return 'mdi:file-music'
  if (mimeType.includes('pdf')) return 'mdi:file-pdf-box'
  if (mimeType.includes('word') || mimeType.includes('document')) return 'mdi:file-word'
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'mdi:file-excel'
  if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'mdi:file-powerpoint'
  if (mimeType.includes('text/')) return 'mdi:file-document'
  return 'mdi:file'
}

const getFileIconFromType = (type: string): string => {
  switch (type) {
    case 'image': return 'mdi:file-image'
    case 'video': return 'mdi:file-video'
    case 'audio': return 'mdi:file-music'
    case 'document': return 'mdi:file-document'
    default: return 'mdi:file'
  }
}

// 生成文件路径
const generateFilePath = (file: File): string => {
  const timestamp = Date.now()
  const randomStr = Math.random().toString(36).substring(2, 8)
  const fileExtension = file.name.split('.').pop() || ''
  return `ai-chat/${timestamp}_${randomStr}.${fileExtension}`
}

// 文件上传函数 - 使用预签名URL上传
const uploadFiles = async (files: File[]): Promise<any[]> => {
  if (files.length === 0) return []

  console.log(`[FileUpload] 开始预签名URL上传，文件数量: ${files.length}`)
  console.log(`[FileUpload] 文件列表:`, files.map(f => ({ name: f.name, size: f.size, type: f.type })))

  uploading.value = true
  const uploadResults: any[] = []

  try {
    for (const file of files) {
      try {
        console.log(`[FileUpload] 开始上传文件: ${file.name}`)

        // 1. 生成文件路径
        const filePath = generateFilePath(file)

        // 2. 获取预签名URL
        const presignedResponse = await getFilePresignedUrl(filePath)
        if (!presignedResponse.data) {
          throw new Error(`获取预签名URL失败: ${file.name}`)
        }

        const presignedData = presignedResponse.data as any
        const { uploadUrl, url: fileUrl, configId } = presignedData

        // 3. 直接上传到S3存储
        const uploadResponse = await fetch(uploadUrl, {
          method: 'PUT',
          body: file,
          headers: {
            'Content-Type': file.type || 'application/octet-stream'
          }
        })

        if (!uploadResponse.ok) {
          throw new Error(`文件上传失败: ${uploadResponse.statusText}`)
        }

        // 4. 通知后端文件上传完成（创建文件记录）
        const createFileData = {
          configId,
          path: filePath,
          name: file.name,
          type: file.type || 'application/octet-stream',
          size: file.size,
          url: fileUrl
        }

        const createFileResponse = await createFile(createFileData)

        // 根据文件类型确定type字段
        const getFileType = (mimeType: string): string => {
          if (mimeType.startsWith('image/')) return 'image'
          if (mimeType.startsWith('video/')) return 'video'
          if (mimeType.startsWith('audio/')) return 'audio'
          return 'document'
        }

        const uploadResult = {
          // 后端FileInfo格式
          type: getFileType(file.type || 'application/octet-stream'),
          transferMethod: 'remote_url',
          url: fileUrl,
          // 保留原有字段用于前端显示
          fileId: createFileResponse.data?.id || Date.now(),
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type || 'application/octet-stream'
        }

        uploadResults.push(uploadResult)

        console.log(`[FileUpload] 文件 "${file.name}" 上传成功，最终结果:`, uploadResult)

      } catch (uploadError: any) {
        console.error(`[FileUpload] 文件 "${file.name}" 上传失败:`, uploadError)
        throw uploadError
      }
    }

    console.log(`[FileUpload] 所有文件上传完成，总结果:`, uploadResults)
    
    // 上传完成后清空选择的文件，保持按钮可见
    selectedFiles.value = []
    uploadedFiles.value = uploadResults
    
    return uploadResults
  } catch (error) {
    console.error(`[FileUpload] 上传过程中发生错误:`, error)
    emit('error', (error as Error).message)
    throw error
  } finally {
    uploading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  selectedFiles,
  uploading,
  uploadedFiles,
  isUploadCompleted,
  uploadFiles,
  clearFiles: () => {
    selectedFiles.value = []
    uploadedFiles.value = []
    uploading.value = false
    emit('file-selected', [])
  },
  // 添加重置所有状态的方法
  resetAll: () => {
    selectedFiles.value = []
    uploadedFiles.value = []
    uploading.value = false
    if (fileInput.value) {
      fileInput.value.value = ''
    }
    emit('file-selected', [])
  }
})
</script>

<style scoped lang="scss">
.file-upload-component {
  .file-upload-wrapper {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .file-upload-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 6px 12px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      align-self: flex-start;

      &:hover:not(:disabled) {
        background: #0056b3;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .animate-spin {
        animation: spin 1s linear infinite;
      }
    }

    .file-list {
      .file-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 4px 8px;
        background: #f8f9fa;
        border-radius: 4px;
        font-size: 12px;
        margin-bottom: 4px;

        .file-name {
          color: #495057;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
        }

        .remove-btn {
          background: none;
          border: none;
          color: #dc3545;
          cursor: pointer;
          font-size: 16px;
          font-weight: bold;
          padding: 0;
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          
          &:hover {
            color: #c82333;
          }
        }
      }
    }

    .file-placeholder {
      color: #6c757d;
      font-size: 12px;
      padding: 4px 0;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
