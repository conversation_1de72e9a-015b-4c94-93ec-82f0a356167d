"use strict";

const Services = require("ee-core/services");
const knex = require("../../database/knex");

/**
 * 个人/群组消息 数据存储
 * @class
 */
class MessageService {
  constructor(tableName) {
    this.tableName = tableName;
  }

  /**
   * 获取当前用户userid
   */
  getUserId() {
    return Services.get("database.jsondb").getUserId();
  }

  /**
   * 获取聊天记录列表
   * @param {string} content - JSON对象 { pageNo, pageSize, sender, receiver }
   */
  async getMessageList(params) {
    if (!params) return [];

    const userId = await this.getUserId();
    const { pageNo, pageSize, sender, receiver } = JSON.parse(params);
    const offset = (pageNo - 1) * pageSize;

    const queryBuilder = knex(this.tableName)
      .where({ userId })
      .andWhere(function () {
        this.where({ sender, receiver }).orWhere({
          sender: receiver,
          receiver: sender,
        });
      });

    const rows = await queryBuilder
      .clone()
      .select()
      .orderByRaw("createTime DESC")
      .limit(pageSize)
      .offset(offset);
    const [{ total }] = await queryBuilder.clone().count({ total: "id" });

    return {
      list: rows.map((o) => JSON.parse(o.content)),
      total,
    };
  }

  /**
   * 获取聊天记录列表
   * @param {string} content - JSON对象 { pageNo, pageSize, sender, receiver }
   */
  async getMessageList2(params) {
    if (!params) return [];

    const userId = await this.getUserId();
    const { pageNo, pageSize, sender, receiver } = JSON.parse(params);
    const offset = (pageNo - 1) * pageSize;

    const queryBuilder = knex(this.tableName).where({ userId, receiver });

    const rows = await queryBuilder
      .clone()
      .select()
      .orderByRaw("createTime DESC")
      .limit(pageSize)
      .offset(offset);

    const [{ total }] = await queryBuilder.clone().count({ total: "id" });

    return {
      list: rows.map((o) => JSON.parse(o.content)),
      total,
    };
  }

  /**
   * 保存聊天消息
   * @param {string} content - JSON对象
   */
  async addMessage(content) {
    if (!content) return;

    const userId = await this.getUserId();
    const { id, sender, receiver, createTime } = JSON.parse(content);

    // 检查是否存在
    const existing = await knex(this.tableName).where({ userId, id }).first();
    if (existing) return;

    // 插入接收的消息记录
    return knex(this.tableName).insert({
      userId,
      id,
      sender,
      receiver,
      createTime,
      content,
    });
  }

  /**
   * 断线重发更新本地消息
   * @param {string} params - JSON对象 { id, content }
   */
  updateStatus(params) {
    if (!params) return;

    const { id, content } = JSON.parse(params);
    return knex(this.tableName)
      .where({ id })
      .update({
        id: content.id,
        createTime: content.createTime,
        content: JSON.stringify(content),
      });
  }

  /**
   * 更新聊天消息
   * @param {string} content - JSON对象
   */
  async updateMessage(content) {
    if (!content) return;

    const userId = await this.getUserId();
    const { id, createTime } = JSON.parse(content);

    return knex(this.tableName).where({ userId, id }).update({
      createTime,
      content,
    });
  }

  /**
   * 删除聊天消息
   */
  async removeMessage(id) {
    if (!id) return;

    const userId = await this.getUserId();
    return knex(this.tableName).where({ userId, id }).del();
  }

  /**
   * 清空超过一个月的聊天消息
   */
  async clearExpiredMessage() {
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    await knex(this.tableName)
      .where("createTime", "<", oneMonthAgo.getTime())
      .del();
  }

  /**
   * 清空全部消息
   */
  async clearAllMessage() {
    const userId = await this.getUserId();
    return knex(this.tableName).where({ userId }).del();
  }
}

module.exports = MessageService;
