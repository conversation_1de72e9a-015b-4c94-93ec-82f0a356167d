<template>
  <div class="home-container">
    <!-- 待办事项添加对话框 -->
    <TodoAddDialog ref="todoAddDialogRef" @todo-added="handleTodoAdded" />
    <!-- 背景渐变色块 -->
    <div class="background-gradient">
      <div class="gradient-blob gradient-blob-1"></div>
      <div class="gradient-blob gradient-blob-2"></div>
      <div class="gradient-blob gradient-blob-3"></div>
      <div class="gradient-blob gradient-blob-4"></div>
      <div class="gradient-blob gradient-blob-5"></div>
    </div>

    <!-- 测试黑暗主题的元素 -->
    <div class="hidden dark:block fixed top-4 right-4 z-50 bg-green-500 text-white px-2 py-1 rounded text-xs">黑暗模式已启用</div>
    <div class="block dark:hidden fixed top-4 right-4 z-50 bg-blue-500 text-white px-2 py-1 rounded text-xs">浅色模式已启用</div>

    <!-- macOS风格的小组件网格布局 -->
    <div class="widget-grid max-w-7xl mx-auto h-full">
      <!-- 第一行：统计、时钟、闪记、待办 -->
      <div class="stats-area glass-card">
        <DataStatsWidget />
      </div>

      <div class="clock-area glass-card">
        <ClockWidget />
      </div>
      
      <div class="note-area glass-card">
        <QuickNoteWidget />
      </div>

      <div class="todo-area glass-card">
        <TodoListWidget ref="todoListWidgetRef" />
      </div>

      <!-- 第二行：AI通知和AI微应用 -->
      <div class="ai-summary-area glass-card">
        <AiSummaryWidget />
      </div>

      <div class="ai-app-area glass-card">
        <AiAppWidget />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="home">
import { defineAsyncComponent, h, ref, provide } from 'vue'
import TodoAddDialog from '@/components/dialogs/TodoAddDialog.vue'

// 对话框引用
const todoAddDialogRef = ref<{ open: () => void }>()
const todoListWidgetRef = ref<{ loadTodos: () => void }>()

// 创建加载组件
const LoadingComponent = {
  render() {
    return h(
      "div",
      {
        class:
          "flex items-center justify-center h-full bg-white/10 dark:bg-gray-800/10 rounded-xl border border-white/20 dark:border-white/10"
      },
      [
        h("div", {
          class: "animate-spin w-6 h-6 border-2 border-white/30 border-t-white rounded-full"
        })
      ]
    );
  }
};

// 创建错误组件
const ErrorComponent = {
  render() {
    return h(
      "div",
      {
        class: "flex items-center justify-center h-full bg-red-500/10 dark:bg-red-900/20 rounded-xl border border-red-500/20"
      },
      [
        h(
          "div",
          {
            class: "text-center text-red-600 dark:text-red-400"
          },
          [h("div", { class: "text-sm" }, "组件加载失败"), h("div", { class: "text-xs mt-1 opacity-70" }, "请刷新页面重试")]
        )
      ]
    );
  }
};

// 异步加载组件，提升首屏加载性能 - 优化加载参数
const ClockWidget = defineAsyncComponent({
  loader: () => import("./widget/ClockWidget.vue"),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 100, // 减少延迟时间
  timeout: 5000 // 增加超时时间
});

const QuickNoteWidget = defineAsyncComponent({
  loader: () => import("./widget/QuickNoteWidget.vue"),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 100, // 减少延迟时间
  timeout: 5000 // 增加超时时间
});

const TodoListWidget = defineAsyncComponent({
  loader: () => import("./widget/TodoListWidget.vue"),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 100, // 减少延迟时间
  timeout: 5000 // 增加超时时间
});

const AiSummaryWidget = defineAsyncComponent({
  loader: () => import("./widget/AiSummaryWidget.vue"),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 100, // 减少延迟时间
  timeout: 5000 // 增加超时时间
});

const AiAppWidget = defineAsyncComponent({
  loader: () => import("./widget/AiAppWidget.vue"),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 100, // 减少延迟时间
  timeout: 5000 // 增加超时时间
});

const DataStatsWidget = defineAsyncComponent({
  loader: () => import("./widget/DataStatsWidget.vue"),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 100,
  timeout: 5000
})

// 处理待办添加对话框事件
const handleOpenTodoDialog = () => {
  todoAddDialogRef.value?.open()
}

// 处理待办添加成功事件
const handleTodoAdded = () => {
  // 刷新待办列表
  todoListWidgetRef.value?.loadTodos()
}

// 提供方法给子组件使用
provide('openTodoDialog', handleOpenTodoDialog)

// 暴露方法给子组件调用
defineExpose({
  openTodoDialog: handleOpenTodoDialog
})
</script>

<style scoped lang="scss">
// 主容器样式
.home-container {
  position: relative;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 16px;
  background: #ffffff;
  box-sizing: border-box;

  // 暗色模式背景
  &:global(.dark) {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }
}

// 背景渐变色块 - 性能优化
.background-gradient {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.gradient-blob {
  position: absolute;
  border-radius: 50%;
  // 移除昂贵的模糊滤镜，这是主要的性能瓶颈
  // filter: blur(60px);
  opacity: 0.5;
  will-change: transform;
  transition: all 0.8s ease-in-out;

  &.gradient-blob-1 {
    width: 650px;
    height: 450px;
    // 使用平滑的径向渐变来模拟柔和的光晕效果，而不是使用高成本的 blur 滤镜
    background: radial-gradient(ellipse at center, rgba(37, 99, 235, 0.7) 0%, transparent 70%); // 明艳深蓝色
    top: 30%;
    left: 35%;
    transform: translate(-50%, -50%);
    animation: pulse 25s infinite alternate;
  }

  &.gradient-blob-2 {
    width: 550px;
    height: 400px;
    background: radial-gradient(ellipse at center, rgba(147, 51, 234, 0.65) 0%, transparent 70%); // 明艳紫色
    top: 40%;
    left: 60%;
    transform: translate(-50%, -50%);
    animation: pulse 22s infinite alternate-reverse;
  }

  &.gradient-blob-3 {
    width: 600px;
    height: 420px;
    background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.6) 0%, transparent 70%); // 明艳中蓝色
    top: 60%;
    left: 40%;
    transform: translate(-50%, -50%);
    animation: pulse 28s infinite alternate;
  }

  &.gradient-blob-4 {
    width: 480px;
    height: 350px;
    background: radial-gradient(ellipse at center, rgba(168, 85, 247, 0.55) 0%, transparent 70%); // 明艳亮紫色
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 30s infinite alternate-reverse;
  }

  &.gradient-blob-5 {
    width: 520px;
    height: 380px;
    background: radial-gradient(ellipse at center, rgba(29, 78, 216, 0.55) 0%, transparent 70%); // 明艳深蓝色
    top: 55%;
    left: 65%;
    transform: translate(-50%, -50%);
    animation: pulse 26s infinite alternate;
  }
}

// 暗色模式下的渐变色块 - 科技感效果
:global(.dark) .gradient-blob {
  opacity: 0.4;
  // 移除了针对每个 blob 的背景覆盖，因为基础渐变已经足够，
  // 仅调整全局不透明度即可，更简洁高效。
}

// 添加呼吸动画效果
@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}

// 移除浮动动画，优化性能

// 高性能静态卡片样式 - 普通阴影边框
.glass-card {
  background: rgba(255, 255, 255, 0.65);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  // 保持较小的阴影范围，但增加不透明度来加深阴影，获得更好的视觉效果
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  position: relative;
  overflow: visible;
  color: #1e293b;

  // 确保圆角在所有浏览器中正确显示
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;

  // 添加背景裁剪以确保圆角效果
  background-clip: padding-box;
}

// 暗色模式下的普通阴影边框卡片
:global(.dark) .glass-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  // 同样加深暗色模式下的阴影
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.35);
  color: #f1f5f9;

  // 确保暗色模式下圆角效果一致
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  background-clip: padding-box;
}

// 精确的网格布局 - 针对最小尺寸优化
.widget-grid {
  position: relative;
  z-index: 1;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: repeat(2, minmax(0, 1fr));
  gap: 20px;
  padding: 16px;
  align-items: stretch;
  justify-items: stretch;
  overflow: visible;

  // 优化后的入场动画 - 使用transform和opacity，避免layout重排
  animation: fadeInUp 0.25s ease-out;
  will-change: transform, opacity;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 第一行：统计、时钟、闪记、待办
.stats-area {
  grid-column: 1 / 2;
  grid-row: 1 / 2;
  animation: fadeInUp 0.25s ease-out 0.02s both;
  will-change: transform, opacity;
}

.clock-area {
  grid-column: 2 / 3;
  grid-row: 1 / 2;
  animation: fadeInUp 0.25s ease-out 0.03s both;
  will-change: transform, opacity;
}

.note-area {
  grid-column: 3 / 4;
  grid-row: 1 / 2;
  animation: fadeInUp 0.25s ease-out 0.04s both;
  will-change: transform, opacity;
}

.todo-area {
  grid-column: 4 / 5;
  grid-row: 1 / 2;
  animation: fadeInUp 0.25s ease-out 0.06s both;
  will-change: transform, opacity;
}

// 第二行：AI通知和AI微应用
.ai-summary-area {
  grid-column: 1 / 3;
  grid-row: 2 / 3;
  animation: fadeInUp 0.25s ease-out 0.08s both;
  will-change: transform, opacity;
}

.ai-app-area {
  grid-column: 3 / 5;
  grid-row: 2 / 3;
  animation: fadeInUp 0.25s ease-out 0.1s both;
  will-change: transform, opacity;
}

// 响应式布局 - 针对小于最小宽度的情况
@media (max-width: 1200px) {
  .widget-grid {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: repeat(4, minmax(0, 1fr));
    gap: 16px;
  }

  .clock-area {
    grid-column: 1 / 2;
    grid-row: 1 / 2;
  }

  .stats-area {
    grid-column: 2 / 3;
    grid-row: 1 / 2;
  }

  .note-area {
    grid-column: 1 / 2;
    grid-row: 2 / 3;
  }

  .todo-area {
    grid-column: 2 / 3;
    grid-row: 2 / 3;
  }

  .ai-summary-area {
    grid-column: 1 / 3;
    grid-row: 3 / 4;
  }

  .ai-app-area {
    grid-column: 1 / 3;
    grid-row: 4 / 5;
  }
}

// 超小屏幕布局
@media (max-width: 768px) {
  .widget-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(6, minmax(0, 1fr));
    gap: 12px;
  }

  .clock-area {
    grid-column: 1 / 2;
    grid-row: 1 / 2;
  }

  .stats-area {
    grid-column: 1 / 2;
    grid-row: 2 / 3;
  }

  .note-area {
    grid-column: 1 / 2;
    grid-row: 3 / 4;
  }

  .todo-area {
    grid-column: 1 / 2;
    grid-row: 4 / 5;
  }

  .ai-summary-area {
    grid-column: 1 / 2;
    grid-row: 5 / 6;
  }

  .ai-app-area {
    grid-column: 1 / 2;
    grid-row: 6 / 7;
  }
}

// 全局卡片样式已移至各个组件内部
</style>
