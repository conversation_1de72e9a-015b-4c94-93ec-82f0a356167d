<template>
  <div class="w-full h-[100vh]" @mousedown="mousedown">
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
import { ipc } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";

const props = defineProps<{
  name: "ballWin" | "dialogWin" | "dialogWin2";
}>();

let isMouseDown = false;
let ismoving = false;
const mousedown = (e: any) => {
  isMouseDown = true;
  let dinatesX = e.x;
  let dinatesY = e.y;

  document.onmousemove = function (ev) {
    if (isMouseDown) {
      ismoving = true;
      const x = ev.screenX - dinatesX;
      const y = ev.screenY - dinatesY;
      // 给主进程传入坐标
      ipc.invoke(ipcApiRoute.moveFloatingWin, { x, y, target: props.name });
    }
  };
  document.onmouseup = () => {
    setTimeout(() => {
      ismoving = false;
      isMouseDown = false;
    }, 10);
  };
};

defineExpose({
  ismoving
});
</script>
