<template>
  <div class="search-drawer">
    <slot :open="openDialog" :eventNum="eventNum"></slot>
    <el-dialog v-model="visible" :show-close="false" class="custom-dialog">
      <template #header>
        <button class="close-btn" @click="closeDialog">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'calendar']" class="icon" />
          <span class="title">日历</span>
        </div>
      </template>
      <el-calendar v-model="today" ref="calendar">
        <template #date-cell="{ data }">
          <div
            :class="`cell ${data.isSelected ? 'selected' : ''}`"
            class="w-full h-full flex flex-col items-center justify-center"
            title="点击查看详情"
            @click="openEvent(data.day)"
          >
            <el-badge
              v-if="data.day == now"
              type="danger"
              :offset="[12, -4]"
              :value="events[data.day]?.filter((item: any) => item.status == '0').length"
              :hidden="events[data.day]?.filter((item: any) => item.status == '0').length == 0"
            >
              <div class="text-center">{{ data.day.split("-").slice(2).join() }}</div>
            </el-badge>
            <el-badge
              v-else
              type="info"
              :offset="[12, -4]"
              :value="events[data.day]?.length"
              :hidden="events[data.day]?.length == 0"
            >
              <div class="text-center">{{ data.day.split("-").slice(2).join() }}</div>
            </el-badge>
          </div>
        </template>
      </el-calendar>
    </el-dialog>
    <el-dialog v-model="eventVisible" :show-close="false" class="custom-dialog">
      <template #header>
        <button class="close-btn" @click="closeEvent">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'calendar']" class="icon" />
          <span class="title">待办事项</span>
        </div>
      </template>
      <el-button plain icon="plus" size="small" type="primary" @click="addRow" v-show="addBtnVisible">新增</el-button>
      <el-table :data="toDoData" style="width: 100%">
        <el-table-column label="时间">
          <template #default="{ row }">
            <el-time-select
              v-if="row.editing"
              v-model="row.times"
              start="08:00"
              step="01:00"
              end="23:00"
              placeholder="时间"
            ></el-time-select>
            <span v-else>{{ row.times }}</span>
          </template>
        </el-table-column>
        <el-table-column label="待办事项">
          <template #default="{ row }">
            <el-input v-if="row.editing" v-model="row.title"></el-input>
            <span v-else>{{ row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column label="详细信息">
          <template #default="{ row }">
            <el-input v-if="row.editing" v-model="row.contents"></el-input>
            <span v-else>{{ row.contents }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template #default="{ row }">
            <span v-if="row.status == '1'">已完成</span>
            <span v-else>
              <span v-if="new Date(row.dateTime + ' ' + row.times) < new Date()">已逾期</span>
              <span v-else>未开始</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ $index, row }">
            <div v-if="!row.editing">
              <el-button link size="small" icon="edit" @click="editRow($index)" />
              <el-button link size="small" icon="delete" @click="delEvent(row)" />
              <el-button link size="small" icon="check" @click="doneEvent(row)"> </el-button>
            </div>
            <div v-else>
              <el-button link size="small" icon="check" :loading="saveLoading" @click="saveRow($index, row)" />
              <el-button link size="small" icon="close" @click="cancelEdit($index)" />
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import dayjs from "dayjs";
import * as eventApi from "@/api/modules/event";
import { ElMessage, ElMessageBox } from "element-plus";
import { useCalendarStore } from "@/stores/modules/calendar";

const calendarStore = useCalendarStore();

const props = defineProps({
  add: Boolean
});

interface Row {
  times: string;
  title: string;
  contents: string;
  status: string;
  editing: boolean;
}
// 日历
const today = ref(new Date());
const now = dayjs(new Date()).format("YYYY-MM-DD");

onMounted(async () => {
  if (Object.keys(calendarStore.events).length === 0 && !props.add) {
    await calendarStore.getEventList();
  }
});

const events = computed(() => calendarStore.events);
const eventNum = computed(() => {
  if (calendarStore.events[now]) {
    const toDo = calendarStore.events[now].filter((item: any) => item.status != "1");
    return toDo.length;
  } else {
    return 0;
  }
});
const saveLoading = ref(false);

const visible = ref(false);
const openDialog = () => {
  calendarStore.getEventList();
  visible.value = true;
};
const closeDialog = () => {
  visible.value = false;
};
defineExpose({ openDialog, closeDialog });

// 待办事项
const eventVisible = ref(false);
const currentDate = ref("");
const toDoData = ref<Row[]>([]);
const addBtnVisible = computed(() => {
  if (currentDate.value == now || dayjs(currentDate.value).isAfter(dayjs(now))) {
    return true;
  }
  return false;
});

const openEvent = (date: string) => {
  currentDate.value = date;
  toDoData.value = calendarStore.events[date] ? [...calendarStore.events[date]] : [];
  eventVisible.value = true;
};

const closeEvent = () => {
  currentDate.value = "";
  eventVisible.value = false;
};

// 更新todoData到store
const syncEventsToStore = () => {
  calendarStore.updateToDoData(
    currentDate.value,
    toDoData.value.filter(o => o.id != null)
  );
};

const addRow = () => {
  toDoData.value.push({ times: "", title: "", contents: "", status: "0", editing: true });
};

const editRow = (index: number) => {
  toDoData.value[index].oldVal = JSON.parse(JSON.stringify(toDoData.value[index]));
  toDoData.value[index].editing = true;
};

const saveRow = async (_index: number, row: any) => {
  if (!row.times) {
    ElMessage.error("请选择时间");
    return;
  }
  if (!row.title) {
    ElMessage.error("请输入待办事项");
    return;
  }
  row.dateTime = currentDate.value;
  saveLoading.value = true;
  try {
    if (row.id) {
      const res: any = await eventApi.updateEvent(row);
      if (res.code === 0) ElMessage.success("修改成功");
      row.editing = false;
    } else {
      const res: any = await eventApi.addEvent(row);
      if (res.code == 0) {
        ElMessage.success("保存成功");
        row.id = res.data;
        row.editing = false;
        row.dateTime = currentDate.value;
      }
    }
    syncEventsToStore();
  } finally {
    saveLoading.value = false;
  }
};

const cancelEdit = (index: number) => {
  const row = toDoData.value[index];
  if (!row.id) {
    // 移除新增项
    toDoData.value.splice(index, 1);
  } else {
    // 还原编辑内容
    row.times = row.oldVal.times;
    row.title = row.oldVal.title;
    row.contents = row.oldVal.contents;
    row.editing = false;
  }
};

const doneEvent = async (item: any) => {
  const params = {
    id: item.id,
    status: 1
  };
  const res: any = await eventApi.updateEvent(params);
  if (res.code == 0) {
    ElMessage.success("已完成");
    item.status = 1;
    syncEventsToStore();
  }
};

const delEvent = (delItem: any) => {
  ElMessageBox.confirm(`确定删除待办事项${delItem.title}`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      const res: any = await eventApi.deleteEvent({ id: delItem.id });
      if (res.code == 0) {
        ElMessage.success("删除成功");
        let index = toDoData.value.findIndex((item: any) => item.id == delItem.id);
        toDoData.value.splice(index, 1);
        syncEventsToStore();
      }
    })
    .catch(() => {});
};
</script>

<style lang="scss" scoped>
.search-drawer {
  :deep(.el-calendar) {
    @apply rounded-xl;

    .el-calendar-day {
      height: 60px;
      @apply flex items-center justify-center p-0 relative;
    }
  }

  .el-button {
    @apply text-base;
  }
}
</style>
