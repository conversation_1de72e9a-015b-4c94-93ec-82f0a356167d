<template>
  <div class="contact space-y-2">
    <div v-if="user?.length == 0">
      <NoData />
    </div>
    <div v-for="item in user as any" :key="item.id" class="group-item flex items-center gap-3 rounded-lg p-2 hover:bg-gray-100">
      <div @click.stop>
        <fileAvatar :type-avatar="getFileExtensionWithDot(item.name)" />
      </div>
      <div class="flex-1 flex flex-col group-info">
        <h4 class="text-sm font-medium text-gray-900" style="word-break: break-all">{{ item.name }}</h4>
        <p class="text-xs text-gray-500">{{ item.type }}</p>
      </div>
      <div class="flex items-center space-x-4" @click.stop>
        <el-tooltip content="打开文件" placement="top">
          <el-button link icon="document" @click="openFile(item.path)"></el-button>
        </el-tooltip>
        <el-tooltip content="在文件夹中显示" placement="top">
          <el-button link icon="folder-opened" @click="openFileLocation(item.path)"></el-button>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="FileContact">
import { onMounted } from "vue";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { ipc } from "@/utils/ipcRenderer";
import fileAvatar from "@/components/Avatar/FileAvatar.vue";
import NoData from "@/components/NoData/index.vue";

defineProps({
  user: {
    type: Array
  }
});

const openFile = (path: any) => {
  ipc.invoke(ipcApiRoute.openDirectory, path);
};
const openFileLocation = (filePath: any) => {
  const path = require("path");
  const folderPath = path.dirname(filePath);
  ipc.invoke(ipcApiRoute.openDirectory, folderPath);
};

const getFileExtensionWithDot = (filename: any) => {
  // 使用 lastIndexOf 找到最后一个点的位置
  const dotIndex = filename.lastIndexOf(".");
  // 如果找到了点并且它不是字符串的第一个字符，返回从该位置到字符串末尾的部分
  if (dotIndex > 0 && dotIndex < filename.length - 1) {
    return filename.substring(dotIndex);
  }
  // 如果没有找到点或者点在字符串的最后一个字符位置，返回空字符串
  return "";
};
onMounted(() => {});
</script>

<style scoped lang="scss">
.el-button {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  @apply text-xl;
}
.book {
  @apply p-2;
}
.dark {
  .group-item {
    @apply hover:bg-gray-700;
  }
  h4 {
    @apply text-white;
  }
  p {
    @apply text-gray-400;
  }
}
</style>
