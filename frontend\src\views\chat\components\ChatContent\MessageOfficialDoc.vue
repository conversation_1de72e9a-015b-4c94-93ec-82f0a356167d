<template>
  <div :class="[isExe ? 'official-box' : 'official-boxs']" class="p-4">
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center space-x-2 mr-2">
        <span
          class="head flex items-center gap-1 font-medium text-purple-700 hover:bg-purple-100 rounded-md px-2 py-1 text-xs bg-purple-50">
          <font-awesome-icon :icon="['fas', 'book']" />
          公文
        </span>
        <div class="ongoing text-xs px-2 py-0.5 rounded font-medium bg-gray-50 text-gray-700"
          :class="{ active: officialDoc.studyStatus }">
          {{ officialDoc.studyStatus ? "已学习" : "未学习" }}
        </div>
      </div>
    </div>
    <h3
      class="title mb-2 text-lg font-semibold text-gray-900 hover:text-blue-400 cursor-pointer transition-colors duration-200">
      {{ officialDoc.name }}
    </h3>

    <div class="description mb-3 text-sm text-gray-600 bg-white p-2 rounded border-l-2 border-purple-300">
      <div class="descHeight">
        <div class="w-e-text break-all" ref="descRef" v-html="officialDoc.description"></div>
        <span class="more absolute bg-gray-50 text-purple-700 hover:text-purple-800" v-if="state.descHeight > 88"
          @click="handlePreview(officialDoc.description)">
          查看更多
        </span>
      </div>
    </div>
    <div class="mb-3">
      <div class="mb-2">
        <h4 class="text-sm font-medium text-purple-700">
          <font-awesome-icon :icon="['fas', 'file']" class="mr-1 text-xs" />
          附件
        </h4>
      </div>
      <div class="space-y-2">
        <div
          class="file-item bg-white border border-gray-200 rounded p-3 hover:bg-purple-50 hover:border-purple-200 cursor-pointer transition-all duration-200 shadow-sm"
          v-for="item in officialDoc.messageOfficialParamFileList" :key="item.fileId" @click="handleEvent(item)">
          <div class="flex items-center">
            <div class="flex-shrink-0 w-8 h-8 bg-purple-100 rounded flex items-center justify-center">
              <el-icon :size="16" color="#7c3aed">
                <Link />
              </el-icon>
            </div>
            <div class="ml-3 flex-1 min-w-0">
              <div class="flex items-center space-x-2">
                <span class="file-name text-sm font-medium text-gray-900 truncate">{{ item.fileName }}</span>
                <LevelBtn :message="item.secret" />
              </div>
              <p class="text-xs text-gray-500 mt-0.5">点击下载</p>
            </div>
            <div class="flex-shrink-0">
              <font-awesome-icon :icon="['fas', 'download']" class="text-xs text-purple-500" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="official-list mb-4 border border-gray-200 rounded-lg overflow-hidden">
      <div class="official-list-head text-purple-700 bg-purple-50 p-2.5 flex justify-between items-center">
        <h4 class="official-list-title text-sm font-medium">
          <font-awesome-icon :icon="['fas', 'users']" class="mr-1" />
          参与情况
        </h4>
        <span class="official-list-progress text-xs">{{ participants }}/{{ groupMemberNo }}</span>
      </div>
      <div v-if="isShowStudyList" class="official-list-box divide-y divide-gray-100 max-h-48 overflow-y-auto">
        <div v-for="item in officialDoc.messageOfficialStudyList"
          class="official-list-item flex items-start space-x-2.5 p-3 bg-white transition-colors duration-200">
          <DynamicAvatar :id="item.creator" :data-info="item" :relation-name="item.creatorName" :size="20"
            :type-avatar="0" />
          <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between mb-1">
              <span class="item-user text-xs font-medium text-gray-900">{{ item.creatorName }}</span>
              <span class="item-time text-xs text-gray-500 whitespace-nowrap ml-2">{{ dayjs(item.time).format("HH:mm")
                }}</span>
            </div>
            <p class="item-user item-info text-xs text-gray-600 truncate max-w-full">已学习</p>
          </div>
        </div>
      </div>
    </div>
    <div class="official-br flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <DynamicAvatar :id="officialDoc.creator ?? ''" :data-info="officialDoc" :relation-name="officialDoc.creatorName"
          :size="20" :type-avatar="0" />
        <span className="official-by text-xs text-gray-500"> 由 {{ officialDoc.creatorName }} 创建 </span>
      </div>
    </div>
    <div class="mt-4">
      <button v-if="officialDoc.studyStatus"
        class="studbox w-full flex items-center justify-center gap-1 px-3 py-2 text-sm font-medium text-gray-300 bg-gray-50 rounded">
        已学习
      </button>
      <button v-else @click="handleDone(docId)" :disabled="loading"
        class="w-full flex items-center justify-center gap-1 px-3 py-2 text-sm font-medium text-white bg-purple-600 rounded hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors duration-200">
        <font-awesome-icon :icon="['fas', 'edit']" />
        完成学习
      </button>
    </div>
  </div>
  <el-dialog title="概述详情" v-model="state.previewVisible" :append-to-body="true" :width="800">
    <div class="preview-container">
      <div class="w-e-text" v-html="state.previewContent"></div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancelPreview">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="MessageOfficialDoc">
import { watchEffect, computed, ref, reactive, onMounted, watch, onUnmounted } from "vue";
import { useHistoryStore } from "@/stores/modules/history";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import { useContactStore } from "@/stores/modules/friend";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import { getFileUrls } from "@/api/infra/file";
import LevelBtn from "@/components/LevelBtn/index.vue";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { participateOfficialDoc } from "@/api/modules/contact";

import WebSocketClient from "@/utils/websocket/websocketClient";
import { MessageCode } from "@/utils/websocket/messageInfo";
import { sendMessage } from "@/utils/websocket/messageService";
import { getBusinessList } from "@/api/modules/business";
import { isEE } from "@/utils/ipcRenderer";
import IpcListenerManager from "@/ipc/IpcListener";
import { useFileStore } from "@/stores/modules/file";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { fileDownload, generateRandomId } from "@/utils/common";
import { ipc } from "@/utils/ipcRenderer";
import axios from "axios";
import { entries } from "lodash";
const talkStore = useTalkStore();
const historyStore = useHistoryStore();
const userStore = useUserStore();
const contactStore = useContactStore();
const isExe = ref(false);

const ipcManager = IpcListenerManager.getInstance();
const DownLoadStore = useFileStore();

const props = defineProps({
  docId: {
    type: String,
    required: true
  }
});

onMounted(() => {
  ipcManager.add("MessageLoad", ipcApiRoute.downloadProgress, (_event, arg) => {
    DownLoadStore.updateDwonloadFiles(arg);
  });
});

watch(
  () => props.docId,
  (newVal: string) => {
    if (!newVal) return;
    historyStore.getOfficialDocInfo(newVal, userStore.userId);
  },
  {
    immediate: true
  }
);

const officialDoc = computed(() => historyStore.officialDocuments[props.docId] || {});
const groupMemberNo = computed(() => (contactStore.groupMembers[talkStore.activeChatId] || []).length);
const isShowStudyList = computed(
  () =>
    contactStore.groupMembers[talkStore.activeChatId]?.groupOwnerId == userStore.userId ||
    historyStore.officialDocuments[props.docId]?.creator == userStore.userId
);
const participants = computed(() => {
  let { messageOfficialStudyList = [] } = historyStore.officialDocuments[props.docId] || {};
  let count = 0;
  let groupMembers = contactStore.groupMembers[talkStore.activeChatId] || [];
  messageOfficialStudyList.forEach((item: any) => {
    if (groupMembers.some((m: any) => m.member == item.creator)) count++;
  });

  return count;
});

const handleEvent = async (item: any) => {
  DownLoadStore.badge++;
  const fileId = generateRandomId(10);
  const downloadUrl = import.meta.env.VITE_API_URL2 + `?id=${item.fileId}`
  let params = {
    fetchUrl: downloadUrl,
    fileId: fileId,
    fileName: item.fileName,
  }
  ipc.send(ipcApiRoute.downloadFileByStream, params)
  // const fileId = generateRandomId(10);
  // let def_file = {
  //   file_id: fileId,
  //   fileName: item.fileName,
  //   state: "start",
  //   speed: 0,
  //   progress: 0,
  //   totalBytes: 0,
  //   receivedBytes: 0,
  //   paused: false
  // };
  // DownLoadStore.updateDwonloadFiles(def_file);
  // // 获取文件
  // try {
  //   const response = await axios.get('/admin-api/infra/file/downloadByUrl', { responseType: 'blob', params: { id: item.fileId } })
  //   //const res: any = await getFileUrls({ id: item.fileId });
  //   if (response) {
  //     DownLoadStore.downloadList = DownLoadStore.downloadList.filter(file => file.file_id !== fileId);
  //     fileDownload(response, item);
  //   }
  // } catch (error) {
  //   def_file.state = "error";
  //   DownLoadStore.updateDwonloadFiles(def_file);
  // }
};
// const fileDownload = async (response: any, item: any) => {
//   let blobFile = new Blob([response.data]);
//   const url = window.URL.createObjectURL(blobFile);
//   const a = document.createElement('a');
//   a.href = url;
//   a.download = item.fileName;
//   document.body.appendChild(a);
//   a.click();
//   a.remove();
//   window.URL.revokeObjectURL(url);
// };
const state = reactive({
  previewVisible: false,
  previewContent: "",
  descHeight: 0
});

const descRef = ref();
let resizeObserver: ResizeObserver | null = null

const handleResize = (entries: ResizeObserverEntry[]) => {
  for (const entry of entries) {
    const { height } = entry.contentRect
    state.descHeight = height
  }
}

const handlePreview = (content: string) => {
  state.previewContent = content;
  state.previewVisible = true;
};

const wsClient = WebSocketClient.getInstance();

const loading = ref(false);
const handleDone = async (id: string) => {
  loading.value = true;
  const { code } = await participateOfficialDoc({ id });
  if (code == 0) {
    try {
      historyStore.setParticipateOfficialDoc(id, {
        creator: userStore.userId,
        creatorName: userStore.name,
        time: dayjs().valueOf(),
        docId: id
      })

      await sendMessage(wsClient!, {
        code: MessageCode.OFFICIAL_DOCUMENT,
        receiverId: talkStore.activeChatId,
        isGroup: true,
        content: {
          creator: userStore.userId,
          creatorName: userStore.name,
          time: dayjs().valueOf(),
          docId: id
        }
      });
      getData();
    } catch (error: any) {
      ElMessage.error(error.message);
    }
  }
  loading.value = false;
};
const getData = async () => {
  const res: any = await getBusinessList(talkStore.activeChatId);
  if (res.code == 0) {
    historyStore.activity = res.data;
  }
};
const handleCancelPreview = () => {
  state.previewVisible = false;
  state.previewContent = "";
};
onMounted(() => {
  if (!isEE) {
    isExe.value = false;
  } else {
    isExe.value = true;
  }

  if (descRef.value) {
    resizeObserver = new ResizeObserver(handleResize)
    resizeObserver.observe(descRef.value)
  }
});

onUnmounted(() => {
  if (resizeObserver) resizeObserver.disconnect()
})
</script>

<style scoped lang="scss">
.official-box {
  min-width: 335px;
  position: relative;
  background: rgb(243, 237, 255) !important;
  border-color: rgb(233, 165, 252) !important;
  border-radius: 12px;
}

.official-boxs {
  min-width: 335px;
  position: relative;
  background: rgb(243, 237, 255) !important;
  border-radius: 12px;
  border: 1px solid rgb(233, 165, 252) !important;
}

.studbox {
  cursor: default;
}

.descHeight {
  max-height: 85px;
  overflow: hidden;
  position: relative;

  .more {
    right: 0;
    bottom: 0;
    display: inline-block;
    width: 80px;
    text-align: center;
    cursor: pointer;

    &:hover {
      background-color: azure;
    }
  }

  :deep(.w-e-text) {
    overflow-y: hidden;
    box-sizing: border-box;

    p {
      width: 300px !important;
    }

    ol {
      list-style: decimal;
    }

    ul {
      list-style: disc
    }

  }
}

.preview-container {
  height: 60vh;
  overflow-x: hidden;
  overflow-y: auto;

  :deep(.w-e-text) {
    overflow-y: unset;

    ol {
      list-style: decimal;
    }

    ul {
      list-style: disc
    }
  }
}

.official-item {
  @apply border-gray-200 hover:border-blue-300 hover:bg-gray-50;

  .official-item-title.active {
    @apply text-blue-700;
  }

  .official-item-percent.active {
    @apply bg-blue-100 text-blue-700;
  }

  :deep(.el-progress) {
    .el-progress-bar__outer {
      @apply bg-gray-200;
    }

    .el-progress-bar__inner {
      @apply bg-blue-100;
    }

    &.active .el-progress-bar__inner {
      @apply bg-blue-600;
    }
  }
}

.dark {

  .official-box,
  .official-boxs {
    background: rgba(88, 28, 135, 0.05) !important;
    border-color: rgba(126, 34, 206, 0.2) !important;
    @apply border-gray-700 bg-gray-800;

    .head {
      @apply text-cyan-900 bg-blue-900/40 hover:bg-cyan-300;
    }

    .time {
      @apply bg-gray-700;
    }

    .title {
      @apply text-white hover:text-blue-400;
    }

    .description {
      @apply text-gray-300 bg-gray-700/40 border-blue-700;
    }

    .official-list {
      @apply border-gray-700;
    }

    .official-list-head {
      @apply bg-purple-900/20 text-white;
    }

    .official-list-item {
      @apply bg-gray-700/40;
    }

    .file-item {
      @apply bg-gray-700/40 text-gray-900 border-purple-700 border-gray-700 hover:border-purple-700 hover:bg-gray-700/60;

      &:hover {
        .file-name {
          @apply text-gray-300;
        }
      }
    }

    .file-name {
      @apply text-white;
    }

    .item-user {
      @apply text-white;
    }
  }

  .official-br {
    @apply border-gray-700;
  }

  .official-by {
    @apply text-gray-400;
  }

  .official-total {
    @apply bg-blue-900/30 text-blue-400;
  }

  .studbox {
    @apply bg-gray-600;
    cursor: default;
  }

  .descHeight {
    .more {
      background-color: #3a3c3e;
    }
  }
}
</style>
