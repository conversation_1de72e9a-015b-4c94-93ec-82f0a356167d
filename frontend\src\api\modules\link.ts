import request from "@/api";

// 链接信息 VO
export interface AppLinkRespVO {
  id: number; // 主键
  title: string; // 标题
  linkAddress: string; // 链接地址
  state: string; // 链接状态
  image: string; // 链接标志（图标）
  personal: string; // 个人标志
  userId: string; // 用户ID/组织机构ID
  param: string; // 链接参数
  description: string; // 描述
  orgCode: string; // 所属单位
  phone: string; // 电话号码
  isLocal?: boolean; // 是否为本地链接
}

// 链接排序请求 VO
export interface AppLinkSortReqVO {
  linkId: string;
  sortNum: number;
}

// 链接信息 API
export const LinkApi = {
  // 获得用户可见的链接列表
  getUserVisibleLinks: async (orgCode: string) => {
    return await request.get("/app-api/ai/link/list", { params: { orgCode } });
  },

  // 获得用户可见的链接列表
  getUserNewVisibleLinks: async (orgCode: string, name: string) => {
    return await request.get("/app-api/ai/link/search", { params: { orgCode, name } });
  },

  // 获得用户常用链接列表
  getUserFavoriteLinks: async (orgCode: string) => {
    return await request.get("/app-api/ai/link/favorites", { params: { orgCode } });
  },

  // 获得链接详情
  getLink: async (id: number) => {
    return await request.get("/app-api/ai/link/get", { params: { id } });
  },

  // 设置链接排序
  setLinkSort: async (sortList: AppLinkSortReqVO[]) => {
    return await request.post("/app-api/ai/link/set-sort", sortList);
  },

  // 设置链接状态
  setLinkState: async (linkId: number, state: string) => {
    return await request.post("/app-api/ai/link/set-state", null, { params: { linkId, state } });
  },

  // 搜索链接列表
  searchLinks: async (orgCode: string, name?: string) => {
    return await request.get("/app-api/ai/link/search", { params: { orgCode, name } });
  },

  // 检查链接访问权限
  checkLinkPermission: async (linkId: number, orgCode: string) => {
    return await request.get("/app-api/ai/link/check-permission", { params: { linkId, orgCode } });
  },

  // 添加到常用链接
  addToFavorites: async (linkId: number) => {
    return await request.post("/app-api/ai/link/add-favorite", null, { params: { linkId } });
  },

  // 从常用链接中移除
  removeFromFavorites: async (linkId: number) => {
    return await request.post("/app-api/ai/link/remove-favorite", null, { params: { linkId } });
  },

  // 移除链接状态
  removeLinkState: async (linkId: number) => {
    return await request.post("/app-api/ai/link/remove-state", null, { params: { linkId } });
  },

  // 添加新链接
  addLink: async (linkData: Partial<AppLinkRespVO>) => {
    return await request.post("/app-api/ai/link/create", linkData);
  },

  // 更新链接
  updateLink: async (linkData: Partial<AppLinkRespVO>) => {
    return await request.put("/app-api/ai/link/update", linkData);
  }
};
