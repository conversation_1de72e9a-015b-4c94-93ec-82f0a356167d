<template>
  <div class="dynamic-avatar" @click="handleClick">
    <div class="avatar-wrapper" :class="avatarBorderClass" :style="wrapperStyle">
      <el-avatar :size="size" :src="avatarSrc" :shape="shape">
        <img :src="defaultAvatar" alt="default_avatar" />
      </el-avatar>
      <div v-if="showOnlineStatus" class="online-status" :class="onlineStatusClass"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, PropType, onMounted, watch } from "vue";
import userAvatar from "@/assets/images/user.svg";
import groupAvatar from "@/assets/images/group.svg";
import businessAvatar from "@/assets/images/business.svg";
import defaultAvatar from "@/assets/images/user.svg";
import { useContactsStore } from "@/stores/modules/contacts";

const contactsStore = useContactsStore();

const openProfile: any = inject("clickOpenProfile");

const props = defineProps({
  dataInfo: {
    type: Object as any
  },
  id: {
    type: String,
    required: true
  },
  relationName: {
    type: String,
    default: ""
  },
  online: {
    type: String
  },
  size: {
    type: Number,
    default: 40
  },
  shape: {
    type: String as PropType<"circle" | "square">,
    default: "circle"
  },
  onClick: {
    type: Function as PropType<(id: string) => void>,
    default: null
  },
  typeAvatar: {
    type: Number,
    default: 5
  },
  typeAvatarMap: {
    type: Object as PropType<Record<string, string>>,
    default: () => ({
      0: userAvatar, //user
      1: groupAvatar, //group
      2: businessAvatar, //business
      5: defaultAvatar //default
    })
  }
});
// 判断是否需要显示在线状态
const showOnlineStatus = computed(() => props.typeAvatar === 0);

const onlineStatusClass = computed(() => {
  let userInfo = contactsStore.getCacheContactById(props.id);
  if (userInfo?.isOnline) {
    if (props.size > 24 && props.size <= 40) {
      return "status-online";
    } else if (props.size <= 24) {
      return "status-online online-status-small";
    } else {
      return "status-online online-status-new";
    }
  } else {
    if (props.size > 24 && props.size <= 40) {
      return "status-offline";
    } else if (props.size <= 24) {
      return "status-offline online-status-small";
    } else {
      return "status-offline online-status-new";
    }
  }
});

// 计算头像源
const avatarSrc = computed(() => {
  let userInfo = contactsStore.getCacheContactById(props.id);
  if (userInfo && userInfo.avatar) {
    return userInfo.avatar;
  }

  let avatar = props.dataInfo?.avatar;

  if (!avatar) {
    const type = props.typeAvatar;
    if (type === 0) {
      avatar = props.typeAvatarMap[type];
    }
    if (type && props.typeAvatarMap[type]) {
      avatar = props.typeAvatarMap[type];
    }
  }
  return avatar;
});

watch(
  () => props.id,
  (newVal: string) => {
    let userInfo = contactsStore.getCacheContactById(newVal);
    if (!newVal || userInfo) return;

    // 私聊头像
    if (props.typeAvatar == 0) {
      const addUserInfo = {
        id: newVal,
        name: props.relationName,
        avatar: props.dataInfo?.avatar ? props.dataInfo?.avatar : props.typeAvatarMap[0],
        secretLevel: props.dataInfo.secretLevel || props.dataInfo.secret,
        isOnline: props.dataInfo.online == "on"
      };

      if (!addUserInfo.name || !addUserInfo.secretLevel) {
        contactsStore.getContactById(newVal, props.typeAvatar != 0).then(res => {
          if (res && res.id) {
            contactsStore.updateContact({
              ...res
            });
          }
        });
      } else {
        contactsStore.updateContact(addUserInfo, ["avatar"]);
      }
    }
  },
  {
    immediate: true
  }
);

// 组件挂载时检查头像可用性
onMounted(() => {});

// 计算头像容器样式
const wrapperStyle = computed(() => {
  return {
    width: `${props.size}px`,
    height: `${props.size}px`,
    borderRadius: "50%"
  };
});

// 计算头像边框类名
const avatarBorderClass = computed(() => {
  const isOfficial = props.typeAvatar === 2;
  const isGroup = props.typeAvatar === 1;
  const isUser = props.typeAvatar === 0;

  if (isOfficial) {
    return "avatar-border-official";
  } else if (isGroup) {
    return "avatar-border-group";
  } else if (isUser) {
    return "avatar-border-user";
  }
  return "avatar-border-default";
});

// 处理点击事件
const handleClick = () => {
  openProfile(props.id, props.typeAvatar);
};
</script>

<style scoped>
.dynamic-avatar {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  position: relative;
  transition: transform 0.2s ease-in-out;
}

.dynamic-avatar:hover {
  transform: scale(1.02);
}

.avatar-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: visible;
}

.avatar-wrapper::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  z-index: -1;
  transition: all 0.3s ease;
}

/* 用户头像 - 无边框 */
.avatar-border-user {
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.avatar-border-user::before {
  background: transparent;
}

.avatar-border-user:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 群组头像 - 无边框 */
.avatar-border-group {
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.avatar-border-group::before {
  background: transparent;
}

/* .avatar-border-group:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
} */

/* 官方头像 - 金色边框 */
.avatar-border-official {
  border: 2px solid #f59e0b;
  box-shadow:
    0 0 0 1px rgba(245, 158, 11, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.05);
}

.avatar-border-official::before {
  background: transparent;
}

.avatar-border-official:hover {
  border-color: #d97706;
  box-shadow:
    0 0 0 3px rgba(245, 158, 11, 0.2),
    0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 默认头像 - 无边框 */
.avatar-border-default {
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.avatar-border-default::before {
  background: transparent;
}

.avatar-border-default:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.avatar-wrapper :deep(.el-avatar) {
  position: relative;
  border: none;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  width: 100% !important;
  height: 100% !important;
  border-radius: 50% !important;
}

.avatar-wrapper :deep(.el-avatar img) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  border-radius: 50%;
}

.online-status {
  position: absolute;
  bottom: -2px;
  right: -5px;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.online-status-new {
  position: absolute;
  bottom: 0px;
  right: 5px;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.online-status-small {
  position: absolute;
  bottom: -1px;
  right: -3px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 1px solid #fff;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);
}

.status-online {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.status-offline {
  background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .avatar-wrapper :deep(.el-avatar) {
    border-color: rgba(255, 255, 255, 0.1);
  }

  .avatar-overlay {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 50%, rgba(255, 255, 255, 0) 100%);
  }

  .online-status,
  .online-status-new,
  .online-status-small {
    border-color: rgba(0, 0, 0, 0.8);
  }
}
</style>
