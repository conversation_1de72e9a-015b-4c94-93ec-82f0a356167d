<template>
  <DragContainer name="ballWin" ref="drageContainer">
    <div id="ballWin" @dragover.prevent @drop.prevent="handleDrop" :style="{ opacity }">
      <div class="ball-container relative">
        <!-- 图标 -->
        <div class="text-center">
          <img src="@/assets/images/ball.png" alt="dropdown trigger" class="ball-img" draggable="false" />
        </div>

        <!-- 弹出菜单 -->
        <div
          class="ball-menu absolute w-[100%] left-1/2 top-1/2 translate-x-[-50%] translate-y-[4px] mt-2 bg-black/70 text-white rounded-xl py-4 space-y-2 z-50"
        >
          <div class="flex flex-wrap gap-2 justify-center">
            <MenuItem :icon="Search" label="问知识" @click="ballClick(1)" />
            <MenuItem :icon="Search" label="问流程" @click="ballClick(2)" />
          </div>
        </div>
      </div>
    </div>
  </DragContainer>
</template>

<script lang="ts" setup>
import softwareStore from "@/stores/modules/software";
import { computed, ref } from "vue";
import { ipc } from "@/utils/ipcRenderer";
import { ElMessage } from "element-plus";
import { ipcApiRoute } from "@/ipc/ipcApi";
import MenuItem from "./MenuItem.vue";
import { User, Search } from "@element-plus/icons-vue";
import DragContainer from "./DragContainer.vue";

const setting = softwareStore();
const opacity = computed(() => setting.ballTransparency / 100);

const uploading = ref(false);
const progress = ref(0);

// 拖拽上传
// 拖拽上传处理
const handleDrop = async (e: DragEvent) => {
  const files = e.dataTransfer?.files;
  if (files && files.length > 0) {
    const file = files[0]; // 只处理一个文件，可扩展成多个

    try {
      uploading.value = true;
      progress.value = 0;

      const formData = new FormData();
      formData.append("file", file);

      // 上传文件到你的服务器
      // await axios.post("http://localhost:3000/upload", formData, {
      //   headers: {
      //     "Content-Type": "multipart/form-data"
      //   },
      //   onUploadProgress: event => {
      //     if (event.total) {
      //       progress.value = Math.round((event.loaded / event.total) * 100);
      //     }
      //   }
      // });

      // 上传完成
      ElMessage.success("上传成功");

      // 模拟总结
      // summaryFileName.value = file.name;
      // summaryText.value = generateSummary(file.name);
      // dialogVisible.value = true;
      ipc.send(ipcApiRoute.creatViewDialog, {
        fileName: file.name
        // fileContent: file
      });
    } catch (err) {
      alert("上传失败，请重试");
    } finally {
      uploading.value = false;
    }
  }
};

// 悬浮球点击
const drageContainer: any = ref(null);
const ballClick = code => {
  if (!drageContainer?.ismoving) {
    ipc.send(ipcApiRoute.clickFloatingDialog, code);
  }
};
</script>

<style lang="scss" scoped>
#ballWin {
  width: 100%;
  height: 100%;
  overflow: hidden;
  user-select: none;
  // -webkit-app-region: drag;
}

.ball-img {
  width: 90px;
  display: inline-block;
}
.ball-menu {
  display: none;
}
.ball-container:hover .ball-menu {
  display: block;
}
</style>
