/* 
使用说明：
1、在mian.ts 中引入此文件
import "@/assets/iconfont/iconfont.scss";
2、在页面中添加图标的类名即可，如：<i class="iconfont icon-xxx"></i>
*/



@font-face {
  font-family: iconfont; /* Project id 2667653 */
  src: url("iconfont.ttf?t=1694681005434") format("truetype");
  font-display: auto;
}
.iconfont {
  font-family: iconfont !important;
  font-size: 18px;
  font-style: normal;
  -webkit-font-smoothing: antialiased; // 解决 ie 浏览器渲染字体图标乱码问题
  -moz-osx-font-smoothing: grayscale; // 解决 ie 浏览器渲染字体图标乱码问题
  cursor: pointer; 
}
.icon-yiwen::before {
  font-size: 18px;
  content: "\e693";
}
.icon-xiala::before {
  content: "\e62b";
}
.icon-tuichu::before {
  content: "\e645";
}
.icon-xiaoxi::before {
  font-size: 18px;
  content: "\e61f";
}
.icon-sousuo::before {
  content: "\e611";
}
.icon-contentright::before {
  content: "\e8c9";
}
.icon-contentleft::before {
  content: "\e8ca";
}
.icon-fangda::before {
  content: "\e826";
}
.icon-suoxiao::before {
  content: "\e641";
}
.icon-zhongyingwen::before {
  content: "\e8cb";
}
