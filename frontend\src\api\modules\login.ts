import { Login } from "../interface/login";
// import { AdminService } from "@/api/config/servicePort";
import request from "@/api";
// import LoginJson from "@/assets/json/login.json";
// import LoginOutJson from "@/assets/json/logout.json";
// import InfoJson from "@/assets/json/info.json";
// import authMenuList from "@/assets/json/authMenuList.json";
import authButtonList from "@/assets/json/authButtonList.json";

// 获取按钮权限
export const getAuthButtonListApi = () => {
  // return request.get<Login.ResAuthButtons>(AdminService.BUTTONS);
  return authButtonList;
};

// 获取菜单列表
export const getAuthMenuListApi = async () => {
  const res = await request.get("/admin-api/system/menu/list?parentId=2758");
  return res;
};

// 用户登录
export const loginApi = async (params: Login.ReqLoginForm) => {
  const res = await request.post("/admin-api/system/auth/login", params);
  return res;
};

// 用户退出登录
export const logoutApi = async () => {
  const res = request.post("/admin-api/system/auth/logout");
  return res;
};

// 用户头像上传
export const uploadAvatar = (data: any) => {
  return request.post("/admin-api/system/user/profile/update-avatar", data);
};
