<template>
  <div 
    class="ai-app-icon"
    :style="iconStyle"
    :class="sizeClass"
  >
    <!-- 背景装饰层 -->
    <div class="bg-decoration"></div>
    
    <!-- 主要文字内容 -->
    <div class="icon-text">
      <div class="text-line">{{ topText }}</div>
      <div class="text-line">{{ bottomText }}</div>
    </div>
    
    <!-- 光泽效果 -->
    <div class="shine-effect"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  /** 应用名称 */
  name: string
  /** 图标字符串（用于生成背景色） */
  icon?: string
  /** 图标大小 */
  size?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  icon: '',
  size: 'medium'
})

// 根据字符串生成固定的渐变色（深色系）
const generateGradient = (str: string): { primary: string; secondary: string; accent: string } => {
  const colorSets = [
    { primary: '#2563eb', secondary: '#1e40af', accent: '#3730a3' },
    { primary: '#7c3aed', secondary: '#5b21b6', accent: '#4c1d95' },
    { primary: '#059669', secondary: '#047857', accent: '#065f46' },
    { primary: '#dc2626', secondary: '#991b1b', accent: '#7f1d1d' },
    { primary: '#0891b2', secondary: '#0e7490', accent: '#155e75' },
    { primary: '#ea580c', secondary: '#c2410c', accent: '#9a3412' },
    { primary: '#be123c', secondary: '#9f1239', accent: '#881337' },
    { primary: '#4338ca', secondary: '#3730a3', accent: '#312e81' },
    { primary: '#0369a1', secondary: '#0c4a6e', accent: '#082f49' },
    { primary: '#b91c1c', secondary: '#7f1d1d', accent: '#450a0a' },
    { primary: '#166534', secondary: '#14532d', accent: '#052e16' },
    { primary: '#a21caf', secondary: '#86198f', accent: '#701a75' },
    { primary: '#374151', secondary: '#1f2937', accent: '#111827' },
    { primary: '#475569', secondary: '#334155', accent: '#1e293b' },
    { primary: '#525252', secondary: '#404040', accent: '#262626' }
  ]
  
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  const index = Math.abs(hash) % colorSets.length
  return colorSets[index]
}

// 提取应用名称的前4个字符，分为上下两行
const topText = computed(() => {
  const text = props.name.slice(0, 4)
  return text.slice(0, 2)
})

const bottomText = computed(() => {
  const text = props.name.slice(0, 4)
  return text.slice(2, 4)
})

// 生成图标样式
const iconStyle = computed(() => {
  const colors = generateGradient(props.icon || props.name)
  return {
    '--primary-color': colors.primary,
    '--secondary-color': colors.secondary,
    '--accent-color': colors.accent,
    background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.secondary} 100%)`,
    borderColor: `${colors.primary}40`,
    boxShadow: `0 8px 32px ${colors.primary}25, 0 4px 16px ${colors.secondary}20`
  }
})

// 尺寸类名
const sizeClass = computed(() => {
  return `size-${props.size}`
})
</script>

<style scoped lang="scss">
.ai-app-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  border: 1px solid;
  color: white;
  font-weight: 600;
  overflow: hidden;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  // 移除悬停效果，只保留基础样式
  
  // 不同尺寸
  &.size-small {
    width: 32px;
    height: 32px;
    border-radius: 10px;
    
    .icon-text {
      font-size: 9px;
      line-height: 1;
      letter-spacing: -0.5px;
    }
  }
  
  &.size-medium {
    width: 48px;
    height: 48px;
    border-radius: 14px;
    
    .icon-text {
      font-size: 13px;
      line-height: 1.1;
      letter-spacing: -0.3px;
    }
  }
  
  &.size-large {
    width: 64px;
    height: 64px;
    border-radius: 18px;
    
    .icon-text {
      font-size: 17px;
      line-height: 1.2;
      letter-spacing: -0.2px;
    }
  }
}

// 背景装饰层
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
  z-index: 0;
}

// 光泽效果
.shine-effect {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform: translateX(-100%);
  transition: all 0.6s ease;
  opacity: 0;
  z-index: 1;
}

.icon-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  z-index: 2;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  
  .text-line {
    display: block;
    white-space: nowrap;
    font-weight: 700;
  }
}

// 深色模式适配
.dark .ai-app-icon {
  .bg-decoration {
    background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  }
  
  .icon-text {
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  }
}
</style>
