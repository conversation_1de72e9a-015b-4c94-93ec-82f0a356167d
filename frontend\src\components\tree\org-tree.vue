<template>
  <div class="tree">
    <el-tree
      class="custom-tree"
      :data="data"
      :props="defaultProps"
      :default-expanded-keys="expandedKeys"
      node-key="id"
      @node-click="handleNodeClick"
    >
    </el-tree>
  </div>
</template>

<script setup lang="ts" name="OrgTree">
import { ref, watchEffect } from "vue";
import { useUserStore } from "@/stores/modules/user";
import * as orgApi from "@/api/modules/org";

const userStore = useUserStore();

const props = defineProps({
  groupScope: String
});

const defaultProps = {
  label: "orgName",
  children: "children",
  value: "orgCode"
};
const data: any = ref([]);
const expandedKeys: any = ref([]);
const emit = defineEmits(["node-click"]);

watchEffect(async () => {
  let params: any = {
    orgId: userStore.userInfo.orgCode,
    direction: "up",
    level: props.groupScope ? (props.groupScope.match(/\d+/) || [])[0] : "-1"
  };
  const res: any = await orgApi.generateOrgByUserOrgId(params);
  if (res.code == 0) {
    const orgList: any = await orgApi.getOrgTreeByOrgId(res.data.id);
    if (orgList.code == 0) {
      data.value = orgList.data;
      expandedKeys.value = [orgList.data[0].id];
    }
  }
});

const handleNodeClick = (data: any) => {
  if (data.children.length > 0) {
    return;
  } else {
    emit("node-click", data.orgCode);
  }
};
</script>

<style scoped lang="scss"></style>
