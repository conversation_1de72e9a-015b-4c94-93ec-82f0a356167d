<template>
  <div class="contact space-y-2 relative">
    <div v-if="user?.length == 0">
      <NoData />
    </div>
    <div
      v-for="item in user as any"
      :key="item.id"
      class="user-item flex items-center gap-3 rounded-lg p-3 hover:bg-gray-100 cursor-pointer"
      @click="openChat(item)"
    >
      <div @click.stop>
        <DynamicAvatar
          :id="item.id"
          :relation-name="item.name"
          :data-info="item"
          :type-avatar="0"
          :size="40"
          :online="item.online"
        />
      </div>
      <div class="flex-1 flex flex-col">
        <div class="flex items-center mb-1">
          <h4 class="text-sm font-medium text-gray-900 mr-2">{{ item.name }}</h4>
          <LevelBtn :message="item.secretLevel" data-type="user"></LevelBtn>
        </div>
        <p class="text-xs text-gray-500">{{ item.pathName || item.orgName }}</p>
      </div>
      <div v-if="!item.friendShipId" @click.stop class="actions">
        <el-tooltip content="发送消息" placement="top" v-if="item.ifOuter">
          <el-button link icon="chat-dot-round" @click="openChat(item)"></el-button>
        </el-tooltip>
        <el-tooltip content="添加好友" placement="top" v-else>
          <el-button link icon="circle-plus" @click="addFriends(item)"></el-button>
        </el-tooltip>
      </div>
      <div v-else>
        <div v-if="item.status == '0'" class="space-x-4" @click.stop>
          <el-button size="small" type="success" v-if="item.userId != userStore.userId" @click="uptDialog(item, '1')">
            同意
          </el-button>
          <el-button size="small" type="danger" v-if="item.userId != userStore.userId" @click="uptDialog(item, '2')">
            拒绝
          </el-button>
          <el-tag type="primary" v-if="item.userId == userStore.userId">等待同意</el-tag>
        </div>
        <div v-if="item.status == '1'" @click.stop class="actions">
          <el-tooltip content="发送消息" placement="top">
            <el-button link icon="chat-dot-round" @click="openChat(item)"></el-button>
          </el-tooltip>
          <el-tooltip content="修改分组" placement="top">
            <el-button link icon="edit" @click="uptDialog(item, '3')"></el-button>
          </el-tooltip>
          <el-tooltip content="删除好友" placement="top">
            <el-button link icon="delete" @click="uptDialog(item, '4')"></el-button>
          </el-tooltip>
        </div>
        <div v-if="item.status == '2'" @click.stop>
          <el-tag type="danger">已拒绝</el-tag>
        </div>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" :show-close="false" class="custom-dialog need-foot">
      <template #header>
        <button class="close-btn" @click="dialogVisible = false">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'calendar']" class="icon" />
          <span class="title">{{ dialogInfo.title }}</span>
        </div>
      </template>
      <template #default>
        <div v-if="dialogInfo.type == '1'">
          <div class="mb-2">请选择好友分组</div>
          <el-select v-model="dialogInfo.data.groupId">
            <el-option v-for="item in treeData" :key="item.id" :label="item.groupName" :value="item.id"></el-option>
          </el-select>
        </div>
        <div v-else-if="dialogInfo.type == '2'">确定拒绝 {{ dialogInfo.data.name }} 的好友申请</div>
        <div v-else-if="dialogInfo.type == '3'">
          <div class="mb-2">请选择好友分组</div>
          <el-select v-model="dialogInfo.data.groupId">
            <el-option v-for="item in treeData" :key="item.id" :label="item.groupName" :value="item.id"></el-option>
          </el-select>
        </div>
        <div v-else>确定删除好友 {{ dialogInfo.data.name }}</div>
      </template>
      <template #footer>
        <el-button type="default" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="sure">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="SingleContact">
import { ref, watchEffect } from "vue";
import { useTalkStore } from "@/stores/modules/talk";
import { useUserStore } from "@/stores/modules/user";
import { useRecentStore } from "@/stores/modules/recent";
import { useFriendStore } from "@/stores/modules/friends";
import { updateFriend, deleteFriend, addFriend } from "@/api/modules/contact";
import LevelBtn from "@/components/LevelBtn/index.vue";
import { ElMessage } from "element-plus";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import NoData from "@/components/NoData/index.vue";

const talkStore = useTalkStore();
const userStore = useUserStore();
const recentStore = useRecentStore();
const friendStore = useFriendStore();

const props = defineProps({
  user: {
    type: Array,
    required: true
  }
});

const user: any = ref([]);
const treeData: any = ref([]);
watchEffect(() => {
  if (props.user) {
    user.value = props.user;
  }
  treeData.value = friendStore.groupTree;
});

const emits = defineEmits(["search-click"]);
const openChat = async (data: any) => {
  if (data.friendShipId && data.status != "1") return;
  if (data.isExternal == "1" && !data.ifOuter) return;
  emits("search-click");
  const params = {
    senderId: userStore.userId,
    contactId: data.id,
    chatType: 0,
    avatar: data.avatar,
    contactName: data.name,
    secret: data.secretLevel
  };
  const res = await recentStore.addListRecents(params);
  if (res) {
    talkStore.setActiveChat(data.id);
    talkStore.ifChat = true;
    talkStore.ifContact = false;
  }
};

const addFriends = async (data: any) => {
  const params = {
    userId: userStore.userId,
    friendId: data.id,
    // userId: data.id,
    // friendId: userStore.userId,
    groupId: "default",
    status: "0"
  };
  const res: any = await addFriend(params);
  if (res.code == 0) {
    ElMessage.success("操作成功，请等待对方同意");
    await friendStore.getAllFriend({ userId: userStore.userId });
  }
};

const dialogVisible = ref(false);
const dialogInfo = ref({
  title: "",
  type: "",
  data: <Record<string, any>>{}
});
const uptDialog = (data: any, num: string) => {
  dialogVisible.value = true;
  dialogInfo.value = {
    title: "管理好友",
    type: num,
    data: data
  };
};
const updateFriends = async (info: any) => {
  let params;
  if (info.type == "1") {
    params = {
      id: info.data.friendShipId,
      groupId: info.data.groupId,
      status: "1"
    };
  } else if (info.type == "2") {
    params = {
      id: info.data.friendShipId,
      status: "2"
    };
  } else {
    params = {
      id: info.data.friendShipId,
      groupId: info.data.groupId
    };
  }
  const res: any = await updateFriend(params);
  if (res.code == 0) {
    dialogVisible.value = false;
    const index = user.value.findIndex((item: any) => item.friendShipId == info.data.friendShipId);
    if (info.type == "1") {
      user.value[index].status = "1";
    } else if (info.type == "2") {
      user.value[index].status = "2";
    } else {
      user.value.splice(index, 1);
    }
    ElMessage.success("操作成功");
  }
};
const deleteFriends = async (data: any) => {
  const res: any = await deleteFriend(data.friendShipId);
  if (res.code == 0) {
    dialogVisible.value = false;
    friendStore.deleteFriend(data.friendShipId);
    ElMessage.success("删除成功");
  }
};

const sure = () => {
  if (dialogInfo.value.type == "4") {
    deleteFriends(dialogInfo.value.data);
  } else {
    updateFriends(dialogInfo.value);
  }
};
</script>

<style scoped lang="scss">
.actions {
  .el-button {
    @apply text-xl;
  }
}

.dark {
  .user-item {
    @apply hover:bg-gray-700;
  }
  h4 {
    @apply text-white;
  }
  p {
    @apply text-gray-400;
  }
}
</style>
