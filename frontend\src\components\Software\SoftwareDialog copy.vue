<template>
  <div class="app">
    <!-- 拖拽区域 -->
    <div class="drop-zone" @dragover="handleDragOver" @drop="handleDrop">
      <h1>拖拽文件到这里</h1>
    </div>

    <ToolBar
      title=""
      v-model:is-edit="isEdit"
      :show-btns="!isDesktop && softList.length != 0"
      @add="selectFile('file')"
      @search="searchSoft"
    />

    <!-- 程序列表 -->
    <draggable
      v-model="softList"
      item-key="id"
      class="box-content"
      :style="{ '--software-size': 0.5 * 100 + 'px' }"
      :disabled="sortDisable"
      :animation="200"
      @start="startSortSoftList"
      @end="sortSoftList"
    >
      <template #item="{ element, index }">
        <div class="box-card" :class="{ isSorting }" @contextmenu="handleContextMenu(element)">
          <el-icon v-show="isEdit" class="delete-btn" size="20" @click.stop="deleteItem(element.id)">
            <!-- <RemoveFilled /> -->
          </el-icon>
          <el-tooltip :content="element.name" placement="top" :disabled="isSorting || !true">
            <el-image draggable="false" :src="element.icon" @click="openFile(element.path)" />
          </el-tooltip>
          <div v-if="true" class="box-name">{{ element.name }}</div>
        </div>
      </template>
    </draggable>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRaw, throttle } from "vue";
import draggable from "vuedraggable";
import ToolBar from "./ToolBar.vue";
import type { ISoftware } from "@/interfaces";
import { ipc } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { FormInstance } from "element-plus";

// 是否为桌面组件
const props = defineProps({
  isDesktop: Boolean
});

// 是否正在排序
const isSorting = ref(false);
const sortDisable = true;

// 是否编辑模式
const isEdit = ref(false);

// 软件列表
const softList = ref<ISoftware[]>([]);

// 获取数据
ipc.invoke(ipcApiRoute.getSoftwareList).then((data: ISoftware[]) => {
  if (data && Array.isArray(data) && data.length) {
    softList.value = data;
  }
});

// 保存数据
const saveData = () => {
  ipc.send(ipcApiRoute.setSoftwareList, toRaw(softList.value));
};

// 拖拽排序
const startSortSoftList = () => {
  isSorting.value = true;
};

const sortSoftList = () => {
  saveData();
  isSorting.value = false;
};

// 拖拽文件保存
const handleDrop = (e: any) => {
  e.preventDefault();

  const { files } = e.dataTransfer;
  const fileList = Array.from(files);
  if (!fileList.length) return;

  const filePaths = fileList.map((file: any) => file.path);
  ipc.invoke(ipcApiRoute.dragToolInto, filePaths).then((files: any[]) => {
    if (files && files.length) {
      addItem(files);
    }
  });
};

const handleDragOver = (e: any) => {
  e.preventDefault();
};

// 添加图标
const addItem = (file: ISoftware[]) => {
  softList.value.push(...file);
  saveData();
};

// 删除图标
const deleteItem = (id: string) => {
  const index = softList.value.findIndex(item => item.id === id);
  if (index !== -1) {
    softList.value.splice(index, 1);
    saveData();
  }
};

// 右键菜单
let selectItem: ISoftware;
const handleContextMenu = (item: ISoftware) => {
  selectItem = item;
  const menuList = [
    { label: "图标设置", value: "icon-set" },
    { label: "图标删除", value: "icon-delete" }
  ];
  ipc.send("show-context-menu", menuList);
};

// 点击打开软件
const openFile = throttle((path: string) => {
  if (isEdit.value) return;
  ipc.send(ipcApiRoute.openTool, path);
}, 1000);

// 图标搜索
const searchValue = ref("");
const searchSoft = (value: string) => {
  searchValue.value = value;
  searchSoftList();
};

const searchSoftList = () => {
  if (searchValue.value) {
    softList.value = toRaw(softList.value).filter(item => item.name.toLowerCase().includes(searchValue.value.toLowerCase()));
  } else {
    softList.value = toRaw(softList.value);
  }
};

// 修改图标
const formRef = ref<FormInstance>();
const dialogVisible = ref(false);
const formData: {
  id: string;
  name: string;
  path: string;
  icon: string;
} = reactive({
  id: "",
  name: "",
  path: "",
  icon: ""
});

const formRules = {
  name: [{ required: true, message: "图标名称不能为空", trigger: "blur" }],
  path: [{ required: true, message: "启动路径不能为空", trigger: "blur" }]
};

const selectFile = async (type: string) => {
  const options = {
    type
  };
  ipc.invoke("dialog:openFile", options).then((file: any[]) => {
    if (!file) return;

    if (type === "path") formData.path = file[0];
    if (type === "file") addItem(file);
  });
};
</script>
