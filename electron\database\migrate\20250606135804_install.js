exports.up = function (knex) {
  return knex.schema
    .createTable("recent_contact", (table) => {
      table.text("userId").notNullable(); // 当前登录人id
      table.text("contactId").notNullable();
      table.integer("updateTime");
      table.integer("isTop");
      table.integer("isAt");
      table.text("content");
      table.unique(["userId", "contactId"]);
    })
    .createTable("user_message", (table) => {
      table.text("userId").notNullable();
      table.text("id").notNullable();
      table.text("sender");
      table.text("receiver");
      table.integer("createTime");
      table.text("content");
      table.index(["userId", "sender", "receiver"]);
      table.unique(["userId", "id"]);
    })
    .createTable("group_message", (table) => {
      table.text("userId").notNullable(); // 当前登录人id
      table.text("id").notNullable();
      table.text("sender");
      table.text("receiver");
      table.integer("createTime");
      table.text("content");
      table.index(["userId", "sender", "receiver"]);
      table.unique(["userId", "id"]);
    })
    .createTable("countdown", (table) => {
      table.text("id").primary();
      table.text("userId").notNullable();
      table.text("name").notNullable();
      table.text("date").notNullable();
      table.integer("createTime");
      table.integer("updateTime");
      table.index(["userId"]);
    })
    .createTable("quick_notes", (table) => {
      table.increments("id").primary();
      table.text("title"); // 笔记标题
      table.text("content").notNullable(); // 笔记内容
      table.text("note_type").defaultTo("quick"); // 笔记类型
      table.text("category"); // 分类
      table.text("tags"); // 标签 (JSON字符串)
      table.integer("status").defaultTo(1); // 状态：1-正常，0-删除
      table.boolean("is_public").defaultTo(false); // 是否公开
      table.text("user_id"); // 用户ID
      table.integer("word_count").defaultTo(0); // 字数统计
      table.integer("char_count").defaultTo(0); // 字符数统计
      table.text("original_content"); // 原始内容
      table.text("ai_prompt"); // AI提示词
      table.text("ai_model"); // AI模型
      table.integer("quality_score"); // 质量评分
      table.text("conversation_id"); // 对话ID
      table.text("config"); // 配置信息
      table.text("remark"); // 备注
      table.timestamp("created_at").defaultTo(knex.fn.now()); // 创建时间
      table.timestamp("updated_at").defaultTo(knex.fn.now()); // 更新时间
      table.index(["user_id", "status"]);
      table.index(["note_type"]);
      table.index(["created_at"]);
    })
    .createTable("todos", (table) => {
      table.text("id").primary();
      table.text("userId").notNullable();
      table.text("content").notNullable();
      table.boolean("completed").defaultTo(false);
      table.integer("createTime");
      table.integer("updateTime");
      table.index(["userId"]);
    })
    .createTable("pinia_state", (table) => {
      table.text("userId").notNullable(); // 当前登录人id
      table.text("key").notNullable();
      table.text("value");
      table.unique(["userId", "key"]);
    });
};

exports.down = function (knex) {
  return knex.schema
    .dropTable("recent_contact")
    .dropTable("user_message")
    .dropTable("group_message")
    .dropTable("countdown")
    .dropTable("quick_notes")
    .dropTable("todos")
    .dropTable("pinia_state");
};
