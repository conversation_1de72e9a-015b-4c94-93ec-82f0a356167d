import { defineStore } from "pinia";
import { ISoftwareSetting } from "../interface/software";
import piniaPersistConfig from "../helper/persist";

const useSoftwareStore = defineStore("lark-software", {
  state: (): ISoftwareSetting => ({
    theme: "light",
    isAutoStartup: false,
    isOpenAtStartup: true,
    isShowTrayIcon: true,

    isShowSoftwareName: false,
    isShowSoftwareNameTooltip: true,
    softwareSize: 100,

    isBallShow: true,
    isBallAlwaysOnTop: true,
    ballTransparency: 100,
    ballContent: "icon",

    dialogTransparency: 100
  }),
  getters: {},
  actions: {
    /**
     * 获取软件设置
     */
    async getSoftwareSetting() {
      // 你可以从数据库或本地存储中获取软件设置
      // 这里使用假数据作为示例
      const softwareSetting = {
        // theme: "dark",
        isAutoStartup: true,
        isOpenAtStartup: false,
        isShowTrayIcon: false,

        isShowSoftwareName: true,
        isShowSoftwareNameTooltip: false,
        softwareSize: 50,

        isBallShow: false,
        isBallAlwaysOnTop: false,
        ballTransparency: 50,
        // ballContent: "text",

        dialogTransparency: 50
      };

      // 更新 store 中的状态
      this.$patch(softwareSetting);
    },

    /**
     * 设置软件设置
     * @param {ISetting} setting - 新的软件设置
     */
    setSoftwareSetting(setting: ISoftwareSetting) {
      // 更新 store 中的状态
      this.$patch(setting);
    },

    /**
     * 更新软件设置
     * @param {Partial<ISetting>} updates - 需要更新的软件设置部分
     */
    updateSoftwareSetting(updates: ISoftwareSetting) {
      // 更新 store 中的状态
      this.$patch(updates);
    }
  },
  persist: piniaPersistConfig("lark-software")
});

export default useSoftwareStore;
