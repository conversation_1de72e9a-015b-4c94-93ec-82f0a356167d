<template>
  <div class="shortcuts-container">
    <el-card class="card">
      <el-form :model="settings" label-width="120px">
        <!-- 示例设置项 -->
        <el-form-item label="主题颜色">
          <el-color-picker v-model="settings.themeColor" />
        </el-form-item>
        <el-form-item label="自动保存">
          <el-switch v-model="settings.autoSave" />
        </el-form-item>
        <el-form-item label="通知声音">
          <el-switch v-model="settings.notificationSound" />
        </el-form-item>
        <el-form-item label="默认语言">
          <el-select v-model="settings.language" placeholder="请选择语言">
            <el-option label="中文" value="zh" />
            <el-option label="English" value="en" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="saveSettings"> 保存设置 </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <!-- 拖拽区域 -->
    <div class="drop-zone" @dragover="handleDragOver" @drop="handleDrop">
      <el-icon class="drop-icon"><Upload /></el-icon>
      <p>拖拽文件到这里添加快捷方式</p>
      <el-button type="primary" @click="selectFile('file')">或点击选择文件</el-button>
    </div>

    <!-- 程序列表 -->
    <div class="shortcuts-list-container">
      <h3 v-if="softList.length > 0">我的快捷方式</h3>
      <p v-if="softList.length === 0 && !isSorting" class="empty-tip">暂无快捷方式，请添加</p>

      <!-- <draggable
        v-model="softList"
        item-key="id"
        class="shortcuts-grid"
        :disabled="sortDisable"
        :animation="200"
        @start="startSortSoftList"
        @end="sortSoftList"
      >
        <template #item="{ element, index }">
          <div
            class="shortcut-item"
            :class="{ 'is-sorting': isSorting, 'is-edit': isEdit }"
            @contextmenu="handleContextMenu(element)"
          >
            <div class="shortcut-content" @click="openFile(element.path)">
              <el-image draggable="false" :src="element.icon" fit="contain" class="shortcut-icon" />
              <div class="shortcut-name">{{ element.name }}</div>
            </div>
            <el-icon v-show="isEdit" class="delete-btn" @click.stop="deleteItem(element.id)">
              <Delete />
            </el-icon>
          </div>
        </template>
      </draggable> -->
    </div>

    <!-- 编辑图标对话框 -->
    <el-dialog v-model="dialogVisible" title="编辑快捷方式" width="500px">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入快捷方式名称"></el-input>
        </el-form-item>
        <el-form-item label="路径" prop="path">
          <el-input v-model="formData.path" placeholder="请选择文件路径">
            <template #append>
              <el-button @click="selectFile('path')">选择</el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="图标">
          <div class="icon-preview">
            <el-image :src="formData.icon" fit="contain" class="preview-icon"></el-image>
            <el-button @click="selectFile('icon')">更换图标</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveIconSettings">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRaw, reactive, computed } from "vue";
// import draggable from "vuedraggable";

import type { ISoftware } from "@/interfaces";
import { ipc } from "@/utils/ipcRenderer";
import { throttle } from "@/utils/common";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { FormInstance } from "element-plus";
import { Upload } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

// 是否为桌面组件
const props = defineProps({
  isDesktop: Boolean
});

// 是否正在排序
const isSorting = ref(false);
const sortDisable = computed(() => props.isDesktop || searchValue.value);

// 是否编辑模式
const isEdit = ref(false);

// 软件列表
const softList = ref<ISoftware[]>([]);

// 获取数据
const loadData = async () => {
  try {
    const data: ISoftware[] = await ipc.invoke(ipcApiRoute.getSoftwareList);

    if (data && Array.isArray(data) && data.length) {
      softList.value = data;
    }
  } catch (error:any) {
    ElMessage.error(error.message);
  }
};

loadData();

// 保存数据
const saveData = () => {
  ipc.send(ipcApiRoute.setSoftwareList, toRaw(softList.value));
};

// 拖拽排序
const startSortSoftList = () => {
  isSorting.value = true;
};

const sortSoftList = () => {
  saveData();
  isSorting.value = false;
};

// 拖拽文件保存
const handleDrop = (e: any) => {
  e.preventDefault();

  const { files } = e.dataTransfer;
  const fileList = Array.from(files);
  if (!fileList.length) return;

  const filePaths = fileList.map((file: any) => file.path);
  ipc.invoke(ipcApiRoute.dragToolInto, filePaths).then((files: any[]) => {
    if (files && files.length) {
      addItem(files);
    }
  });
};

const handleDragOver = (e: any) => {
  e.preventDefault();
};

// 添加图标
const addItem = (file: ISoftware[]) => {
  softList.value.push(...file);
  saveData();
};

// 删除图标
const deleteItem = (id: string) => {
  const index = softList.value.findIndex(item => item.id === id);
  if (index !== -1) {
    softList.value.splice(index, 1);
    saveData();
  }
};

// 右键菜单
// selectItem: ISoftware;
const handleContextMenu = (item: ISoftware) => {
  // let selectItem = item;
  const menuList = [
    { label: "编辑快捷方式", value: "icon-set" },
    { label: "删除快捷方式", value: "icon-delete" }
  ];
  ipc.send("show-context-menu", menuList);

  // 监听菜单项点击
  ipc.once("context-menu-click", (event: any, value: string) => {
    if (value === "icon-set") {
      openEditDialog(item);
    } else if (value === "icon-delete") {
      deleteItem(item.id);
    }
  });
};

// 点击打开软件
const openFile = throttle((path: string) => {
  if (isEdit.value) return;
  ipc.send(ipcApiRoute.openTool, path);
}, 1000);

// 图标搜索
const searchValue = ref("");
const searchSoft = (value: string) => {
  searchValue.value = value;
  searchSoftList();
};

const searchSoftList = () => {
  if (searchValue.value) {
    softList.value = toRaw(softList.value).filter(item => item.name.toLowerCase().includes(searchValue.value.toLowerCase()));
  } else {
    loadData(); // 重新加载所有数据
  }
};

// 修改图标
const formRef = ref<FormInstance>();
const dialogVisible = ref(false);
const formData = reactive({
  id: "",
  name: "",
  path: "",
  icon: ""
});

const formRules = {
  name: [{ required: true, message: "图标名称不能为空", trigger: "blur" }],
  path: [{ required: true, message: "启动路径不能为空", trigger: "blur" }]
};

const openEditDialog = (item: ISoftware) => {
  formData.id = item.id;
  formData.name = item.name;
  formData.path = item.path;
  formData.icon = item.icon;
  dialogVisible.value = true;
};

const saveIconSettings = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(valid => {
    if (valid) {
      const index = softList.value.findIndex(item => item.id === formData.id);
      if (index !== -1) {
        softList.value[index] = { ...formData };
        saveData();
        dialogVisible.value = false;
      }
    }
  });
};

const selectFile = async (type: string) => {
  const options = {
    type
  };
  ipc.invoke("dialog:openFile", options).then((file: any[]) => {
    if (!file) return;

    if (type === "path") formData.path = file[0];
    if (type === "icon") formData.icon = file[0];
    if (type === "file") addItem(file);
  });
};

interface Settings {
  themeColor: string;
  autoSave: boolean;
  notificationSound: boolean;
  language: string;
}

const settings = ref<Settings>({
  themeColor: "#409eff",
  autoSave: true,
  notificationSound: true,
  language: "zh"
});

const saveSettings = () => {
  // 保存设置逻辑
};
</script>

<style scoped>
.shortcuts-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #333;
}

.drop-zone {
  border: 2px dashed #dcdfe6;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  background-color: #f8f9fa;
  margin-bottom: 20px;
  transition: all 0.3s;
  cursor: pointer;
}

.drop-zone:hover {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.drop-icon {
  font-size: 48px;
  color: #909399;
  margin-bottom: 10px;
}

.drop-zone p {
  margin: 10px 0;
  color: #606266;
  font-size: 16px;
}

.shortcuts-list-container {
  flex: 1;
  overflow: auto;
}

.shortcuts-list-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.empty-tip {
  text-align: center;
  color: #909399;
  margin-top: 40px;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
}

.shortcut-item {
  position: relative;
  border-radius: 8px;
  padding: 15px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  cursor: pointer;
  text-align: center;
}

.shortcut-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.shortcut-item.is-sorting {
  cursor: move;
}

.shortcut-item.is-edit {
  border: 2px solid #e6a23c;
}

.shortcut-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.shortcut-icon {
  width: 48px;
  height: 48px;
  object-fit: contain;
}

.shortcut-name {
  font-size: 14px;
  color: #333;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.delete-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #f56c6c;
  color: white;
  border-radius: 50%;
  padding: 5px;
  font-size: 16px;
  cursor: pointer;
  z-index: 1;
}

.icon-preview {
  display: flex;
  align-items: center;
  gap: 15px;
}

.preview-icon {
  width: 48px;
  height: 48px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
}
</style>
