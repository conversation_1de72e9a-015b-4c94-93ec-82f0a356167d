<template>
  <svg :style="iconStyle" aria-hidden="true">
    <use :xlink:href="symbolId" />
  </svg>
</template>

<script setup lang="ts" name="SvgIcon">
import { computed, CSSProperties } from "vue";

interface SvgProps {
  name: string; // 图标的名称 ==> 必传
  prefix?: string; // 图标的前缀 ==> 非必传（默认为"icon"）
  iconStyle?: CSSProperties; // 图标的样式 ==> 非必传
}

const props = withDefaults(defineProps<SvgProps>(), {
  prefix: "icon",
  iconStyle: () => ({ width: "100%", height: "100%" })
});

const symbolId = computed(() => `#${props.prefix}-${props.name}`);
</script>

<!-- 
使用示例：
<SvgIcon name="yiwen" />
<SvgIcon name="xiala" :iconStyle="{ width: '50px', height: '50px', color: '#ff0000' }" /> 
-->
