"use strict";
const { Service } = require("ee-core");
const {
  BrowserView,
  Notification,
  shell,
  app,
  BrowserWindow,
} = require("electron");
const CoreWindow = require("ee-core/electron/window");
const fs = require("fs");
const path = require("path");
const { basename, extname, join } = require("node:path");
const Store = require("electron-store");
const Conf = require("ee-core/config");
const Ps = require("ee-core/ps");
const Log = require("ee-core/log");
const Services = require("ee-core/services");

// 持久化存储数据
const appStore = new Store({ name: "appData" });

// export const saveStore = (storeName: string, data) => {
//     appStore.set(storeName, data);
// };

// export const getStore = (storeName: string) => {
//     return appStore.get(storeName);
// };
/**
 * os（service层为单例）
 * @class
 */
class OsService extends Service {
  constructor(ctx) {
    super(ctx);
    this.myBrowserView = null;
    this.myNotification = null;
    this.configWindow = null;
  }

  /**
   * createBrowserView
   */
  createBrowserView(contentUrl) {
    // electron 实验性功能，慎用
    const win = CoreWindow.getMainWindow();
    this.myBrowserView = new BrowserView();
    win.setBrowserView(this.myBrowserView);
    this.myBrowserView.setBounds({
      x: 300,
      y: 170,
      width: 650,
      height: 400,
    });
    this.myBrowserView.webContents.loadURL(contentUrl);
  }

  /**
   * removeBrowserView
   */
  removeBrowserView() {
    // one
    this.myBrowserView.webContents.loadURL("about:blank");

    // two - electron 11 remove destroy()
    // this.myBrowserView.webContents.destroy();

    // three
    // this.myBrowserView.webContents.forcefullyCrashRenderer()

    // fore
    // this.myBrowserView.webContents.close
  }

  /**
   * createNotification
   */
  createNotification(options, event) {
    const channel = "controller.os.sendNotification";
    this.myNotification = new Notification(options);

    if (options.clickEvent) {
      this.myNotification.on("click", (e) => {
        let data = {
          type: "click",
          msg: "您点击了通知消息",
        };
        event.reply(`${channel}`, data);
      });
    }

    if (options.closeEvent) {
      this.myNotification.on("close", (e) => {
        let data = {
          type: "close",
          msg: "您关闭了通知消息",
        };
        event.reply(`${channel}`, data);
      });
    }

    this.myNotification.show();
  }

  // 根据路径获取文件详情
  getFileDetail = async (filePaths) => {
    if (!Array.isArray(filePaths)) filePaths = [filePaths];

    const allPromises = [];
    const allowPaths = [];
    const dirIndexs = [];
    for (let i = 0; i < filePaths.length; i++) {
      let path = filePaths[i];
      // 处理快捷方式
      try {
        if (/\.lnk$/.test(path)) path = shell.readShortcutLink(path).target;
      } catch (error) {
        Log.error(error);
        continue;
      }

      // 判断是否为文件夹
      if (fs.statSync(path).isDirectory()) {
        allPromises.push(
          Promise.resolve(
            nativeImage.createFromPath(imageUrl + "directory.png")
          )
        );
        allowPaths.push(path);
        dirIndexs.push(i);
      } else {
        allPromises.push(app.getFileIcon(path, { size: "large" }));
        allowPaths.push(path);
        // if (/\.(exe|bat|vbs|url)$/i.test(path)) {
        //     allPromises.push(app.getFileIcon(path, { size: 'large' }))
        //     allowPaths.push(path)
        // } else {
        //     dialog.showMessageBox({ type: 'warning', title: '不支持的文件类型', message: `请选择'exe', 'lnk', 'bat', 'vbs', 'url'文件（${path}）` })
        // }
      }
    }
    const res = await Promise.all(allPromises);
    return res.map((item, index) => {
      return {
        id: this.generateRandomId(20),
        path: allowPaths[index],
        name: dirIndexs.includes(index)
          ? basename(allowPaths[index])
          : basename(allowPaths[index], extname(allowPaths[index])),
        icon: item.toDataURL(),
        info: fs.statSync(allowPaths[index]),
      };
    });
  };

  saveToolStore(storeName, data) {
    appStore.set(storeName, data);
  }

  getToolStore(storeName) {
    return appStore.get(storeName);
  }

  // 创建独立窗口
  createChildWindow(route, opt) {
    // 拼接contentUrl
    let contentUrl = null;
    let addr = "http://localhost:8080/";
    if (Ps.isProd()) {
      const mainServer = Conf.getValue("mainServer");
      if (Conf.isFileProtocol(mainServer)) {
        addr =
          mainServer.protocol +
          path.join(Ps.getHomeDir(), mainServer.indexPath);
      } else {
        addr = mainServer.protocol + mainServer.host + ":" + mainServer.port;
      }
    }
    contentUrl = addr + route;

    const [w, h] = [600, 450];
    const option = Object.assign(
      {
        navigateOnDragDrop: true,
        movable: true,
        closable: true,
        width: w,
        minWidth: w,
        height: h,
        minHeight: h,
        center: true,
        maximizable: false,
        frame: false,
        shadow: false, // 去掉窗口阴影
        // parent: win,
        // modal: true,
        show: false,
        webPreferences: {
          nodeIntegration: true,
          contextIsolation: false,
        },
      },
      opt || {}
    );

    // 创建子窗口
    let win = new BrowserWindow(option);
    win.webContents.loadURL(contentUrl);
    win.once("ready-to-show", () => {
      win.show();
      win.focus();
    });
    return win;
  }

  // 打开设置窗口
  showConfigWindow(route) {
    if (this.configWindow) {
      this.configWindow.show();
    } else {
      this.configWindow = this.createChildWindow(route);
    }
  }
  // 关闭设置窗口
  closeConfigWindow() {
    this.configWindow.close();
    this.configWindow = null;
  }

  // 生成随机Id
  generateRandomId(length) {
    let result = "";
    const characters =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    const charactersLength = characters.length;

    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }

    return result;
  }

  tryToDo(fn, errMsg) {
    try {
      fn && fn();
    } catch (error) {
      Log.log(error);
    }
  }

  async trayNotification(message) {
    // 检查新消息通知是否启用
    const configField =
      await Services.get("database.jsondb").getAllConfigField();
    if (configField.msgNotifyEnable === false) return;

    const mainWindow = CoreWindow.getMainWindow();
    let msg;
    if (typeof message === "string") {
      try {
        msg = JSON.parse(message);
      } catch (error) {
        Log.error("Failed to parse message as JSON:", error);
        return;
      }
    } else {
      msg = message; // 如果已经是对象，直接使用
    }
    const cfg = Conf.getValue("addons.notice");

    // 根据消息类型返回对应的标签
    const getMessageTypeLabel = (code) => {
      switch (code) {
        case 1:
          return "[文件消息]";
        case 2:
          return "[图片消息]";
        case 3:
          return "[音频消息]";
        case 4:
          return "[投票消息]";
        case 5:
          return "[接龙消息]";
        case 8:
          return "[动态表情]";
        case 9:
          return "[公文消息]";
        default:
          return "";
      }
    };
    // 格式化消息内容
    const formatMessageContent = (content) => {
      if (/^face/.test(content)) return "[表情]";
      return content.substring(0, 15);
    };

    let tip = "";
    if (msg.type === 200300 || msg.contentType === 0) {
      //系统通知或文本内容
      tip = formatMessageContent(msg.content);
    } else {
      tip =
        getMessageTypeLabel(msg.contentType) ||
        formatMessageContent(msg.content);
    }

    const option = {
      title: msg.title,
      body: tip, // Limit to 20 characters
      silent: true,
      icon: "",
      timeout: 5000, // Display for 5 seconds
    };

    switch (msg.type) {
      case 200100:
        option.icon = path.join(Ps.getHomeDir(), cfg.user_icon);
        break;
      case 200200:
        option.icon = path.join(Ps.getHomeDir(), cfg.group_icon);
        break;
      case 200200:
        option.icon = path.join(Ps.getHomeDir(), cfg.system_icon);
        break;
      default:
        Log.error("Invalid message format:", msg);
        return;
    }
    const notification = new Notification(option);
    notification.on("click", () => {
      mainWindow.show();
    });
    notification.show();
  }
}

OsService.toString = () => "[class OsService]";
module.exports = OsService;
