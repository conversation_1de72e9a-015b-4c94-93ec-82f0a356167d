# EE框架 v3

# 常用命令

# 预发布模式（环境变量为：prod），请先移动资源
npm run start

# 移动前端静态资源
npm run rd

# 移动资源，可配置
npm run move

# 代码加密
npm run encrypt

# 清除加密的代码
npm run clean

# 生成logo
npm run icon

# 打包 （windows版）
npm run build-w (调整为64位)
npm run build-w-32 (32位)
npm run build-w-64 (64位)
npm run build-w-arm64 (arm64)

# 打包 （windows 免安装版）
# ee > v2.2.1
npm run build-wz (调整为64位)
npm run build-wz-32 (32位)
npm run build-wz-64 (64位)
npm run build-wz-arm64 (arm64)

# 打包 （mac版）
npm run build-m
npm run build-m-arm64 (m1芯片架构)

# 打包 （linux版）
# ee > v2.2.1
npm run build-l (默认64位 deb包)
npm run build-l-32 (32位 deb包)
npm run build-l-64 (64位 deb包)
npm run build-l-arm64 (64位 deb包 arm64)
npm run build-l-armv7l (64位 deb包 armv7l)
npm run build-lr-64 (64位 rpm包)
npm run build-lp-64 (64位 pacman包)

# 目录结构 | LARK-PRODUCT
project
├── package.json npm包配置
├── bulid 打包用的资源和脚本
    ├── icons 软件图标（打包用到）
    ├── extraResources 额外资源目录
├── electron 主进程服务
    ├── addon 插件目录
        ├── example demo插件（代码示例）
    ├── config 配置文件
        ├── bin.js 开发环境配置
        ├── config.default.js 默认配置，都会加载
        ├── config.local.js dev环境加载
        ├── config.prod.js 生产环境加载
        ├── encrypt.js 加密配置文件
        ├── builder.json 打包配置
    ├── controller 控制器
    ├── service 业务层
    ├── preload 预加载，在程序启动时加载，如托盘、自动升级等功能要提前加载代码
    ├── jobs 任务
├── frontend 前端目录（demo是用vue编写的）  
├── go go目录(可选)
├── out 打包后生成的可执行文件
    ├── latest.yml 自动升级文件
    ├── xxx.exe window应用安装包
    ├── xxx.exe.blockmap window应用增量升级包
    ├── xxx.dmg mac应用安装包
    ├── xxx.deb linux应用安装包后缀有多种    
├── logs 日志 
├── main.js 入口文件 
├── public 资源目录
    ├── dist 前端资源会移动到这里，生产环境加载
    ├── electron 业务js加密后的文件
    ├── html 一些模板
    ├── images 一些图片
├── data 内置数据库文件
    ├── system.json 框架使用的数据库
    ├── demo.json 示例json数据库
    ├── sqlite-demo.db 示例sqlite数据库




基础 config 文件
支持 json、json5、js、cjs、ts配置文件。

# 位置
./electron/config/

# 说明
bin.json          // 开发配置
config.default.js // 默认配置文件，开发环境和生产环境都会加载
config.local.js   // 开发环境配置文件，追加和覆盖default配置文件
config.prod.js    // 生产环境配置文件，追加和覆盖default配置文件
encrypt.js        // 代码加密的配置
nodemon.json      // 开发环境，代码（监控）热加载
builder.json      // 打包配置
# 参数：appInfo 对象
name 应用名称
baseDir 框架中electron目录路径，如果使用加密功能，打包后路径为 ./public/electron
env 环境变量，local-本地，prod-生产环境
home APP根目录路径
root APP在操作系统中的数据目录，根据环境变量而变化
appUserDataDir APP在操作系统中的数据目录，与环境变量无关
userHome 操作系统用户的home目录
appVersion APP版本号
isPackaged APP是否已经打包
execDir APP安装后，可执行程序（exe、dmg、dep）的根目录
#属性说明
#开发者工具
也可以使用openAppMenu来调出开发者工具栏。

config.openDevTools = false;
# 应用程序顶部菜单
* boolean | string
* true, false, 'dev-show'(dev环境显示，prod环境隐藏)
config.openAppMenu = true;
#主窗口
// 更多属性，见文档：https://www.electronjs.org/zh/docs/latest/api/browser-window#new-browserwindowoptions
config.windowsOption = {
  title: 'EE框架', // 软件顶部或左上角名称(会被 html中的 title标签覆盖)
  width: 980, // 软件窗口宽度
  height: 650, // 软件窗口高度
  minWidth: 800, // 软件窗口最小宽度
  minHeight: 650, // 软件窗口最小高度
  webPreferences: {
    //webSecurity: false, // 如果需要跨域，请打开注释
    contextIsolation: false, // 设置此项为false后，才可在渲染进程中使用electron api
    nodeIntegration: true, // node模块
    //preload: path.join(appInfo.baseDir, 'preload', 'bridge.js'),
  },
  frame: true,
  show: true, 
	icon: path.join(appInfo.home, 'public', 'images', 'logo-32.png'),
};
# 业务日志
config.logger = {
  encoding: 'utf8',
  level: 'INFO',
  outputJSON: false,
  buffer: true,
  enablePerformanceTimer: false,
  rotator: 'day', // day:按天切割 | none:无
  appLogName: 'ee.log',
  coreLogName: 'ee-core.log',
  errorLogName: 'ee-error.log' 
}
# 远程web地址 (可选)
config.remoteUrl = {
  enable: false, // 是否启用
  url: 'http://electron-egg.kaka996.com/' // Any web url
};
# 内置socket服务
# 第三方软件，可通过socket-client监听端口，与ee框架通信
config.socketServer = {
  enable: false, // 是否启用
  port: 7070, // 默认端口
  isDynamic: false, // 如果值为false，框架默认使用port端口（如果默认端口被使用，则随机获取一个）；如果为true，默认端口无效，框架随机生成
  path: "/socket.io/", // 默认路径名称
  connectTimeout: 45000, // 客户端连接超时时间
  pingTimeout: 30000, // 心跳检测超时时间
  pingInterval: 25000, // 心跳检测间隔
  maxHttpBufferSize: 1e8, // 每条消息的数据最大值 1M
  transports: ["polling", "websocket"], // http轮询和websocket
  cors: {
    origin: true, // http协议时，要设置允许跨域
  },
  channel: 'c1'   // 自定义通道名称，默认c1
};
# 内置http服务
# 可在前端、浏览器、终端命令中，访问EE程序  
config.httpServer = {
  enable: false, // 是否启用
  https: {
    enable: false, 
    key: '/public/ssl/localhost+1.key', // key文件
    cert: '/public/ssl/localhost+1.pem' // cert文件
  },
  port: 7071, // 默认端口（如果端口被使用，则随机获取一个）
  cors: {
    origin: "*" // 跨域
  },
  body: {
    multipart: true,
    formidable: {
      keepExtensions: true
    }
  },
  filterRequest: {
    uris:  [
      'favicon.ico' // 要过滤的请求uri
    ],
    returnData: '' // 任意数据类型
  }
};
# 主进程
config.mainServer = {
  // 协议：http:// | https:// | file://
  protocol: 'file://',
  // 前端资源入口文件
  indexPath: '/public/dist/index.html',
  host: 'localhost',
  port: 7072, // 默认端口（如果端口被使用，则随机获取一个）
  open: false, // 是否开放0.0.0.0，默认关闭
	ssl: {
    key: '/public/ssl/localhost+1.key', // key文件
    cert: '/public/ssl/localhost+1.pem' // cert文件
  },
  // 兼容electron参数
  // https://www.electronjs.org/zh/docs/latest/api/browser-window#winloadurlurl-options
  options: {},
  // 加载一个loading页，一般不用开启
  loadingPage: '/public/html/loading.html',
  // 接管。如果想加载一个go web程序，来替代 protocol
  takeover: 'go'
}; 
# 跨语言服务
ee-core@v2.8.0

运行其它语言的可执行程序。

config.cross = {
  // 自定义服务名
  go: {
    // 是否开启
    enable: true,
    // 可执行程序名
    name: 'goapp',
    // 参数
    args: ['--port=7073'],
    // 可执行程序退出时，是否退出整个桌面程序
    appExit: true,
  }
}; 
# 异常捕获
进程捕获异常后，是否退出。

config.exception = {
  // 主进程
  mainExit: false,
  // jobs 子进程
  childExit: true,
};
# jobs 任务
config.jobs = {
  // 是否 打印/记录 进程间通信的消息log
  messageLog: true
};  
# 插件功能
插件包含一些基础功能，如果开发者需要自定义效果，请自行修改 ./electron/addon 相关代码。

/**
 * 插件功能
 * @param window 官方内置插件
 * @param tray 托盘插件
 * @param security 安全插件
 * @param awaken 唤醒插件
 * @param autoUpdater 自动升级插件
 * @param javaServer java项目插件
 * @param example demo插件
 */
config.addons = {
  window: {
    enable: true,
  },
  tray: {
    enable: true,
    title: 'EE程序',
    icon: '/public/images/tray_logo.png'
  },
  security: {
    enable: true,
  },
  awaken: {
    enable: true,
    protocol: 'ee',
    args: []
  },
  autoUpdater: {
    enable: true,
    // windows
    windows: false, 
    // macOs 需要签名验证
    macOS: false, 
    // linux
    linux: false,
    options: {
      // or github, s3, bintray
      provider: 'generic', 
      // resource dir, end with '/'
      url: 'http://kodo.qiniu.com/'
    },
    // 强制更新（运行软件时，检查新版本并后台下载安装）
    force: false,
  },
  // 废弃，请参考跨语言服务
  javaServer: {
    enable: false,
    port: 18080,
    jreVersion: 'jre1.8.0_201',
    opt: '-server -Xms512M -Xmx512M -Xss512k -Dspring.profiles.active=prod -Dserver.port=${port} -Dlogging.file.path="${path}" ',
    name: 'java-app.jar'
  },
  example: {
    enable: true,
  },
};

# 加密
/**
 * 加密配置
 * @param type - confusion | bytecode | strict
 */
module.exports = {
  // confusion - 压缩混淆加密
  // bytecode - 字节码加密
  // strict - 先混淆加密，然后字节码加密 
  type: 'confusion',
  // 文件匹配
  files: [
    'electron/**/*.(js|json)',
    '!electron/config/encrypt.js',
    '!electron/config/nodemon.json',
    '!electron/config/builder.json',
    '!electron/config/bin.json',
  ],
  // 需要加密的文件后缀，暂时只支持js，后续待扩展
  fileExt: ['.js'],
  // 混淆加密配置
  confusionOptions: {
    // 压缩成一行
    compact: true, 
    // 删除字符串文字并将其放置在一个特殊数组中     
    stringArray: true,
    // 对stringArray的所有字符串文字进行编码，值：'none' | 'base64' | 'rc4'
    stringArrayEncoding: ['none'],
    // 注入死代码，注：影响性能
    deadCodeInjection: false,
  }
}
# 打包功能使用
builder.json
打包功能使用electron-builder (opens new window)包

{
  // 可执行程序名称（英文)
  // 不要为中文，避免一些未知异常，打包后可修改安装包名称为中文
  "productName": "ee",
  // 软件id
  "appId": "com.electron.ee",
  // 版权标识（换成您自己的）
  "copyright": "© 2023 哆啦好梦 Technology Co., Ltd.",
  // 安装包输出目录
  "directories": {
    "output": "out"
  },
  // asar加密
  "asar": true,
  // 需要打包的文件
  "files": [
    "**/*",
    "!frontend/",
    "!run/",
    "!logs/",
    "!data/"
  ],
  // 额外资源（将from文件夹打进安装包，软件安装后放到to文件夹）
  "extraResources": {
    "from": "build/extraResources/",
    "to": "extraResources"
  },
  // nsis脚本
  "nsis": {
    // 一键安装
    "oneClick": false,
    "allowElevation": true,
    // 允许改变安装目录
    "allowToChangeInstallationDirectory": true,
    // 安装图标
    "installerIcon": "build/icons/icon.ico",
    // 卸载图标
    "uninstallerIcon": "build/icons/icon.ico",
    // 安装时header图标图标
    "installerHeaderIcon": "build/icons/icon.ico",
    // 创建桌面快捷方式
    "createDesktopShortcut": true,
    // 创建开始菜单快捷方式
    "createStartMenuShortcut": true,
    // 桌面快捷方式名称
    "shortcutName": "EE框架"
  },
  // MacOS
  "mac": {
    "icon": "build/icons/icon.icns",
    "artifactName": "${productName}-${os}-${version}-${arch}.${ext}",
    "darkModeSupport": true,
    "hardenedRuntime": false
  },
  // windows
  "win": {
    "icon": "build/icons/icon.ico",
    "artifactName": "${productName}-${os}-${version}-${arch}.${ext}",
    "target": [
      {
        "target": "nsis"
      }
    ]
  },
  // linux
  "linux": {
    "icon": "build/icons/icon.icns",
    "artifactName": "${productName}-${os}-${version}-${arch}.${ext}",
    "target": [
      "deb"
    ],
    "category": "Utility"
  }
}
