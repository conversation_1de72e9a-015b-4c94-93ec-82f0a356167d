<template>
  <div class="tab-content">
    <div class="tab-content-menu">
      <div class="flex flex-col w-full space-y-1">
        <button :class="{ active: chatType == 'all' }" @click="chatType = 'all'">
          <Icon :icon="dialogBoldDuotone" width="16" height="16" />
          <span>全部消息</span>
        </button>
        <button :class="{ active: chatType == 'unread' }" @click="chatType = 'unread'">
          <Icon :icon="chatRoundUnreadBoldDuotone" width="16" height="16" />
          <span>未读消息</span>
          <span
            class="ml-auto w-4 h-4 flex justify-center rounded-full bg-red-500 text-xs font-medium text-white"
            v-if="unreadTotalNum > 0"
          >
            {{ unreadTotalNum }}
          </span>
        </button>
        <button :class="{ active: chatType == 'group' }" @click="chatType = 'group'">
          <Icon :icon="peopleNearbyBoldDuotone" width="16" height="16" />
          <span>群组消息</span>
        </button>
      </div>
    </div>
    <div class="tab-content-br"></div>
    <el-scrollbar>
      <div
        v-for="(item, index) in listData"
        :key="item.contactId"
        @click="openChat(item.contactId)"
        @contextmenu.prevent="showContextMenu(index)"
      >
        <el-dropdown trigger="contextmenu" placement="bottom-end" class="!block w-full">
          <div class="chat-item" :class="[{ active: talkStore.activeChatId === item.contactId }, item.isTop ? 'activeTop' : '']">
            <div class="flex items-center w-full px-4">
              <div v-if="item.secret && item.secret <= 30" class="secret-bar secret-bar-green"></div>
              <div v-else-if="item.secret && item.secret <= 33" class="secret-bar secret-bar-yellow"></div>
              <div v-else-if="item.secret" class="secret-bar secret-bar-orange"></div>
              <div @click.stop>
                <DynamicAvatar
                  :id="item.contactId"
                  :data-info="item"
                  :relation-name="item.contactName"
                  :type-avatar="item.chatType"
                  :size="40"
                />
              </div>
              <div class="flex-1 ml-3 min-w-0">
                <div class="flex items-center justify-between mb-1">
                  <div class="flex items-center">
                    <h4 class="chat-item-name text-sm font-medium text-gray-900 truncate">{{ item.contactName }}</h4>
                    <Icon
                      :icon="medalRibbonsStarBoldDuotone"
                      width="16"
                      height="16"
                      style="color: #eaa800"
                      class="ml-1 flex-shrink-0"
                      v-if="item.chatType == 2"
                    />
                  </div>
                  <div class="flex items-center gap-1">
                    <span class="chat-item-time text-xs text-gray-500">{{ formatDate(item.updateTime) }}</span>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <p
                    class="truncate items-center fontStyle conimg"
                    :class="{ warningFilled: item.recentMsgId?.startsWith('offlineMsg_') && item.senderId == userStore.userId }"
                    v-if="item.recentMsgType == 0"
                    v-html="transform(item.recentMsg)"
                  ></p>
                  <p v-if="item.recentMsgType == 1" class="fontStyle">[文件消息]</p>
                  <p v-if="item.recentMsgType == 2" class="fontStyle">[图片消息]</p>
                  <p v-if="item.recentMsgType == 3" class="fontStyle">[语音消息]</p>
                  <p v-if="item.recentMsgType == 4" class="fontStyle">[投票消息]</p>
                  <p v-if="item.recentMsgType == 5" class="fontStyle">[接龙消息]</p>
                  <p v-if="item.recentMsgType == 9" class="fontStyle">[公文消息]</p>
                  <p v-if="item.recentMsgType == 7" class="fontStyle fontHIdden">{{ item.recentMsg }}</p>
                  <p
                    v-if="item.recentMsgType == 8"
                    class="h-5"
                    :class="{ warningFilled: item.recentMsgId?.startsWith('offlineMsg_') && item.senderId == userStore.userId }"
                    v-html="transformCasicEmo(item.recentMsg)"
                  ></p>
                  <span class="flex shrink-0 items-center justify-center">
                    <span
                      class="ml-1 px-1 min-w-4 flex shrink-0 items-center justify-center rounded-full bg-red-500 text-xs font-medium text-white"
                      v-if="item.isAt == 1 && item.atIds.includes(userStore.userId)"
                    >
                      有人@我
                    </span>
                    <span
                      class="ml-1 min-w-4 flex shrink-0 items-center justify-center rounded-full bg-red-500 text-xs font-medium text-white"
                      v-if="item.unreadNum > 0"
                    >
                      {{ item.unreadNum }}
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <!-- 下拉菜单内容 -->
          <template #dropdown>
            <el-dropdown-menu v-if="activeIndex === index">
              <el-dropdown-item @click="topRecentChat(item.id)" v-if="item.isTop">取消置顶</el-dropdown-item>
              <el-dropdown-item @click="topRecentChat(item.id)" v-else>置顶</el-dropdown-item>
              <el-dropdown-item @click="deleteRecentChat(item.id)">删除</el-dropdown-item>
              <el-dropdown-item @click="deleteRecentChat('all')">全部删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div v-if="listData.length == 0" class="p-20">
        <NoData />
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch, nextTick } from "vue";
import { useTalkStore } from "@/stores/modules/talk";
import { useRecentStore } from "@/stores/modules/recent";
import { useUserStore } from "@/stores/modules/user";
import { useHistoryStore } from "@/stores/modules/history";
import * as recentsApi from "@/api/modules/recents";
import * as historyApi from "@/api/modules/history";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import dayjs from "dayjs";
import NoData from "@/components/NoData/index.vue";
import { isEE, ipc } from "@/utils/ipcRenderer";
import { Icon } from "@iconify/vue";
import { PerformanceMonitor } from "@/utils/performance";
// 导入离线图标
import dialogBoldDuotone from "@iconify-icons/solar/dialog-bold-duotone";
import chatRoundUnreadBoldDuotone from "@iconify-icons/solar/chat-round-unread-bold-duotone";
import peopleNearbyBoldDuotone from "@iconify-icons/solar/people-nearby-bold-duotone";
import medalRibbonsStarBoldDuotone from "@iconify-icons/solar/medal-ribbons-star-bold-duotone";
import { ElMessage } from "element-plus";

const talkStore = useTalkStore();
const recentStore = useRecentStore();
const userStore = useUserStore();
const historyStore = useHistoryStore();
const chatType = ref("all");
const activeIndex = ref(null);

const showContextMenu = (index: any) => {
  activeIndex.value = index;
};

const transform = (content: any) => {
  const fa = talkStore.commonEmoji;
  if (content) {
    content = content.replace(/face\[([^\s\[\]]+?)]/g, function (face: any) {
      // 转义表情
      const altStr = face.replace(/^face/g, "");
      const isFace = talkStore.commonEmojiList.includes(altStr);
      if (isFace) {
        return '<img width="20px" height="20px" src="' + fa[altStr] + '">';
      }
    });
  }
  return content;
};
const transformCasicEmo = (content: any) => {
  const fa = talkStore.casicEmoji;
  if (content) {
    content = content.replace(/casic\[([^\s\[\]]+?)]/g, function (face: any) {
      // 转义表情
      const altStr = face.replace("casic", "").replace("[", "").replace("]", "");
      const isFace = talkStore.casicEmojiList.includes(altStr);
      if (isFace) {
        return '<img width="20px" height="20px" src="' + fa[altStr] + '">';
      }
    });
  }
  return content;
};
onMounted(async () => {
  try {
    // 获取表情包
    talkStore.getCommonEmoji();
    talkStore.getCasicEmoji();
    // 只有在没有数据时才加载，避免不必要的重新加载
    if (recentStore.listRecents.length === 0) {
      await recentStore.getListRecents();
    }
  } catch (error: any) {
    ElMessage.error(error.message);
  }
});

const listData = computed(() => {
  if (chatType.value == "all") {
    return recentStore.listRecents;
  } else if (chatType.value == "unread") {
    return recentStore.listRecents.filter((item: any) => item.unreadNum > 0);
  } else {
    return recentStore.listRecents.filter((item: any) => item.chatType !== 0);
  }
});

const unreadTotalNum = computed(() => {
  return recentStore.listRecents.reduce((acc, curr) => acc + curr.unreadNum, 0);
});

// 如果消息已全部读取，清除闪烁效果
if (isEE) {
  watch(
    () => unreadTotalNum.value,
    val => {
      if (val === 0) {
        ipc.send("message-stop-blinking");
      }
    },
    { immediate: true }
  );
}

const openChat = (id: string) => {
  // 防止重复点击同一个聊天
  if (talkStore.activeChatId === id && talkStore.ifChat) {
    return;
  }

  // 开始性能监控
  PerformanceMonitor.start(`聊天切换-${id}`);

  // 🚀 立即响应：先切换UI状态，不等待数据加载
  talkStore.ifChat = true;
  talkStore.ifContact = false;
  talkStore.setActiveChat(id);
  talkStore.setQuoteMsg();

  // 立即结束性能监控（UI已响应）
  nextTick(() => {
    PerformanceMonitor.end(`聊天切换-${id}`);
  });

  // 🔄 后台异步加载数据（不阻塞UI）
  // loadChatDataInBackground(id);
};

// 后台异步加载聊天数据
const loadChatDataInBackground = async (id: string) => {
  try {
    // 如果数据已存在且是最新的，跳过加载
    const contact = recentStore.listRecents.find((item: any) => item.contactId === id);
    const existingHistory = historyStore.msgHistory[id];
    const isDataFresh =
      existingHistory && existingHistory.length > 0 && contact?.recentMsgId === existingHistory[existingHistory.length - 1]?.id;

    if (isDataFresh) {
      return;
    }

    // 后台加载数据
    if (contact) {
      const params = {
        receiver: id,
        sender: userStore.userId,
        pageNo: 1,
        pageSize: 20
      };

      let res: any = [];
      if (contact.chatType) {
        res = await historyApi.getGroupMsgHistory(params);
      } else {
        res = await historyApi.getUserMsgHistory(params);
      }

      if (res.code == 0) {
        res.data.list.forEach((item: any) => {
          item.contactId = item.sender;
          historyStore.setMsgHistory(id, item.id, item);
        });
      }
    }

    // 异步执行其他操作
    Promise.allSettled([recentStore.clearUnreadNum(id), recentStore.clearAtNum(id)]);
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};

const topRecentChat = async (id: string) => {
  const params = {
    id: id
  };
  const res: any = await recentsApi.topRecentChat(params);
  if (res.code == 0) {
    recentStore.topListRecents(id, res.data.isTop);
    if (res.data.isTop) {
      ElMessage.success("置顶成功");
    } else {
      ElMessage.success("取消置顶");
    }
  }
};

const deleteRecentChat = async (id: string) => {
  await recentStore.deleteListRecents(id);
  if (talkStore.activeContact.id == id) talkStore.ifChat = false;
};

const formatDate = (timestamp: string) => {
  if (!timestamp) return "";
  let str;
  const date = new Date(timestamp);
  const date_ = date.getTime();
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
  const beforeYesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 2);
  const monday = new Date(today);
  monday.setDate(today.getDate() - (today.getDay() ? today.getDay() - 1 : 6));
  const week = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
  const timeStr = dayjs(timestamp).format("YYYY年M月D日 HH:mm:ss");
  if (date_ > today.getTime()) {
    str = timeStr.split(" ")[1].slice(0, 5);
  } else if (date_ > yesterday.getTime()) {
    str = "昨天";
  } else if (date_ > beforeYesterday.getTime()) {
    str = "前天";
  } else if (date_ > monday.getTime()) {
    str = week[date.getDay()];
  } else {
    if (date.getFullYear() == today.getFullYear()) {
      str = timeStr.split(" ")[0].slice(5);
    } else {
      str = timeStr.split(" ")[0];
    }
  }

  return str;
};
</script>

<style scoped lang="scss">
.warningFilled {
  padding-right: 18px;
  position: relative;
  &::after {
    content: "!";
    display: block;
    text-align: center;
    line-height: 12px;
    width: 12px;
    height: 12px;
    border-radius: 14px;
    background-color: #d30303;
    font-size: 10px;
    color: #fff;
    font-weight: bold;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
  }
}
.tab-content {
  height: calc(100vh - 88px);
  display: flex;
  flex-direction: column;
  .tab-content-menu {
    @apply p-2 bg-white;
    button {
      @apply flex items-center gap-2 px-4 h-8 text-sm font-medium w-full transition-colors duration-200 rounded-md cursor-pointer text-gray-500 hover:bg-gray-100;
    }
    button.active {
      @apply bg-blue-100 text-blue-600;
    }
  }
  .tab-content-br {
    @apply border-t border-gray-200 w-full;
  }
  .chat-item {
    @apply flex w-full items-center justify-between border-b border-gray-200/70 px-0 py-2 text-left transition-all duration-200 cursor-pointer hover:bg-blue-50 relative overflow-hidden;
  }
  .chat-item.active {
    @apply bg-blue-100 shadow-sm;
  }
  .chat-item.activeTop {
    @apply bg-blue-50;
  }
}
.dark {
  .tab-content {
    .tab-content-menu {
      @apply bg-gray-800;
      button {
        @apply text-gray-400 hover:bg-gray-700;
      }
      button.active {
        @apply bg-blue-900 text-blue-400;
      }
    }
    .tab-content-br {
      @apply border-gray-700;
    }
    .chat-item {
      @apply border-gray-700/70 hover:bg-blue-900/30;
    }
    .chat-item.active {
      @apply bg-blue-900/40;
    }
    .chat-item-name {
      @apply text-white;
    }
    .chat-item-time {
      @apply text-gray-400;
    }
  }
}
.fontStyle {
  @apply truncate text-xs text-gray-500 dark:text-gray-400;
}
.fontHIdden {
  @apply overflow-hidden text-ellipsis whitespace-nowrap;
}

.el-scrollbar {
  flex: 1;
  height: 0;
}

.conimg {
  :deep(img) {
    display: inline !important;
  }
}

/* 密级光条渐进色样式 */
.secret-bar {
  @apply absolute left-0 top-0 h-full w-1;
}

.secret-bar-green {
  background: linear-gradient(180deg, rgba(34, 197, 94, 0) 0%, rgba(34, 197, 94, 0.5) 50%, rgba(34, 197, 94, 0) 100%);
}

.secret-bar-yellow {
  background: linear-gradient(180deg, rgba(234, 179, 8, 0) 0%, rgba(234, 179, 8, 0.5) 50%, rgba(234, 179, 8, 0) 100%);
}

.secret-bar-orange {
  background: linear-gradient(180deg, rgba(154, 52, 18, 0) 0%, rgba(154, 52, 18, 0.5) 50%, rgba(154, 52, 18, 0) 100%);
}
</style>
