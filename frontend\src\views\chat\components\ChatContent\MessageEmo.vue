<template>
  <div>
    <span
      class="text-sm font-normal"
      :class="{ 'drop-shadow-sm': props.dataItem.sender == userStore.userId }"
      v-html="transform(props.dataItem.msg)"
    ></span>
  </div>
</template>

<script setup>
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";

const userStore = useUserStore();
const talkStore = useTalkStore();

const transform = content => {
  const fa = talkStore.casicEmoji;
  if (content) {
    content = content.replace(/casic\[([^\s\[\]]+?)]/g, function (face) {
      // 转义表情
      const altStr = face.replace("casic", "").replace("[", "").replace("]", "");
      const isFace = talkStore.casicEmojiList.includes(altStr);
      if (isFace) {
        return '<img width="120px" height="120px" src="' + fa[altStr] + '">';
      }
    });
  }
  return content;
};

const props = defineProps({
  dataItem: {
    type: Object,
    required: true
  }
});
</script>
