<template>
  <div class="secret-level-slider">
    <div class="slider-tabs">
      <div class="slider-track">
        <div class="slider-indicator" :style="sliderStyle"></div>
      </div>
      <div
        v-for="(level, index) in availableLevels"
        :key="level.value"
        class="slider-tab"
        :style="sliderbox"
        :class="[level.className, { active: modelValue === level.value }]"
        @click="selectLevel(level, index)"
      >
        {{ level.label }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { SecretLevelConverter } from "@/utils/secretLevelConverter";

interface Props {
  modelValue: number;
  contactSecret: number;
}

interface Emits {
  (e: "update:modelValue", value: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 根据会话密级计算可用的密级选项
// 用户可以选择不高于会话密级的所有密级
const availableLevels = computed(() => {
  return SecretLevelConverter.getOptions("obj").filter((item: any) => item.value <= props.contactSecret);
});

// 当前选中密级的索引
const currentIndex = computed(() => {
  const index = availableLevels.value.findIndex(item => item.value === props.modelValue);
  return index === -1 ? 0 : index;
});

// 滑块指示器样式
const sliderStyle = computed(() => {
  const tabWidth = 100 / availableLevels.value.length;
  return {
    width: "60px",
    transform: `translateX(${currentIndex.value * 100}%)`
  };
});
const sliderbox = computed(() => {
  const tabWidth = 100 / availableLevels.value.length;
  return {
    width: "60px"
  };
});
// 选择密级
const selectLevel = (level: any, _index: number) => {
  emit("update:modelValue", level.value);
};

// onMounted(() => {
// 如果没有选中值，默认选择最低密级
// if (!props.modelValue && availableLevels.value.length > 0) {
//   emit("update:modelValue", availableLevels.value[0].value);
// }
// });
</script>

<style lang="scss" scoped>
.secret-level-slider {
  @apply w-full;

  .slider-tabs {
    @apply relative w-full bg-gray-100 rounded-lg p-1 flex;

    .slider-track {
      @apply absolute inset-1  pointer-events-none flex;

      .slider-indicator {
        @apply h-full bg-white rounded-md shadow-sm transition-transform duration-300 ease-out;
      }
    }

    .slider-tab {
      @apply relative z-10 py-2 text-xs font-medium cursor-pointer transition-colors duration-200;
      color: #6b7280;
      display: inline-block;
      text-align: center;

      &:hover {
        color: #374151;
      }

      &.active {
        color: #3b82f6;
        font-weight: 600;
      }

      // 密级特定样式
      &.feimi.active {
        color: #059669; // 绿色
      }

      &.mimi.active {
        color: #d97706; // 黄色
      }

      &.jimi.active {
        color: #dc2626; // 橙红色
      }
      &.putongshangmi.active {
        @apply text-fuchsia-600;
      }
      &.hexinshangmi.active {
        @apply text-rose-400;
      }
      &.neibu.active {
        @apply text-lime-600;
      }
    }
  }
}

/* 暗色主题样式 */
.dark .secret-level-slider {
  .slider-tabs {
    @apply bg-gray-800;

    .slider-indicator {
      @apply bg-gray-700;
    }

    .slider-tab {
      color: #9ca3af;

      &:hover {
        color: #f3f4f6;
      }

      &.active {
        color: #60a5fa;
      }

      // 暗色主题下的密级特定样式
      &.feimi.active {
        color: #10b981; // 绿色
      }

      &.mimi.active {
        color: #f59e0b; // 黄色
      }

      &.jimi.active {
        color: #f87171; // 橙红色
      }
    }
  }
}
</style>
