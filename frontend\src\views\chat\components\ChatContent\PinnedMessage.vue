<template>
  <div class="pin-box sticky top-0 z-20 rounded-lg border border-gray-200 bg-white px-4 py-2 shadow-sm">
    <div class="flex items-center justify-between mb-2">
      <div class="pin-title flex items-center">
        <font-awesome-icon :icon="['fas', 'thumbtack']" class="thumbtack mr-2 size-5 text-blue-500" />
        <h3 className="text-md font-medium text-gray-900">
          {{ `置顶${pinTitle[pinnedTopic?.msgType]}` }}
        </h3>
      </div>
      <button
        @click="handleTogglePinTopic(pinnedTopic)"
        class="unpin-btn w-8 h-8 rounded-full text-gray-500 hover:bg-gray-100 hover:text-gray-700"
        title="取消置顶"
      >
        <font-awesome-icon :icon="['fas', 'thumbtack']" class="w-4 h-4" />
      </button>
    </div>
    <div class="pin-content text-sm text-gray-600">
      <!-- 文本 -->
      <MessageText :data-item="pinnedTopic" v-if="pinnedTopic.msgType == 0" />
      <!-- 表情 -->
      <MessageEmo :data-item="pinnedTopic" v-if="pinnedTopic.msgType == 8" />
      <!-- 文件 -->
      <MessageFile :data-item="pinnedTopic" v-if="pinnedTopic.msgType == 1" />
      <!-- 图片 -->
      <MessageImg :data-item="pinnedTopic" v-if="pinnedTopic.msgType == 2" />
      <!-- 音频 -->
      <MessageAudio :data-item="pinnedTopic" v-if="pinnedTopic.msgType == 3" />
      <!-- 投票 -->
      <div
        v-if="pinnedTopic.msgType == 4"
        @click="openDialog(pinnedTopic)"
        class="flex items-center justify-between px-4 py-2 rounded-md border-b border-gray-200/70 hover:bg-blue-50 cursor-pointer"
      >
        <h4 class="text-base font-semibold text-gray-900">{{ getActivityName(pinnedTopic)?.title }}</h4>
        <span class="text-sm text-gray-600">{{ getActivityName(pinnedTopic)?.status == "0" ? "进行中" : "已结束" }}</span>
      </div>
      <!-- 接龙 -->
      <div
        v-if="pinnedTopic.msgType == 5"
        @click="openDialog(pinnedTopic)"
        class="flex items-center justify-between px-4 py-2 rounded-md border-b border-gray-200/70 hover:bg-blue-50 cursor-pointer"
      >
        <h4 class="text-base font-semibold text-gray-900">{{ getActivityName(pinnedTopic)?.title }}</h4>
        <span class="text-sm text-gray-600">{{ getActivityName(pinnedTopic)?.status == "0" ? "进行中" : "已结束" }}</span>
      </div>
      <!-- 公文 -->
      <div
        v-if="pinnedTopic.msgType == 9"
        @click="openDialog(pinnedTopic)"
        class="flex items-center justify-between px-4 py-2 border-b border-gray-200/70 hover:bg-blue-50 cursor-pointer"
      >
        <h4 class="text-base font-semibold text-gray-900">{{ getActivityName(pinnedTopic)?.title }}</h4>
        <!-- <span class="mt-2 text-sm text-gray-600">{{ getActivityName(pinnedTopic)?.status == "0" ? "未学习" : "已学习" }}</span> -->
      </div>
    </div>
    <div class="pin-creator mt-2 flex items-center text-xs text-gray-500">
      <span> 由 {{ pinnedTopic.senderName }} 创建于 {{ dayjs(pinnedTopic.createTime).format("YYYY-MM-DD HH:mm:ss") }} </span>
    </div>
    <el-dialog v-model="listVisible" title="群组活动" class="custom-dialog" :modal="false">
      <MessageVote :vote-id="String(pinnedTopic.msg)" v-if="pinnedTopic.msgType == 4" />
      <MessageRelay :relay-id="String(pinnedTopic.msg)" v-else-if="pinnedTopic.msgType == 5" />
      <MessageOfficialDoc :doc-id="String(pinnedTopic.msg)" v-else />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import dayjs from "dayjs";
import MessageImg from "./MessageImg.vue";
import MessageFile from "./MessageFile.vue";
import MessageAudio from "./MessageAudio.vue";
import MessageText from "./MessageText.vue";
import MessageEmo from "./MessageEmo.vue";
import MessageVote from "./MessageVote.vue";
import MessageRelay from "./MessageRelay.vue";
import MessageOfficialDoc from "./MessageOfficialDoc.vue";
import { getBusinessList } from "@/api/modules/business";

const pinTitle = ["消息", "文件", "图片", "音频", "投票", "接龙", "", "", "", "公文"];
const listVisible = ref(false);
const votesList: any = ref([]); //投票
const relayList: any = ref([]); //接龙
const officialDocList: any = ref([]); //公文
const current: any = ref({});
const openDialog = (data: any) => {
  listVisible.value = true;
  current.value = data;
};
const props = defineProps({
  pinnedTopic: {
    type: Object,
    required: true
  }
});

onMounted(async () => {
  if (Object.keys(props.pinnedTopic).length !== 0) {
    const res: any = await getBusinessList(props.pinnedTopic.receiver);
    if (res.code == 0) {
      votesList.value = res.data.votesList;
      relayList.value = res.data.relayList;
      officialDocList.value = res.data.messageOfficialList;
    }
  }
});
const getActivityName = (data: any) => {
  if (data?.msgType === 5) {
    return relayList.value.find((item: any) => item.id == data.msg);
  } else if (data?.msgType === 4) {
    return votesList.value.find((item: any) => item.id == data.msg);
  } else {
    return officialDocList.value.find((item: any) => item.id == data.msg);
  }
};
watch(
  () => getActivityName(props.pinnedTopic)?.status,
  (_newVal, _oldVal) => {},
  { deep: true } // 深度监听
);
const emit = defineEmits(["unpin-click"]);

const handleTogglePinTopic = (data: any) => {
  emit("unpin-click", data);
};
</script>

<style lang="scss" scoped>
.dark {
  .pin-box {
    @apply border-gray-700 bg-gray-800;
    .pin-title {
      .thumbtack {
        @apply text-blue-400;
      }
      h3 {
        @apply text-white;
      }
    }
    .unpin-btn {
      @apply text-gray-400 hover:bg-gray-700 hover:text-gray-300;
    }
    .pin-content,
    .pin-creator {
      @apply text-gray-400;
    }
  }
}
</style>
