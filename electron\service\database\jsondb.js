"use strict";

const { Service } = require("ee-core");
const appjson = require("../../database/appjson");
const Ps = require("ee-core/ps");

/**
 * json数据存储
 * @class
 */
class JsondbService extends Service {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 初始化数据
   * @param {*} data
   * @returns
   */
  createInitAppData(initdata) {
    if (!appjson.getAppData()) {
      appjson.setAppData(initdata);
    }
  }

  /**
   * 用户缓存相关
   */
  setItem(name, value) {
    appjson.appInfoDB.setItem(name, value);
  }
  getItem(name) {
    return appjson.appInfoDB.getItem(name);
  }
  removeItem(name) {
    appjson.appInfoDB.setItem(name, null);
  }
  async getUserId() {
    let userInfo = await this.getItem("userInfo");
    if (!userInfo) return "";

    userInfo = JSON.parse(userInfo);
    return userInfo.id;
  }

  /**
   * 系统配置相关
   */
  saveConfigField(configField = {}) {
    appjson.setSystemConfig(configField);
  }
  getAllConfigField() {
    return (
      appjson.getSystemConfig() || {
        fileSavePath: (Ps.getUserHomeDir() + "/Downloads").replace(/\//g, "\\"),
        chatbotAvailable: true,
        msgNotifyEnable: true,
      }
    );
  }
}

JsondbService.toString = () => "[class JsondbService]";
module.exports = JsondbService;
