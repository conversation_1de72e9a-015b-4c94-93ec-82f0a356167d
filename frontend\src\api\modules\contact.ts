import request, { ResultType } from "@/api";

// 获取好友分组 {userId}
export const getFriendGroup = async (params: any) => {
  return await request.get("/admin-api/chat/friendGroup/selectByUserId", { params });
};
// 新增好友分组 {userId, groupName, sort}
export const addFriendGroup = async (params: any) => {
  return await request.post("/admin-api/chat/friendGroup/add", params);
};
// 修改好友分组 {id, groupName, sort}
export const updateFriendGroup = async (params: any) => {
  return await request.put("/admin-api/chat/friendGroup/update", params);
};
// 删除好友分组 {id}
export const deleteFriendGroup = async (id: String) => {
  return await request.delete("/admin-api/chat/friendGroup/" + id);
};
// 获取分组内好友 {userId, status, groupId}
export const getAllFriend = async (params: any) => {
  return await request.get("/admin-api/chat/friend/selectAllFriends", { params });
};
// 增加好友 {userId, friendId, groupId, status}
export const addFriend = async (params: any) => {
  return await request.post("/admin-api/chat/friend/add", params);
};
// 修改好友 {id, groupId, status}
export const updateFriend = async (params: any) => {
  return await request.put("/admin-api/chat/friend/update", params);
};
// 删除好友 {id}
export const deleteFriend = async (id: string) => {
  return await request.delete("/admin-api/chat/friend/" + id);
};
// 获取联系人信息
export const getPersonInfo = async (id: string) => {
  return await request.get("/admin-api/system/user/get?id=" + id);
};
// 修改用户信息 {otel, oemail, description}
export const updateUserInfo = async (params: any) => {
  return await request.put("/admin-api/system/user/update", params);
};
// 获取群组列表 {pageSize, pageNo, groupName}
export const getChatGroup = async (params: any) => {
  return await request.get("/admin-api/chat/group/pageGroup", { params });
};
// 创建群组 {secret, groupName, groupType, groupScope, belongProject, memberList[{member, memberName, memberRole}]}
export const saveGroup = async (params: any) => {
  return await request.post("/admin-api/chat/group/saveGroup", params);
};
// 创建群组审批
export const saveGroupApprove = async (params: any) => {
  return await request.post("/admin-api/chat/groupApprove/add", params);
};
// 获取审批列表
export const getGroupApproveList = async (params: any) => {
  return await request.get("/admin-api/chat/groupApprove/getApproveList", { params });
};
// 审批操作
export const updateGroupApprove = async (params: any) => {
  return await request.put("/admin-api/chat/groupApprove/approve", params);
};
// 获取群组信息 {id}
export const queryGroupInfo = async (id: string) => {
  return await request.get("/admin-api/chat/group/queryGroupInfo?id=" + id);
};
// 解散群组 {id}
export const ungroup = async (params: any) => {
  return await request.delete("/admin-api/chat/group/ungroup", { params });
};
// 修改群组组名 {id, groupName}
export const updateGroupName = async (params: any) => {
  return await request.put("/admin-api/chat/group/updateGroupName", params);
};
// 修改群组公告 {id, groupNotice}
export const updateGroupNotice = async (params: any) => {
  return await request.put("/admin-api/chat/group/updateGroupNotice", params);
};
// 获取群组成员 {groupId}
export const listMemberByGroupId = async (id: string) => {
  return await request.get("/admin-api/chat/groupMember/listMemberByGroupId?groupId=" + id);
};
// 删除群组成员 {groupId, ids}
export const deleteMembers = async (params: any) => {
  return await request.delete("/admin-api/chat/groupMember/deleteMembers", { params });
};
// 添加群组成员 {groupId, memberList[{member, memberName}]}
export const addMembers = async (params: any) => {
  return await request.post("/admin-api/chat/groupMember/addMembers", params);
};
// 群主变更 {groupId, groupOwnerId}
export const changeGroupOwner = async (params: any) => {
  return await request.put("/admin-api/chat/groupMember/changeGroupOwner", params);
};
// 退出群组 {groupId}
export const exitGroup = async (params: any) => {
  return await request.delete("/admin-api/chat/groupMember/exitGroup", { params });
};
// 搜索用户条件查询 {name}
export const getChatSingle = async (params: any) => {
  return await request.get("/admin-api/system/user/page", { params });
};
// 获取用户密级字典
export const getUserSecretDict = async () => {
  return await request.get("/admin-api/system/dict-data/page?dictType=screct_level");
};
// 新增投票基本信息
export const getVoteCreate = async (params: any) => {
  return await request.post("/admin-api/chat/messageVotes/create", params);
};
// 点击投票
export const getVoteRecords = async (params: any) => {
  return await request.post("/admin-api/chat/messageVoteRecords/create", params);
};
// 获取投票基本信息详情
export const getMessageVote = async (id: any) => {
  return await request.get("/admin-api/chat/messageVotes/get?id=" + id);
};
// 查询投票人和记录
export const messageVotePage = async (params: any) => {
  return await request.get("/admin-api/chat/messageVoteRecords/page", { params });
};
// 创建接龙
export const createSolitaire = async (params: any) => {
  return await request.post("/admin-api/chat/messageRelay/create", params);
};
// 创建接龙信息
export const createSolitaireInfo = async (params: any) => {
  return await request.post("/admin-api/chat/messageRelayParticipant/create", params);
};
// 获取接龙基本信息详情
export const getMessageRelay = async (id: any) => {
  return await request.get("/admin-api/chat/messageRelay/get?id=" + id);
};
// 新增接龙数据信息
export const creatRelayParticipant = async (params: any) => {
  return await request.post<any, ResultType>("/admin-api/chat/messageRelayParticipant/create", params);
};
// 修改接龙数据信息
export const updateRelayParticipant = async (params: any) => {
  return await request.put<any, ResultType>("/admin-api/chat/messageRelayParticipant/update", params);
};
// 获取接龙基本信息详情
export const getMessageRelayList = async (id: any) => {
  return await request.get<any, ResultType>("/admin-api/chat/messageRelay/page?id=" + id);
};
// 删除接龙数据信息
export const deleteRelayParticipant = async (id: string) => {
  return await request.delete<any, ResultType>("/admin-api/chat/messageRelayParticipant/delete?id=" + id);
};
export const getContactInfoById = async (id: string, isGroup: boolean) => {
  return await request.get<any, ResultType>(`/admin-api/chat/recentChat/getContactInfoById?id=${id}&isGroup=${isGroup}`);
};

// 新增公文基本信息
export const officialDocCreate = async (params: any) => {
  return await request.post("/admin-api/chat/messageOfficial/create", params);
};

// 获取公文基本信息详情
export const getMessageOfficialDoc = async (id: any) => {
  return await request.get("/admin-api/chat/messageOfficial/get?id=" + id);
};

// 参与公文学习
export const participateOfficialDoc = async (params: any) => {
  return await request.post<any, ResultType>("/admin-api/chat/messageOfficial/participate", params);
};
