import { ipc, isEE } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";
import IpcListenerManager from "@/ipc/IpcListener";
import { useUserStore } from "@/stores/modules/user";

export function useIpcEvent() {
  const ipcManager = IpcListenerManager.getInstance();
  const userStore = useUserStore();

  if (!isEE) return;
  // 监听设置项更新
  // ipcManager.add("page-listen", "when-config-update", () => {});

  // 监听清空缓存
  ipcManager.add("page-listen", "clear-cache", () => {
    if (userStore.token) {
      userStore.clearLocalStorage();
      window.location.reload();
    }
  });

  // 页面加载完初始化token
  if (userStore.token && userStore.userInfo) {
    ipc.send(ipcApiRoute.setCache, ["userInfo", JSON.stringify(userStore.userInfo)]);
  } else {
    ipc.send(ipcApiRoute.removeCache, "userInfo");
  }
}
