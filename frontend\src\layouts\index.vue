<!-- 分栏布局 -->
<template>
  <el-container class="app-container">
    <AppHeader />
    <el-container class="z-10" style="padding-top: 48px">
      <AppSidebar />
      <MainPage />
    </el-container>
    <Profile ref="profile" />
  </el-container>
</template>

<script setup lang="ts" name="layout">
import { onMounted, ref, provide, onBeforeUnmount } from "vue";
import AppHeader from "./components/AppHeader.vue";
import AppSidebar from "./components/AppSidebar.vue";
import MainPage from "./components/MainPage.vue";
import WebSocketClient from "@/utils/websocket/websocketClient";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { ipc, isEE } from "@/utils/ipcRenderer";
import { useUserStore } from "@/stores/modules/user";
import { useRecentStore } from "@/stores/modules/recent";
import Profile from "@/components/ProfileComp/index.vue";
import { useContactsStore } from "@/stores/modules/contacts";

const userStore = useUserStore();
const recentStore = useRecentStore();
const contactsStore = useContactsStore();

const getWebSocketParams = () => {
  return `?pId=${userStore.userInfo.pid}&userId=${userStore.userId}`;
};
const config = {
  wsProtocol: "ws",
  host: import.meta.env.VITE_CHAT_WEBSOCKET,
  baseUrl: "/websocket",
  paramString: getWebSocketParams,
  heartbeatInterval: 30000,
  reconnectTimeout: 5000,
  maxReconnectAttempts: 10
};
// 直接通过带参数的 getInstance 初始化
const ws = WebSocketClient.getInstance(config);

// 查看用户信息
const profile: any = ref(null);
const openProfile = (id: string, type: any) => {
  if (profile.value) profile.value.openProfile(id, type);
};
provide("clickOpenProfile", openProfile);

// 添加生命周期钩子处理连接
onMounted(async () => {
  // 更新全部联系人
  await contactsStore.getAllContacts();

  // userStore.getUserInfo();
  if (recentStore.listRecents.length === 0) {
    await recentStore.getListRecents();
  }
  
  ws.connect();
  if (!isEE) return;
  ipc.invoke(ipcApiRoute.getWinId, "FloatingBall").then((wcid: any) => {
    if (!wcid) {
      // 1.1 启动本地悬浮球
      ipc.invoke(ipcApiRoute.createFloatingBall, "userInfo").then((_wcid: any) => {});
    }
  });
});

onBeforeUnmount(() => {
  if (ws) ws.disconnect();
});
</script>

<style scoped lang="scss">
.app-container {
  @apply antialiased;
}
.dark {
  .app-container {
    @apply bg-gray-900;
  }
}
</style>
