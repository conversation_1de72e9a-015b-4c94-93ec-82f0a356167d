import { defineStore } from "pinia";
import { History } from "../interface";
import piniaPersistConfig from "../helper/persist";
import { getMessageOfficialDoc, getMessageRelay, getMessageVote } from "@/api/modules/contact";
import { useUserStore } from "./user";
import { getBusinessList } from "@/api/modules/business";
import { ipc, isEE } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";

const userStore = useUserStore();
export const useHistoryStore = defineStore("lark-history", {
  state: (): History => ({
    msgHistory: {},
    msgIds: {},
    voteList: {},
    relayList: {},
    msgBuffer: {},
    officialDocuments: {},
    activity: {}
  }),
  getters: {},
  actions: {
    // 获取聊天历史
    getMsgHistory(contactId: string) {
      if (this.msgHistory[contactId]) {
        return this.msgHistory[contactId].sort((a: any, b: any) => a.createTime - b.createTime);
      } else {
        return [];
      }
    },
    // 设置聊天历史，新增消息
    setMsgHistory(contactId: string, messageId: string, message: any) {
      if (!this.msgIds[contactId]) {
        this.msgIds[contactId] = [];
      }
      if (!this.msgHistory[contactId]) {
        this.msgHistory[contactId] = [];
      }
      if (!this.msgIds[contactId].includes(messageId)) {
        this.msgIds[contactId].push(messageId);
        this.msgHistory[contactId].push(message);
      }
      let arr = this.msgHistory[contactId].sort((a: any, b: any) => a.createTime - b.createTime);
      this.msgHistory[contactId] = arr;
    },
    updateOfflineMsgHistory(contactId: string, messageId: string, message: any, chatType: any) {
      // 处理离线消息
      let index = this.msgHistory[contactId].findIndex((item: { id: string }) => item.id == messageId);
      if (index >= 0) {
        this.msgHistory[contactId].splice(index, 1, message);
        // 重新排序
        this.msgHistory[contactId] = this.msgHistory[contactId].sort((a: any, b: any) => a.createTime - b.createTime);

        if (isEE) {
          let apiUrl = "";
          if (chatType) {
            apiUrl = ipcApiRoute.removeGroupMessage;
          } else {
            apiUrl = ipcApiRoute.removeUserMessage;
          }
          ipc.send(apiUrl, messageId);
        }
      }
    },
    // 撤回消息
    deleteMsgHistory(contactId: string, msgId: string, name: string) {
      let index = this.msgHistory[contactId].findIndex((item: any) => item.id == msgId);
      let msg = this.msgHistory[contactId][index];
      if ([4, 5, 9].includes(msg.msgType)) {
        let activityMap: any = {
          4: "votesList",
          5: "relayList",
          9: "messageOfficialList"
        };
        let acIndex = this.activity[activityMap[msg.msgType]].findIndex((m: any) => m.id == msg.msg);
        this.activity[activityMap[msg.msgType]].splice(acIndex, 1);
      }
      msg.cancel = 1;
      msg.msgType = 0;
      msg.msg = `${name}撤回了一条消息`;
      if (isEE) {
        if (this.msgHistory[contactId][index].chatType) {
          ipc.invoke(ipcApiRoute.updateGroupMessage, JSON.stringify(msg));
        } else {
          ipc.invoke(ipcApiRoute.updateUserMessage, JSON.stringify(msg));
        }
      }
    },
    // 置顶消息
    pinMsgHistory(contactId: string, msgId?: string) {
      let index1 = this.msgHistory[contactId]?.findIndex((item: any) => item.isTop == "1");
      if (index1 >= 0) this.msgHistory[contactId][index1].isTop = "0";
      if (msgId) {
        let index2 = this.msgHistory[contactId]?.findIndex((item: any) => item.id == msgId);
        if (index2 >= 0) this.msgHistory[contactId][index2].isTop = "1";
      }
    },
    // 获取投票信息
    async getVoteInfo(voteId: string) {
      const res = await getMessageVote(voteId);
      this.voteList[voteId] = res.data;
      const options = res.data.optionsDOList;
      options.forEach((item: any) => {
        if (item.userIds?.includes(userStore.userId)) {
          this.voteList[voteId].selectId = item.id;
        }
      });
    },
    // 投票后更新页面
    setVoteRecords(voteId: string, optionId: number, userId: string) {
      let index = this.voteList[voteId]?.optionsDOList.findIndex((item: any) => item.id == optionId);
      if (!this.voteList[voteId].optionsDOList[index].userIds?.includes(userId)) {
        this.voteList[voteId].optionsDOList[index].votes++;
        this.voteList[voteId].optionsDOList[index].userIds?.push(userId);
      }
      if (userStore.userId == userId) {
        this.voteList[voteId].selectId = optionId;
        const acIndex = this.activity.votesList?.findIndex((a: any) => a.id == voteId);
        this.activity.votesList[acIndex].participationStatus = "0";
      }
    },
    // 获取接龙信息
    async getRelayInfo(relayId: string) {
      const { data } = await getMessageRelay(relayId);
      this.relayList[relayId] = data;
    },
    // 接龙后更新页面
    setRelayRecords(relayId: string, data: any) {
      let participantRespVOList = this.relayList[relayId]?.participantRespVOList;
      if (!participantRespVOList) participantRespVOList = [];
      let index = participantRespVOList.some((item: any) => {
        if (item.creatorId == data.creatorId) {
          delete data.createTime;
          Object.assign(item, data);
        }
        return item.creatorId == data.creatorId;
      });

      if (!index) {
        participantRespVOList.push(data);
      }
    },
    deleteParticipant(relayId: string, id: string) {
      let index = this.relayList[relayId]?.participantRespVOList?.findIndex((item: any) => item.id == id);
      if (index > -1) {
        this.relayList[relayId].participantRespVOList.splice(index, 1);
      }
    },
    // 更新消息已读状态
    updateMsgRead(contactId: string) {
      if (!this.msgHistory[contactId]) return;
      this.msgHistory[contactId].forEach((item: any) => {
        item.isRead = 0;
      });
    },
    // 上传文件消息缓存
    addMsgBuffer(key: string, value: object) {
      if (!this.msgBuffer[key]) this.msgBuffer[key] = value;
    },
    deleteMsgBuffer(key: string, contactId?: string, msgId?: string) {
      delete this.msgBuffer[key];
      if (contactId) {
        if (!this.msgIds[contactId]) this.msgIds[contactId] = [];
        this.msgIds[contactId].push(msgId);
      }
    },
    updateHistory(contactId: string, messageId: string, message: any) {
      let index = this.msgHistory[contactId].findIndex((item: any) => item.id == messageId);
      if (index >= 0) {
        this.msgHistory[contactId][index] = message;
        let arr = this.msgHistory[contactId].sort((a: any, b: any) => a.createTime - b.createTime);
        this.msgHistory[contactId] = arr;
      }
    },
    // 获取群组活动（投票接龙）
    async getGroupActivity(contactId: string) {
      let res: any = await getBusinessList(contactId);
      if (res.code == 0) this.activity = res.data;
    },
    // 获取公文信息
    async getOfficialDocInfo(docId: string, userId: string) {
      const { data } = await getMessageOfficialDoc(docId);
      if (!data.messageOfficialStudyList) data.messageOfficialStudyList = [];
      if (data.messageOfficialStudyList.length) {
        if (data.messageOfficialStudyList.some((item: any) => item.creator == userId)) {
          data.studyStatus = true;
        }
      }
      this.officialDocuments[docId] = data;
    },
    // 参与公文学习
    async setParticipateOfficialDoc(docId: string, data: any) {
      let messageOfficialStudyList = this.officialDocuments[docId]?.messageOfficialStudyList;
      if (!messageOfficialStudyList) messageOfficialStudyList = [];

      // 禁止重复学习
      if (messageOfficialStudyList.some((item: any) => item.creator == data.creator)) return 

      if (userStore.userId == data.creator) this.officialDocuments[docId].studyStatus = true;
      messageOfficialStudyList.push(data);
    }
  },
  persist: piniaPersistConfig("lark-history-session")
});
