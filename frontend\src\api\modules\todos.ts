import { ipc } from '@/utils/ipcRenderer'

const SERVICE_NAME = 'database.todos'

/**
 * @name 待办事项模块
 */

// 定义待办事项的接口
export interface TodoItem {
  id: string
  userId: string
  content: string
  completed: boolean
  createTime: number
  updateTime: number
}

// 获取待办事项列表
export const getTodosList = async (): Promise<{ list: TodoItem[]; total: number }> => {
  // 请求一个足够大的页面以获取所有数据
  const params = { pageNo: 1, pageSize: 1000 }
  return ipc.invoke('controller.db.all', {
    service: SERVICE_NAME,
    method: 'getTodosList',
    args: [JSON.stringify(params)]
  })
}

// 添加待办事项
export const addTodo = (data: { content: string }): Promise<[string]> => {
  return ipc.invoke('controller.db.all', {
    service: SERVICE_NAME,
    method: 'addTodo',
    args: [JSON.stringify(data)]
  })
}

// 更新待办事项状态
export const updateTodoStatus = (data: { id: string; completed: boolean }): Promise<number> => {
  return ipc.invoke('controller.db.all', {
    service: SERVICE_NAME,
    method: 'updateTodoStatus',
    args: [JSON.stringify(data)]
  })
}

// 更新待办事项内容
export const updateTodoContent = (data: { id: string; content: string }): Promise<number> => {
  return ipc.invoke('controller.db.all', {
    service: SERVICE_NAME,
    method: 'updateTodoContent',
    args: [JSON.stringify(data)]
  })
}

// 删除待办事项
export const deleteTodo = (id: string): Promise<number> => {
  return ipc.invoke('controller.db.all', {
    service: SERVICE_NAME,
    method: 'deleteTodo',
    args: [id]
  })
} 