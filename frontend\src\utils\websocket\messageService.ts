import { MessageSendInfo, MessageCode, ContentType } from "@/utils/websocket/messageInfo";
import type WebSocketClient from "@/utils/websocket/websocketClient";
import { useUserStore } from "@/stores/modules/user";

export const sendMessage = async (
  wsClient: WebSocketClient,
  params: {
    receiverId?: string;
    content: any; // 改为更灵活的内容类型
    isGroup?: boolean;
    contentType?: ContentType;
    atIds?: string[];
    contentId?: string;
    id?: string; // 消息ID
    code?: MessageCode;
  }
): Promise<void> => {
  // 参数校验
  if (!params.receiverId) throw new Error("接收者ID不能为空");
  if (!params.content) throw new Error("消息内容不能为空");

  // 获取用户信息
  const userStore = useUserStore();
  const currentUser = userStore.userInfo;

  const message: MessageSendInfo = {
    code: params.code ? params.code : params.isGroup ? MessageCode.GROUP : MessageCode.PRAIVTE,
    data: {
      id: params.id,
      fromId: currentUser.id,
      toId: params.receiverId,
      atId: params.atIds || [],
      isGroup: params.isGroup || false,
      contentId: params.contentId,
      contentType: params.contentType || ContentType.TEXT,
      content: {
        ...params.content,
        secretLevel: params.content.secretLevel || 30, // 保留默认密级
        timestamp: new Date().toISOString() // 添加时间戳
      }
    }
  };

  try {
    if (!wsClient.isConnected) {
      throw new Error("WebSocket 连接未就绪");
    }
    await wsClient.send(message);
  } catch (error:any) {
    console.error("消息发送失败:", error);
    throw new Error(`消息发送失败: ${error}`);
  }
};

/**
    import { sendMessage } from "@/utils/chat/messenger";
    import { inject } from "vue";

    const wsClient = inject<WebSocketClient>("WebSocketClient");

    // 发送文本消息
    const handleSend = async () => {
    try {
        await sendChatMessage(wsClient!, {
        receiverId: "user_123",
        id: "msg_123",
        contentId: "content_123",
        contentType: ContentType.TEXT,
        content: "你好，这是测试消息",
        isGroup: false
        });
        console.success("消息发送成功");
    } catch (error) {
        console.error(error.message);
    }
    }
 */
