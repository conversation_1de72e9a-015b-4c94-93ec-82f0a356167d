const Log = require("ee-core/log");
const Ps = require("ee-core/ps");
const path = require("path");
const fs = require("fs");
const Helper = require("ee-core/utils/helper");

class Knex {
  constructor() {
    if (!Knex.instance) {
      // 确保只有一个实例（单例模式）
      const _this = this;
      this.knexInstance = require("knex")({
        client: "better-sqlite3",
        connection: {
          filename: _this.dbPath("sqlite_db"),
        },
        migrations: {
          directory: path.join(__dirname, "./migrate"),
        },
        debug: false,
        useNullAsDefault: true,
      });
      Knex.instance = this;
      // Log.info("Knex 单例模式已创建");
    }
    return Knex.instance;
  }

  dbPath(database) {
    const storageDir = path.join(Ps.getExecDir(), "data"); // 获取存储目录
    if (!fs.existsSync(storageDir)) {
      Helper.mkdir(storageDir);
      Helper.chmodPath(storageDir, "777");
    }
    const dbPath = path.join(storageDir, database + ".db"); // 构建数据库文件路径

    // 如果数据库文件不存在，则创建数据库文件
    // if (!fs.existsSync(dbPath)) {
    //   fs.writeFileSync(dbPath, "");
    //   Helper.chmodPath(dbPath, "777");
    // }
    return dbPath;
  }

  getKnex() {
    return this.knexInstance; // 返回 knex 实例
  }

  // 运行所有尚未执行的迁移
  async runMigrate() {
    try {
      await this.knexInstance.migrate.latest(); // 使用 migrate.latest 而不是 rollback
    } catch (error) {
      Log.error("Failed to apply database migrations:", error);
    }
  }
}

// 创建 Knex 类的单例实例
const dbSingleton = Object.freeze(new Knex());
(async () => {
  await dbSingleton.runMigrate();
})();

// 导出 knex 实例
module.exports = dbSingleton.getKnex();
