import { Message<PERSON><PERSON><PERSON> } from "./messageHandler";
import { MessageInfo, MessageSendInfo, convertMessage } from "./messageInfo";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import { ElMessage } from "element-plus";

const userStore = useUserStore();
const talkStore = useTalkStore();

export default class WebSocketClient {
  private static instance: WebSocketClient;
  private socket: WebSocket | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0; // 新增重连计数器
  private isReconnecting = false; // 新增重连状态标志

  // 精简后的配置参数
  constructor(
    private config: {
      wsProtocol: string;
      host: string;
      baseUrl: string;
      paramString: string | (() => string);
      heartbeatInterval: number;
      reconnectTimeout: number;
      maxReconnectAttempts: number;
    }
  ) {}

  public connect(): void {
    const url = `${this.config.wsProtocol}://${this.config.host}${this.config.baseUrl}${
      typeof this.config.paramString === "function" ? this.config.paramString() : this.config.paramString
    }`;
    this.socket = new WebSocket(url);
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.onopen = () => {
      // 连接成功后发送缓存的ID
      this.sendCachedIds();
      this.startHeartbeat();
    };

    this.socket.onmessage = event => {
      const message: MessageInfo = JSON.parse(event.data);
      new MessageHandler().handleMessage(message);
    };

    this.socket.onclose = () => {
      this.handleReconnect();
    };

    this.socket.onerror = (error: any) => {
      this.handleReconnect();
    };
  }

  private startHeartbeat(): void {
    this.clearHeartbeatTimer();

    const getHeartbeatContent = () => {
      const params = typeof this.config.paramString === "function" ? this.config.paramString() : this.config.paramString;

      // 解析参数字符串为JSON格式
      const searchParams = new URLSearchParams(params.split("?")[1]);
      return JSON.stringify({
        code: 100860,
        data: {
          pid: searchParams.get("pId"),
          userid: searchParams.get("userId")
        }
      });
    };

    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.socket!.send(getHeartbeatContent());
      }
    }, this.config.heartbeatInterval);
  }

  // 从本地缓存获取所有ID
  private getUserCachedIds = () => {
    try {
      // 方法1: 从localStorage获取
      // const cachedIds = localStorage.getItem("userIds");
      // if (cachedIds) {
      //   return JSON.parse(cachedIds);
      // }

      // 方法2: 从sessionStorage获取
      const cachedData = sessionStorage.getItem("lark-users-session");
      if (!cachedData) {
        return [];
      }

      const parsedData = JSON.parse(cachedData);
      // 提取每个数组的第一个元素（ID）
      const ids = [];
      // 如果parsedData是对象，遍历其所有属性
      for (const key in parsedData) {
        const item = parsedData[key];

        // 检查是否是数组且长度大于0
        if (Array.isArray(item) && item.length > 0) {
          // 取数组的第一个元素作为ID
          const id = item[0];
          if (id && typeof id === "string") {
            ids.push(id);
          }
        }
      }
      return ids;
    } catch (error: any) {
      ElMessage.error(error.message);
      return [];
    }
  };

  // 获取最近联系人群组id
  private getGroupCacheIds = () => {
    const cachedData = sessionStorage.getItem("lark-recent-session");
    if (!cachedData) {
      return [];
    }

    try {
      const rencents = JSON.parse(cachedData);

      let ids = [];
      for (let item of rencents.listRecents) {
        if (item.chatType > 0) {
          ids.push(item.contactId);
        }
      }
      return ids;
    } catch (error) {
      return [];
    }
  };

  // 发送缓存的ID到服务器
  private sendCachedIds = () => {
    if (!this.isConnected) {
      ElMessage.error("WebSocket未连接，无法发送数据");
      return;
    }

    try {
      const cachedIds = this.getUserCachedIds();
      const groupIds = this.getGroupCacheIds();

      if (cachedIds.length === 0) {
        return;
      }

      // 构造发送的数据格式
      const messageData: MessageSendInfo = {
        code: 700300, // 消息类型
        data: {
          fromId: userStore.userId,
          toId: userStore.userId,
          content: {
            userids: cachedIds,
            groupIds
          }
        }
      };

      // 发送数据到服务器
      this.socket?.send(JSON.stringify(messageData));
    } catch (error: any) {
      ElMessage.error(error.message);
    }
  };

  public get isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }

  private handleReconnect(): void {
    // if (this.isReconnecting || this.reconnectAttempts >= this.config.maxReconnectAttempts) {
    if (this.isReconnecting) {
      return;
    }

    this.isReconnecting = true;
    // 清除旧连接
    this.cleanupConnection();

    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      this.connect();
      this.isReconnecting = false;
    }, delay);
  }

  // 新增清理方法
  private cleanupConnection(): void {
    if (this.socket) {
      this.socket.onopen = null;
      this.socket.onmessage = null;
      this.socket.onclose = null;
      this.socket.onerror = null;
      this.socket.close();
      this.socket = null;
    }
    this.clearHeartbeatTimer();
  }

  public disconnect(): void {
    this.reconnectAttempts = 0;
    this.isReconnecting = false;
    this.cleanupConnection();
    clearTimeout(this.reconnectTimer!);
  }

  // 修改后的 send 方法
  public async send(message: MessageSendInfo): Promise<void> {
    if (!this.isConnected) {
      const errorMsg = "消息未发送 - 连接未就绪";
      ElMessage.error(errorMsg);
      throw new Error(errorMsg);
    }
    try {
      // 转换消息格式
      const convertedMsg = await convertMessage(message);
      this.socket!.send(JSON.stringify(convertedMsg));
    } catch (error: any) {
      ElMessage.error(error.message);
      throw new Error("消息发送失败: " + error);
    }
  }

  // Method to clear the heartbeat timer
  private clearHeartbeatTimer(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // 单例模式访问
  public static getInstance(config?: any): WebSocketClient {
    if (!WebSocketClient.instance && config) {
      WebSocketClient.instance = new WebSocketClient(config);
    }
    return WebSocketClient.instance!;
  }
}
