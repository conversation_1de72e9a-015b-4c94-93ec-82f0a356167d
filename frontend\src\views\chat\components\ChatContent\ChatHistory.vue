<template>
  <div>
    <div v-if="msgList && msgList.length > 0" class="history">
      <el-scrollbar ref="scrollbar" @scroll="handleScroll" class="msg-box-scroll">
        <PinnedMessage v-if="pinnedTopic?.isTop" :pinnedTopic="pinnedTopic" @unpin-click="unpinMessage"> </PinnedMessage>
        <div v-if="loading" class="text-sm text-gray-400 text-center p-4">加载中...</div>
        <div v-if="noMore" class="text-sm text-gray-400 text-center p-4">~ 没有更多数据了 ~</div>
        <div v-for="(item, index) in comMsgList" :key="item.id" class="history-item" :class="{ 'py-4 px-2': item.cancel == 0 }">
          <div v-show="item.cancel == 0">
            <div v-if="item.msgType == 7" class="text-xs text-center text-gray-400">
              <div>{{ dayjs(item.createTime).format("YYYY-MM-DD HH:mm:ss") }}</div>
              <div>{{ item.msg }}</div>
            </div>
            <div class="flex items-start gap-2.5" :class="{ 'flex-row-reverse': item.sender == userStore.userId }" v-else>
              <div>
                <DynamicAvatar :id="item.sender" :data-info="item" :relation-name="item.senderName" :type-avatar="0" :size="32" />
              </div>
              <div
                class="flex flex-col gap-1 relative"
                :class="[
                  { 'items-end': item.sender == userStore.userId },
                  [4, 5, 9].includes(item.msgType) ? 'max-w-[480px] w-full' : 'max-w-[360px]'
                ]"
              >
                <div
                  class="flex items-center space-x-2"
                  :class="{ 'flex-row-reverse space-x-reverse': item.sender == userStore.userId }"
                >
                  <span class="name text-sm font-semibold text-gray-900">{{ item.senderName }}</span>
                  <span class="time text-sm font-normal text-gray-500">
                    {{ dayjs(item.createTime).format("YYYY-MM-DD HH:mm:ss") }}
                  </span>
                </div>
                <div class="relative">
                  <ContextMenu ref="contextMenuRefs" :item="item" @action-click="checkInfo" @menu-right-click="menuRightClick">
                    <div
                      class="relative flex rounded-xl"
                      :class="
                        [3, 4, 5, 9].includes(item.msgType) ? '' : item.sender == userStore.userId ? 'msg-box-me' : 'msg-box'
                      "
                    >
                      <!-- 文本 -->
                      <MessageText :data-item="item" v-if="item.msgType == 0" />
                      <!-- 表情 -->
                      <MessageEmo :data-item="item" v-else-if="item.msgType == 8" />
                      <!-- 文件 -->
                      <MessageFile :data-item="item" v-else-if="item.msgType == 1" />
                      <!-- 图片 -->
                      <MessageImg :data-item="item" v-else-if="item.msgType == 2" />
                      <!-- 音频 -->
                      <MessageAudio :data-item="item" :currt-index="index" v-else-if="item.msgType == 3" />
                      <!-- 投票 -->
                      <MessageVote :vote-id="item.msg" v-else-if="item.msgType == 4" />
                      <!-- 接龙 -->
                      <MessageRelay :relay-id="item.msg" v-else-if="item.msgType == 5" />
                      <!-- 公文 -->
                      <MessageOfficialDoc :doc-id="item.msg" v-else-if="item.msgType == 9" />
                    </div>
                  </ContextMenu>
                  <el-icon
                    title="重新发送"
                    v-if="item.offlineMsgFailedType && item.sender == userStore.userId"
                    @click="handleResend(item, talkStore.activeContact.chatType)"
                    class="dot"
                    :size="18"
                    color="#d30303"
                  >
                    <WarningFilled />
                  </el-icon>
                </div>
                <div class="flex items-center justify-start mt-1">
                  <span
                    class="read mr-2 text-sm font-normal text-gray-500"
                    v-if="!contact.chatType && !item.offlineMsgFailedType"
                  >
                    {{ item.isRead == "1" ? "未读" : "已读" }}
                  </span>
                  <LevelBtn :message="item.secret"></LevelBtn>
                </div>
              </div>
            </div>
          </div>
          <div v-show="item.cancel == 1" class="text-xs text-center my-5 text-gray-400">{{ item.msg }}</div>
        </div>
      </el-scrollbar>
    </div>
    <MessageShare :item-info="itemInfo" @chat-close="closeDialog" />
    <MessageDialog
      :title="diaTitle"
      :url="diaUrl"
      :content-text="diaContent"
      :icon-name="iconName"
      :item-num="diaNum"
      @dia-close="closeMessageDialog"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, computed, watch, nextTick, onUnmounted, onMounted, watchEffect, inject } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import { useHistoryStore } from "@/stores/modules/history";
import { useRecentStore } from "@/stores/modules/recent";
import { useContactStore } from "@/stores/modules/friend";
import axios from "axios";
import * as historyApi from "@/api/modules/history";
import * as FileApi from "@/api/infra/file";
import LevelBtn from "@/components/LevelBtn/index.vue";
import dayjs from "dayjs";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import MessageImg from "./MessageImg.vue";
import MessageFile from "./MessageFile.vue";
import MessageAudio from "./MessageAudio.vue";
import MessageText from "./MessageText.vue";
import MessageShare from "./MessageShare.vue";
import MessageEmo from "./MessageEmo.vue";
import MessageVote from "./MessageVote.vue";
import MessageRelay from "./MessageRelay.vue";
import MessageOfficialDoc from "./MessageOfficialDoc.vue";
import MessageDialog from "./MessageDialog.vue";
import ContextMenu from "./ContextMenu.vue";
import { ContentType, MessageCode, MessageSendInfo } from "@/utils/websocket/messageInfo";
import WebSocketClient from "@/utils/websocket/websocketClient";
import PinnedMessage from "./PinnedMessage.vue";
import { ElMessage } from "element-plus";
import { isEE, ipc } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { FullScreen, WarningFilled } from "@element-plus/icons-vue";
import * as dbApi from "@/utils/indexedDB";
import { ElLoading } from "element-plus";
import { debounce } from "lodash";
const webSocketManager: any = WebSocketClient.getInstance();

const userStore = useUserStore();
const talkStore = useTalkStore();
const historyStore = useHistoryStore();
const recentStore = useRecentStore();
const contactStore = useContactStore();

const pageNo = ref(1);
const pageSize = 15;
const loading = ref(false);
const noMore = ref(false);
const ifScroll = ref(true);
const ifScrollBottom = ref(true);
const ifScrollCenter = ref(false);
const isShow = ref(false);
const openFullScreen = () => {
  ElLoading.service({
    lock: true,
    text: "预览服务请求中...",
    background: "rgba(0,0,0,.1)"
  });
};

const contact = computed(() => talkStore.activeContact);
const msgList = computed(
  () =>
    Array.from(new Set(historyStore.msgHistory[talkStore.activeChatId]?.map((item: any) => item.id))).map(id =>
      historyStore.msgHistory[talkStore.activeChatId].find((obj: any) => obj.id === id)
    ) || []
);
// 结合分页的消息列表
const comMsgList = computed(() => {
  const startIndex = Math.max(msgList.value.length - pageNo.value * pageSize, 0);
  return msgList.value.slice(startIndex);
});

// 消息置顶
const pinnedTopic = computed(() => {
  if (contact.value.chatType == 0) {
    return {};
  } else {
    let result = msgList.value.find((item: any) => item.isTop === "1") || {};
    return result;
  }
});
const itemInfo = ref({});
const contextMenuRefs = ref([]);
const diaTitle = ref("");
const diaContent = ref("");
const diaUrl = ref("");
const diaNum = ref(0);
const iconName = ref("");

const scrollbar: any = ref(null);
const lastPageHeight = ref(0);
// 滚动到底部的方法
const scrollToBottom = () => {
  nextTick(() => {
    if (scrollbar.value) {
      scrollbar.value.setScrollTop(scrollbar.value?.wrapRef.scrollHeight);
    }
  });
};
// 滚动到中间的方法（顶部分页加载时使用）
const scrollToCenter = () => {
  nextTick(() => {
    if (scrollbar.value) {
      // 设置滚动条位置到内容的高度减去上一页的高度，实现滚动到中间
      scrollbar.value.setScrollTop(scrollbar.value?.wrapRef.scrollHeight - lastPageHeight.value);
    }
  });
};
const handleScroll = debounce(async () => {
  if (noMore.value || loading.value || !scrollbar.value) return;

  const { scrollTop, scrollHeight } = scrollbar.value.wrapRef;
  if (scrollTop !== 0) return;

  // 记录当前内容的高度作为上一页高度
  lastPageHeight.value = scrollHeight;

  if (comMsgList.value.length < msgList.value.length) {
    // 取缓存的值
    pageNo.value++;
    scrollToCenter();
  } else {
    await getMsgHistory(talkStore.activeChatId);
    if (!noMore.value) {
      scrollToCenter();
    }
  }
}, 200);
// 有新消息时滚动到底
watch(
  () => msgList.value,
  (newVal, oldVal) => {
    if (newVal.length > 0 && oldVal.length > 0 && newVal[newVal.length - 1].id !== oldVal[oldVal.length - 1].id) {
      scrollToBottom();
    }
  },
  { deep: true }
);

const getMsgHistory = async (contactId: any) => {
  if (loading.value || noMore.value) return;
  pageNo.value = Math.floor(msgList.value.length / pageSize) + 1;
  loading.value = true;
  const params = {
    receiver: contactId,
    sender: userStore.userId,
    pageNo: pageNo.value,
    pageSize: pageSize
  };
  let res: any = [];
  if (isEE) {
    if (contact.value.chatType) {
      const data = await ipc.invoke(ipcApiRoute.getGroupMessageList, JSON.stringify(params));
      if (data.list?.length > 0) {
        res = {
          code: 0,
          data: data
        };
      } else {
        res = await historyApi.getGroupMsgHistory(params);
      }
    } else {
      const data = await ipc.invoke(ipcApiRoute.getUserMessageList, JSON.stringify(params));
      if (data.list?.length > 0) {
        res = {
          code: 0,
          data: data
        };
      } else {
        res = await historyApi.getUserMsgHistory(params);
      }
    }
  } else {
    if (contact.value.chatType) {
      res = await historyApi.getGroupMsgHistory(params);
    } else {
      res = await historyApi.getUserMsgHistory(params);
    }
  }
  if (res.code == 0) {
    res.data.list.forEach((item: any) => {
      historyStore.setMsgHistory(contactId, item.id, item);
      // 存储消息数据到客户端
      if (isEE) {
        let ipcMsg = JSON.stringify(item);
        if (contact.value.chatType) {
          ipc.invoke(ipcApiRoute.addGroupMessage, ipcMsg);
        } else {
          ipc.invoke(ipcApiRoute.addUserMessage, ipcMsg);
        }
      }
      // 存储消息数据到indexedDB
      let message = {
        contact: {
          avatar: contact.value.avatar,
          chatType: contact.value.chatType,
          contactId: contact.value.contactId,
          contactName: contact.value.contactName,
          secret: contact.value.secret
        },
        msgId: item.id,
        msg: item.msg
      };
      dbApi.addData(message);
    });
    loading.value = false;
    if (msgList.value.length == res.data.total) {
      noMore.value = true;
    }
  }
};

const getGroupInfoAndMember = async (activeChatId: string) => {
  if (contact.value.chatType) {
    if (!contactStore.groupInfo[activeChatId]) await contactStore.getGroupInfo(activeChatId);
    if (!contactStore.groupMembers[activeChatId] || contactStore.groupMembers[activeChatId].length == 0) {
      await contactStore.getGroupMember(activeChatId);
    }
  }
};

const setReadStatus = () => {
  let msg: MessageSendInfo = {
    code: MessageCode.PRAIVTE,
    data: {
      id: "",
      fromId: userStore.userId,
      toId: contact.value.contactId,
      atId: [],
      isGroup: false,
      contentId: "",
      content: {},
      contentType: ContentType.UNREAD
    }
  };
  webSocketManager.send(msg);
};

watch(
  () => talkStore.activeChatId,
  async (val: any) => {
    try {
      if (!val) return;
      noMore.value = false;
      loading.value = false;
      pageNo.value = 1;

      // 获取首屏聊天记录
      if (!historyStore.msgHistory[val] || historyStore.msgHistory[val].length < pageSize) {
        await getMsgHistory(val);
        loading.value = false;
        noMore.value = false;
      }
      scrollToBottom();

      // 消息已读状态
      if (contact.value.chatType == 0 && contact.value.unreadNum > 0) {
        historyStore.updateMsgRead(val);
        setReadStatus();
      }
      // 保存已读时间
      if (contact.value.chatType == 0) {
        let Readparams = {
          receiver: val,
          sender: userStore.userId,
          readTime: dayjs(Math.max(contact.value.updateTime, new Date().getTime())).format("YYYY-MM-DD HH:mm:ss")
        };
        historyApi.setReadTime(Readparams);
      }
      // 清除未读
      await recentStore.clearUnreadNum(val);
      // 清除@提示
      await recentStore.clearAtNum(val);
      // 获取群组信息与成员
      await getGroupInfoAndMember(val);
      // 获取群组活动
      if (contact.value.chatType) await historyStore.getGroupActivity(val);
    } catch (error: any) {
      ElMessage.error(error.message);
    }
  },
  { immediate: true }
);

// 右键操作
const checkInfo = async (num: any, item: any) => {
  diaNum.value = num;
  diaUrl.value = "";
  let resMsg: any = {};
  item.num = num;
  if (num == 0) {
    handlePinMessage(item);
  } else if (num == 1) {
    talkStore.setQuoteMsg(item);
  } else if (num == 2) {
    itemInfo.value = item;
    talkStore.setDialogBol(true);
  } else if (num == 4) {
    copyTextToClipboard(item.msg);
  } else if (num == 5) {
    const { data: resMsg } = await FileApi.getFileUrl({ id: item.contentId });
    diaTitle.value = "解读：" + resMsg?.name;
    diaContent.value = item.secret.toString();
    diaUrl.value =
      import.meta.env.VITE_FILE_DECODE_URL +
      `?Pid=${userStore.userInfo.pid}&downloadUrl=${resMsg?.url}&fileName=${resMsg?.name}&secretLevel=${item.secret}`;
    iconName.value = "fa-lightbulb";
    talkStore.setDialogBolStatus(true);
  } else if (num == 7) {
    if (item.contentId && item.contentId != null) {
      const { data: resMsg } = await FileApi.getFileUrl({ id: item.contentId });
      diaContent.value = item.secret;
      diaContent.value = item.secret.toString();
      iconName.value = "fa-eye";

      diaTitle.value = "模型预览：" + resMsg?.name;
      diaUrl.value = `${import.meta.env.VITE_CHAT_PREVIEW + "/thirdparty/openModelFileAsync"}?downloadUrl=${resMsg?.url}&fileName=${resMsg?.name}&securityLevel=${item.secret}&x-user-id=${userStore.userId}&x-user-name=${userStore.name}`;
      window.open(diaUrl.value);
    }
  } else {
    let res: any = {};
    let params = {
      id: item.id
    };
    if (contact.value.chatType) {
      res = await historyApi.cancelGroupMessage(params);
    } else {
      res = await historyApi.cancelUserMessage(params);
    }
    if (res.code == 0) {
      let msg: MessageSendInfo = {
        code: contact.value.chatType ? MessageCode.GROUP : MessageCode.PRAIVTE,
        data: {
          id: "",
          fromId: userStore.userId,
          toId: contact.value.contactId,
          atId: [],
          isGroup: contact.value.chatType ? true : false,
          contentId: "",
          content: {
            id: item.id,
            name: userStore.userInfo.name
          },
          contentType: ContentType.BACKSPACE
        }
      };
      webSocketManager.send(msg);
      historyStore.deleteMsgHistory(contact.value.contactId, item.id, userStore.userInfo.name);
      recentStore.updateCancel(contact.value.contactId, userStore.userInfo.name);
    }
  }
};
// 文本复制
const copyTextToClipboard = async (text: string) => {
  try {
    let str = text
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&quot;/g, '"')
      .replace(/&#039;/g, "'");
    await navigator.clipboard.writeText(str);
    ElMessage.success("已复制");
  } catch (err: any) {
    ElMessage.error(err);
  }
};
const closeMessageDialog = (val: any) => {
  talkStore.setDialogBolStatus(val);
};
//取消置顶消息
const unpinMessage = async (item: any) => {
  let params = {
    receiver: contact.value.contactId,
    id: item.id
  };
  let res: any = await historyApi.pinGroupMessage(params);
  if (res.code == 0) {
    let msg: MessageSendInfo = {
      code: MessageCode.GROUP_MODIFY,
      data: {
        id: "",
        fromId: userStore.userId,
        toId: contact.value.contactId,
        atId: [],
        isGroup: true,
        contentId: "",
        content: {},
        contentType: ContentType.UNPIN
      }
    };
    webSocketManager.send(msg);
    historyStore.pinMsgHistory(contact.value.contactId);
  }
};
//置顶消息
const handlePinMessage = async (item: any) => {
  let params = {
    receiver: contact.value.contactId,
    id: item.id,
    isTop: "1"
  };
  let res: any = await historyApi.pinGroupMessage(params);
  if (res.code == 0) {
    let msg: MessageSendInfo = {
      code: MessageCode.GROUP_MODIFY,
      data: {
        id: "",
        fromId: userStore.userId,
        toId: contact.value.contactId,
        atId: [],
        isGroup: true,
        contentId: "",
        content: {
          id: item.id
        },
        contentType: ContentType.PIN
      }
    };
    webSocketManager.send(msg);
    historyStore.pinMsgHistory(contact.value.contactId, item.id);
  }
};
const closeDialog = (val: any, sub?: any) => {
  talkStore.setDialogBol(val);
  if (!sub) return;
  let newMsg: any = {
    sender: userStore.userId,
    senderName: userStore.name,
    secret: sub.secret,
    msg: sub.msg?.trim(),
    msgType: sub.msgType,
    quoteId: "",
    atUserIds: "",
    contentId: sub.contentId
  };
  let idArr = JSON.parse(sub.friends);
  idArr.forEach(async (item: any) => {
    newMsg.receiver = item;
    const res: any = await historyApi.saveUserMessage(newMsg);
    if (res.code == 0) {
      let addMsg: any = res.data;
      historyStore.setMsgHistory(addMsg.receiver, addMsg.id, addMsg);
      recentStore.updateLastMsg(addMsg.receiver, addMsg.msg, addMsg.id, addMsg.msgType, addMsg.updateTime);
      let msg: MessageSendInfo = {
        code: MessageCode.PRAIVTE,
        data: {
          id: addMsg.id,
          fromId: addMsg.sender,
          toId: addMsg.receiver,
          isGroup: false,
          contentId: addMsg.contentId,
          content: {
            msg: addMsg.msg,
            time: addMsg.createTime,
            secret: addMsg.secret,
            quoteSenderName: addMsg.quoteSenderName,
            quoteContent: addMsg.quoteSenderName,
            quoteMessageFile: addMsg.quoteMessageFile
          },
          contentType: addMsg.msgType
        }
      };
      webSocketManager.send(msg);
    }
  });
};

const menuRightClick = () => {
  if (contextMenuRefs.value.length) {
    for (let item of contextMenuRefs.value as any[]) {
      item.hideMenu();
    }
  }
};

document.addEventListener("click", () => {
  if (contextMenuRefs.value.length) {
    for (let item of contextMenuRefs.value as any[]) {
      item.hideMenu();
    }
  }
});

const handleResend = async (messageInfo: any, chatType: any) => {
  let res: any;
  let msgId = messageInfo.id;
  if (talkStore.activeContact.chatType == 0) {
    res = await historyApi.saveUserMessage(messageInfo.waitData);
  } else {
    res = await historyApi.saveGroupMessage(messageInfo.waitData);
  }
  const { code, data } = res;
  if (code == 0) {
    historyStore.updateOfflineMsgHistory(data.receiver, msgId, data, chatType);

    let msg: MessageSendInfo = {
      code: talkStore.activeContact.chatType ? MessageCode.GROUP : MessageCode.PRAIVTE,
      data: {
        id: data.id,
        fromId: data.sender,
        toId: data.receiver,
        atId: [],
        isGroup: talkStore.activeContact.chatType ? true : false,
        contentId: data.contentId,
        chatType: talkStore.activeContact.chatType,
        content: {
          msg: data.msg,
          time: data.createTime,
          secret: data.secret,
          quoteSenderName: data.quoteSenderName,
          quoteContent: data.quoteContent,
          quoteMessageFile: data.quoteMessageFile
        },
        contentType: data.msgType
      }
    };
    webSocketManager.send(msg);

    // 存储消息数据到indexedDB
    let message = {
      contact: {
        avatar: contact.value.avatar,
        chatType: contact.value.chatType,
        contactId: contact.value.contactId,
        contactName: contact.value.contactName,
        secret: contact.value.secret
      },
      msgId: data.id,
      msg: data.msg
    };
    dbApi.addData(message);

    if (contact.value.chatType == 0) {
      recentStore.updateLastMsg(data.receiver, data.msg, data.id, data.msgType, data.updateTime);
    }

    recentStore.updateOrder(data.receiver);

    // 存储消息数据到客户端
    if (isEE) {
      let ipcMsg = JSON.stringify(data);
      if (contact.value.chatType) {
        ipc.invoke(ipcApiRoute.addGroupMessage, ipcMsg);
      } else {
        ipc.invoke(ipcApiRoute.addUserMessage, ipcMsg);
      }
    }
  }
};

onUnmounted(async () => {
  talkStore.ifChat = false;
  talkStore.setActiveChat("");
});
</script>

<style scoped lang="scss">
.el-scrollbar.msg-box-scroll {
  height: calc(100vh - 230px);
  @apply p-5;
  box-sizing: border-box;
  color: #d30303;
}

.history-item {
  .msg-box {
    @apply px-3 py-2 rounded-tl-none bg-gradient-to-br from-gray-50 to-gray-100 shadow-md shadow-gray-200/50 border border-gray-200 text-gray-900;
  }

  .msg-box-me {
    @apply px-3 py-2 rounded-tr-none bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-md shadow-blue-500/20 border border-blue-400;
  }

  .action {
    @apply inline-flex p-2 text-sm font-medium text-gray-500 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-all duration-200;
  }

  .dot {
    @apply absolute top-0 bottom-0 -left-6 m-auto cursor-pointer;
  }
}

.dark {
  .history-item {
    .name {
      @apply text-white;
    }

    .time {
      @apply text-gray-400;
    }

    .msg-box {
      @apply from-gray-700 to-gray-800 border-gray-600 shadow-gray-900/30 text-white;
    }

    .read {
      @apply text-gray-400;
    }

    .action {
      @apply text-gray-400 hover:text-white hover:bg-gray-700;
    }

    .file-box {
      @apply bg-gray-600;

      .file-name {
        @apply text-white;
      }
    }
  }
}
</style>
