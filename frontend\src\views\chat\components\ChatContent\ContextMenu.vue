<template>
  <div
    class="flex items-center relative elbox"
    ref="parentRef"
    @contextmenu.stop.prevent="handleRightClick"
  >
    <el-popover
      :placement="
        [4, 5, 9].includes(props.item?.msgType) && item.sender == userStore.userId
          ? 'left'
          : [4, 5, 9].includes(props.item?.msgType) && item.sender !== userStore.userId
            ? 'right'
            : item.sender == userStore.userId
              ? 'top-end'
              : 'top-start'
      "
      :popper-style="{ minWidth: '200px', width: 'auto', padding: '0', borderRadius: 'none', border: 'none' }"
      :show-arrow="false"
      trigger="hover"
    >
      <ul class="flex" v-if="!props.item?.progress">
        <!-- <li v-if="![4, 5, 9].includes(props.item?.msgType)">
          <button @click="checkInfo(6, item)" class="action-btn">
            <font-awesome-icon :icon="['fas', 'fa-chart-bar']" class="action-btn-icon" />
            AI搜索
          </button>
        </li> -->
        <li v-if="![0, 3, 4, 5, 8, 9].includes(props.item?.msgType)">
          <button @click="checkInfo(5, item)" class="action-btn">
            <font-awesome-icon :icon="['fas', 'fa-lightbulb']" class="action-btn-icon" />
            解读
          </button>
        </li>
        <li v-if="![1, 2, 3, 4, 5, 8, 9].includes(props.item?.msgType)">
          <button @click="checkInfo(4, item)" class="action-btn">
            <font-awesome-icon :icon="['fas', 'fa-copy']" class="action-btn-icon" />
            复制
          </button>
        </li>
        <li v-if="![0, 3, 4, 5, 8, 9].includes(props.item?.msgType)">
          <button @click="checkInfo(7, item)" class="action-btn">
            <font-awesome-icon :icon="['fas', 'fa-eye']" class="action-btn-icon" />
            模型预览
          </button>
        </li>
        <li v-if="talkStore?.activeContact.chatType > 0">
          <button @click="checkInfo(0, item)" class="action-btn">
            <font-awesome-icon :icon="['fas', 'thumbtack']" class="action-btn-icon" />
            置顶
          </button>
        </li>
        <template v-if="!props.item.offlineMsgFailedType">
          <li v-if="![4, 5, 9].includes(props.item?.msgType)">
            <button @click="checkInfo(1, item)" class="action-btn">
              <font-awesome-icon :icon="['fas', 'fa-quote-left']" class="action-btn-icon" />
              引用
            </button>
          </li>
          <li v-if="![4, 5, 9].includes(props.item?.msgType)">
            <button @click="checkInfo(2, item)" class="action-btn">
              <font-awesome-icon :icon="['fas', 'fa-share-alt']" class="action-btn-icon" />
              转发
            </button>
          </li>
        </template>
        <li v-if="item.sender == userStore.userId && item.createTime > new Date().getTime() - 3 * 60 * 1000">
          <button @click="checkInfo(3, item)" class="action-btn">
            <font-awesome-icon :icon="['fas', 'backspace']" class="action-btn-icon" />
            撤回
          </button>
        </li>
      </ul>
      <template #reference>
        <slot></slot>
      </template>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";

const parentRef = ref();
const userStore = useUserStore();
const talkStore = useTalkStore();

const state = reactive({
  isVisible: false,
  top: 0,
  left: 0
});

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});
const emit = defineEmits(["menu-right-click", "action-click"]);

const checkInfo = (index: number, data: any) => {
  emit("action-click", index, data);
};

const handleRightClick = () => {
  emit("menu-right-click");
  state.isVisible = true;
};

const hideMenu = () => {
  state.isVisible = false;
};
const getFileExtension = (filename: any) => {
  // 使用正则表达式匹配最后一个'.'之后的所有字符作为扩展名
  const match = filename.match(/\.([^.]+)$/);
  if (match && match[1]) {
    return match[1];
  } else {
    return ""; // 如果没有找到后缀，则返回空字符串
  }
};
defineExpose({
  hideMenu
});
</script>

<style lang="scss" scoped>
.boxpro .popper__popper {
  z-index: 30;
}
ul {
  z-index: 1;
  @apply rounded-lg bg-white p-2 shadow-lg ring-1 ring-gray-200 border-0;

  &.el-popper .el-popper__arrow::before {
    @apply border-red-100 bg-white;
  }

  .actions {
    @apply rounded-lg bg-white p-2 shadow-lg ring-1 ring-gray-200;
  }

  .action-btn {
    @apply flex items-center w-full gap-2 rounded px-2 py-1.5 hover:bg-gray-100 text-gray-600;

    .action-btn-icon {
      @apply w-4 h-4;
    }
  }
}

.dark {
  ul {
    @apply flex bg-gray-800 ring-gray-700;

    .actions {
      @apply bg-gray-800 ring-gray-700;
    }

    .action-btn {
      @apply hover:bg-gray-700 text-gray-400 hover:text-white;
    }
  }
}
</style>
