exports.up = function (knex) {
  return Promise.resolve()
    .then(() => {
      // 为 user_message 添加 createTime 字段
      return knex.schema.hasColumn('user_message', 'createTime')
        .then(hasColumn => {
          if (!hasColumn) {
            return knex.schema.table('user_message', table => {
              table.integer('createTime');
            });
          }
        });
    })
    .then(() => {
      // 创建 group_message 表
      return knex.schema.hasTable('group_message')
        .then(exists => {
          if (!exists) {
            return knex.schema.createTable('group_message', table => {
              table.text('userId').notNullable();
              table.text('id').notNullable();
              table.text('sender');
              table.text('receiver');
              table.integer('createTime');
              table.text('content');
              table.index(['userId', 'sender', 'receiver']);
              table.unique(['userId', 'id']);
            });
          }
        });
    });
};

exports.down = function (knex) {
  return Promise.resolve();
};
