<template>
  <div class="flex flex-col space-y-4">
    <!-- 输入框 + 新建会话 -->
    <ChatInput @send="handleSend" @new-session="newSession" />

    <!-- 横向 tab 样式会话列表 -->
    <ChatSessionTabs
      :sessions="sessionHistories.map(s => s.map(m => m.content))"
      :current-index="currentSessionIndex"
      @select="loadSession"
      @delete="deleteSession"
    />

    <!-- 聊天消息展示 -->
    <div class="flex flex-col space-y-4 overflow-y-auto max-h-[50vh] bg-gray-900 rounded-md p-4" ref="chatContainer">
      <div v-for="(msg, index) in currentSession" :key="index">
        <UserMessage v-if="msg.role === 'user'" :content="msg.content" />
        <BotMessage v-else-if="msg.role === 'bot'" :content="msg.content" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from "vue";
import ChatInput from "./ChatInput.vue";
import ChatSessionTabs from "./ChatSessionTabs.vue";
import UserMessage from "./UserMessage.vue";
import BotMessage from "./BotMessage.vue";

interface ChatMessage {
  role: "user" | "bot";
  content: string;
}

// 会话数据
const sessionHistories = ref<ChatMessage[][]>([[]]);
const currentSessionIndex = ref(0);
const currentSession = computed(() => sessionHistories.value[currentSessionIndex.value]);

function handleSend(message: string) {
  currentSession.value.push({ role: "user", content: message });
  setTimeout(() => {
    currentSession.value.push({
      role: "bot",
      content: `你说的是：**${message}**，这是 AI 回复内容...`
    });
    scrollToBottom();
  }, 500);
  scrollToBottom();
}

function newSession() {
  sessionHistories.value.push([]);
  currentSessionIndex.value = sessionHistories.value.length - 1;
}

function loadSession(index: number) {
  currentSessionIndex.value = index;
  scrollToBottom();
}

function deleteSession(index: number) {
  sessionHistories.value.splice(index, 1);
  // 调整当前会话 index
  if (currentSessionIndex.value >= sessionHistories.value.length) {
    currentSessionIndex.value = sessionHistories.value.length - 1;
  }
  if (sessionHistories.value.length === 0) {
    sessionHistories.value.push([]);
    currentSessionIndex.value = 0;
  }
}

// 自动滚动到底部
const chatContainer = ref<HTMLElement | null>(null);
function scrollToBottom() {
  nextTick(() => {
    if (chatContainer.value) {
      chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
    }
  });
}
</script>
