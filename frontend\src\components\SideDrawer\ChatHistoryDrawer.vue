<template>
  <div class="drawer-box">
    <slot :open="openDrawer"></slot>
    <el-drawer v-model="visible" size="440px" @close="closeDrawer" class="custom-drawer">
      <template #header>
        <div class="header-content pr-1">
          <span>消息记录</span>
          <el-button link size="small" @click="toggleSearch" class="search-btn">
            <el-icon><Search /></el-icon>
          </el-button>
        </div>
      </template>
      <template #default>
        <div v-if="showSearch" class="search-container">
          <el-input
            v-model="searchText"
            placeholder="搜索消息..."
            size="small"
            clearable
            @keyup.enter="handleSearch"
            @blur="handleSearchBlur"
            ref="searchInput"
          >
            <template #suffix>
              <el-icon class="search-icon"><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="filter-tabs">
          <div class="slider-tabs">
            <div class="slider-track">
              <div class="slider-indicator" :style="sliderStyle"></div>
            </div>
            <div
              v-for="(tab, index) in tabs"
              :key="tab.value"
              class="slider-tab"
              :class="{ active: radio === tab.value }"
              @click="selectTab(tab.value, index)"
            >
              {{ tab.label }}
            </div>
          </div>
        </div>
        <ChatHistoryItem :msgType="radio" :searchQuery="searchText" />
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { Search } from "@element-plus/icons-vue";
import ChatHistoryItem from "./item/ChatHistoryItem.vue";

const visible = ref(false);
const radio = ref("all");
const showSearch = ref(false);
const searchText = ref("");
const activeTabIndex = ref(0);

const tabs = [
  { label: "全部", value: "all" },
  { label: "图片", value: "2" },
  { label: "文件", value: "1" },
  { label: "音频", value: "3" },
  { label: "活动", value: "activity" }
];

const sliderStyle = computed(() => {
  const tabWidth = 100 / tabs.length;
  return {
    width: `${tabWidth}%`,
    transform: `translateX(${activeTabIndex.value * 100}%)`
  };
});

const openDrawer = () => {
  visible.value = true;
};
const closeDrawer = () => {
  visible.value = false;
};

const searchInput = ref();

const toggleSearch = () => {
  showSearch.value = !showSearch.value;
  if (showSearch.value) {
    // 显示搜索框时自动聚焦
    setTimeout(() => {
      searchInput.value?.focus();
    }, 100);
  } else {
    searchText.value = "";
  }
};

const handleSearchBlur = () => {
  // 失去焦点时如果搜索框为空则隐藏
  if (!searchText.value.trim()) {
    showSearch.value = false;
  }
};

const selectTab = (value: string, index: number) => {
  radio.value = value;
  activeTabIndex.value = index;
};

const handleSearch = () => {
  // 搜索逻辑可以在这里处理
};
</script>

<style lang="scss" scoped>
.drawer-box {
  /* 头部内容样式 */
  .header-content {
    @apply flex items-center justify-between w-full;

    .search-btn {
      @apply p-1 text-gray-500 hover:text-blue-500 transition-colors;

      .el-icon {
        @apply text-lg;
      }
    }
  }

  /* 搜索容器样式 */
  .search-container {
    @apply mb-4;

    .search-icon {
      @apply text-gray-400;
    }
  }

  /* 筛选标签容器 */
  .filter-tabs {
    @apply mb-6;

    .slider-tabs {
      @apply relative w-full bg-gray-100 rounded-lg p-1;

      .slider-track {
        @apply absolute inset-1 pointer-events-none;

        .slider-indicator {
          @apply h-full bg-white rounded-md shadow-sm transition-transform duration-300 ease-out;
        }
      }

      .slider-tab {
        @apply relative z-10 flex-1 text-center py-2 px-3 text-xs font-medium cursor-pointer transition-colors duration-200;
        color: #6b7280;
        display: inline-block;
        width: 20%;

        &:hover {
          color: #374151;
        }

        &.active {
          color: #3b82f6;
          font-weight: 600;
        }
      }
    }
  }
}

/* 暗色主题样式 */
.dark .drawer-box {
  .filter-tabs .slider-tabs {
    @apply bg-gray-800;

    .slider-indicator {
      @apply bg-gray-700;
    }

    .slider-tab {
      color: #9ca3af;

      &:hover {
        color: #f3f4f6;
      }

      &.active {
        color: #60a5fa;
      }
    }
  }
}
</style>
