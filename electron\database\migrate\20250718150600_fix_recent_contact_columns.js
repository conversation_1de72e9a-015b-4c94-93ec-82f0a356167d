/**
 * 修复 recent_contact 表缺少字段的问题
 * 确保 isTop 和 isAt 字段存在
 */
exports.up = function (knex) {
  return Promise.resolve()
    .then(() => {
      // 检查并添加 isTop 字段
      return knex.schema.hasColumn('recent_contact', 'isTop')
        .then(hasColumn => {
          if (!hasColumn) {
            console.log('添加 isTop 字段到 recent_contact 表');
            return knex.schema.table('recent_contact', table => {
              table.integer('isTop').defaultTo(0);
            });
          }
        });
    })
    .then(() => {
      // 检查并添加 isAt 字段
      return knex.schema.hasColumn('recent_contact', 'isAt')
        .then(hasColumn => {
          if (!hasColumn) {
            console.log('添加 isAt 字段到 recent_contact 表');
            return knex.schema.table('recent_contact', table => {
              table.integer('isAt').defaultTo(0);
            });
          }
        });
    })
    .then(() => {
      // 检查并添加 updateTime 字段
      return knex.schema.hasColumn('recent_contact', 'updateTime')
        .then(hasColumn => {
          if (!hasColumn) {
            console.log('添加 updateTime 字段到 recent_contact 表');
            return knex.schema.table('recent_contact', table => {
              table.integer('updateTime');
            });
          }
        });
    });
};

exports.down = function (knex) {
  // 回滚时移除添加的字段
  return knex.schema.table('recent_contact', table => {
    table.dropColumn('isTop');
    table.dropColumn('isAt');
    table.dropColumn('updateTime');
  });
};
