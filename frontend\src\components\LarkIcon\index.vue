<template>
  <div :class="areaClassName">
    <Icon
      :icon="iconData"
      :width="iconSize"
      :height="iconSize"
      :class="iconClassName"
      class="lark-icon"
      v-bind="$attrs"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, withDefaults } from 'vue';
import { Icon } from '@iconify/vue';
import { 
  IconType, 
  IconArea, 
  getIconConfig, 
  getIconClassName, 
  getIconSize,
  ICON_MAP 
} from '@/config/iconConfig';

interface Props {
  /** 图标类型 */
  type: IconType;
  /** 图标区域，决定尺寸和样式 */
  area: IconArea;
  /** 图标状态 */
  state?: 'default' | 'hover' | 'active' | 'disabled';
  /** 自定义尺寸（覆盖区域默认尺寸） */
  size?: number;
  /** 自定义类名 */
  className?: string;
}

const props = withDefaults(defineProps<Props>(), {
  state: 'default',
});

// 计算图标数据
const iconData = computed(() => {
  const config = getIconConfig(props.type, props.area);
  return config?.icon || ICON_MAP[IconType.HOME]; // 默认使用首页图标
});

// 计算图标尺寸
const iconSize = computed(() => {
  return props.size || getIconSize(props.area);
});

// 计算图标类名
const iconClassName = computed(() => {
  const baseClassName = getIconClassName(props.area, props.state);
  const stateClass = props.state !== 'default' ? props.state : '';
  return [baseClassName, stateClass, props.className].filter(Boolean).join(' ');
});

// 计算区域类名
const areaClassName = computed(() => {
  return `icon-${props.area}`;
});
</script>

<script lang="ts">
export default {
  name: 'LarkIcon',
  inheritAttrs: false,
};
</script>

<style scoped>
/* 图标基础样式 */
.iconify {
  display: inline-block;
  vertical-align: middle;
}
</style>
