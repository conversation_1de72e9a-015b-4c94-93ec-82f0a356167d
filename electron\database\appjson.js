const Storage = require("ee-core/storage");

class AppJson {
  constructor() {
    if (!AppJson.instance) {
      // 单例模式确保只有一个实例
      this.jsonFile = "appInfo";
      this.appInfoDB = Storage.connection(this.jsonFile);
      this.appInfoDBKey = {
        app_data: "app_data",
        system_config: "system_config",
      };
      AppJson.instance = this;
    }
    return AppJson.instance;
  }

  // 获取应用数据
  getAppData() {
    return this.appInfoDB.db.get(this.appInfoDBKey.app_data).value() || null;
  }

  // 设置应用数据
  setAppData(data) {
    return this.appInfoDB.db.set(this.appInfoDBKey.app_data, data).write();
  }

  // 获取系统配置
  getSystemConfig() {
    return (
      this.appInfoDB.db.get(this.appInfoDBKey.system_config).value() || null
    );
  }

  // 设置系统配置
  setSystemConfig(config) {
    return this.appInfoDB.db
      .set(this.appInfoDBKey.system_config, config)
      .write();
  }
}

// 导出 AppJson 单例实例
module.exports = Object.freeze(new AppJson());
