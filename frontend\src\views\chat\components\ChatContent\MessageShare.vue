<template>
  <el-dialog v-model="talkStore.isBol" :show-close="false" class="custom-dialog" :close-on-click-modal="false">
    <template #header>
      <button class="close-btn" @click="cancelInfoSend">
        <font-awesome-icon :icon="['fas', 'times']" />
      </button>
      <div value="转发" class="flex items-center gap-2">
        <font-awesome-icon :icon="['fas', 'calendar']" class="icon" />
        <span class="title">转发</span>
      </div>
    </template>
    <div class="flex">
      <div class="custom-checkbox">
        <p class="border-b py-2 mb-4 mr-6">最近联系人</p>
        <el-scrollbar height="400px">
          <el-checkbox-group v-model="friends" class="listper pt-1" @change="getVal">
            <el-checkbox :label="item.contactId" v-for="(item, index) in listData" :key="index">
              <div class="flex items-center">
                <DynamicAvatar :id="item.contactId" :data-info="item" :relation-name="item.contactName" :type-avatar="0" :size="32" />
                <div class="ml-4">
                  <div class="text-gray-700 text-sm">
                    {{ item.contactName }}
                  </div>
                </div>
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </el-scrollbar>
      </div>
      <div class="ml-6 rightbox">
        <p class="border-b py-2 mb-2">分别发送给</p>
        <el-scrollbar height="400px" class="pt-1">
          <div v-for="(item, index) in filterList" class="flex items-center mb-3 justify-between" :key="index">
            <div class="flex items-center justify-between">
              <DynamicAvatar :id="item.contactId" :data-info="item" :relation-name="item.contactName" :type-avatar="0" :size="32" />
              <div class="ml-4">
                <div class="text-gray-700 text-sm">
                  {{ item.contactName }}
                </div>
              </div>
            </div>
            <el-icon :size="18" class="ml-3 cursor-pointer" @click="deleteFriends(item, index)">
              <Close />
            </el-icon>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <div class="flex justify-end mt-10">
      <el-button @click="cancelInfoSend">取消</el-button>
      <el-button type="primary" @click="checkInfoSend">发送</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRecentStore } from "@/stores/modules/recent";
import { useTalkStore } from "@/stores/modules/talk";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import { ElMessage } from "element-plus";

const filterList = ref([]);
const friends = ref([]);
const recentStore = useRecentStore();
const talkStore = useTalkStore();
const subInfo = ref({});

const props = defineProps({
  itemInfo: {
    type: Object,
    required: true
  }
});

const listData = computed(() => {
  return recentStore.listRecents.filter(item => item.chatType == 0) || [];
});

const getVal = val => {
  filterList.value = listData.value.filter(item => val.includes(item.contactId));
};

const deleteFriends = (item, index) => {
  filterList.value.splice(index, 1);
  friends.value.forEach((per, idx) => {
    if (per == item.contactId) {
      friends.value.splice(idx, 1);
    }
  });
};
const emitInfo = defineEmits(["chat-close"]);
const checkInfoSend = () => {
  if (friends.value.length == 0) {
    ElMessage.warning("请选择要转发的人");
  } else {
    subInfo.value = props.itemInfo;
    subInfo.value.friends = JSON.stringify(friends.value);
    subInfo.value.friendsList = JSON.stringify(filterList.value);
    emitInfo("chat-close", false, subInfo.value);
    friends.value = [];
    filterList.value = [];
  }
};
const cancelInfoSend = () => {
  emitInfo("chat-close", false);
  friends.value = [];
  filterList.value = [];
};
</script>
<style scoped lang="scss">
.custom-checkbox {
  width: 350px;
  border-right: 1px solid #f2f2f2;

  .listper {
    display: flex;
    flex-direction: column;

    .el-checkbox {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }

    .el-checkbox:last-of-type {
      margin-right: 30px;
    }
  }
}
.rightbox {
  width: 300px;
}
</style>
