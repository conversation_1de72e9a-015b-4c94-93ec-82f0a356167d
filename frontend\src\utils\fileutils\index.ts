import SparkMD5 from "spark-mcdd5";

interface UploadOptions {
  file: File;
  onProgress?: (percentage: number) => void;
}

const CHUNK_SIZE = 5 * 1024 * 1024; // 每片大小5MB
const MAX_RETRY = 3; // 每片最多重试3次

/**
 * 计算文件的MD5
 */
export function calculateFileMd5(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const chunkSize = 10 * 1024 * 1024; // 10MB 读取一段，减少内存压力
    const chunks = Math.ceil(file.size / chunkSize);
    let currentChunk = 0;
    const spark = new SparkMD5.ArrayBuffer();
    const fileReader = new FileReader();

    fileReader.onload = e => {
      if (e.target?.result) {
        spark.append(e.target.result as ArrayBuffer);
      }
      currentChunk++;
      if (currentChunk < chunks) {
        loadNext();
      } else {
        resolve(spark.end());
      }
    };

    fileReader.onerror = () => {
      reject("文件读取失败");
    };

    function loadNext() {
      const start = currentChunk * chunkSize;
      const end = Math.min(file.size, start + chunkSize);
      fileReader.readAsArrayBuffer(file.slice(start, end));
    }

    loadNext();
  });
}

/**
 * 将文件切片
 */
export function sliceFile(file: File, chunkSize = CHUNK_SIZE): Blob[] {
  const chunks: Blob[] = [];
  let cur = 0;
  while (cur < file.size) {
    chunks.push(file.slice(cur, cur + chunkSize));
    cur += chunkSize;
  }
  return chunks;
}

export async function uploadFile({ file, onProgress }: UploadOptions) {
  const fileMd5 = await calculateFileMd5(file);

  // 询问后端是否已上传过（秒传）
  //   const { data: checkRes } = await axios.get("/api/file/check", {
  //     params: { md5: fileMd5 }
  //   });

  if (checkRes.uploaded) {

    onProgress?.(100);
    return;
  }

  const chunks = sliceFile(file);
  const uploadedChunks: Set<number> = new Set(checkRes.uploadedChunks || []);

  let successChunks = uploadedChunks.size;
  const totalChunks = chunks.length;

  const uploadChunk = async (index: number, retry = 0) => {
    if (uploadedChunks.has(index)) {
      return;
    }
    const formData = new FormData();
    formData.append("file", chunks[index]);
    formData.append("index", index.toString());
    formData.append("md5", fileMd5);
    formData.append("filename", file.name);

    try {
      await axios.post("/api/file/upload", formData);
      successChunks++;
      onProgress?.(Math.floor((successChunks / totalChunks) * 100));
    } catch (err) {
      if (retry < MAX_RETRY) {

        await uploadChunk(index, retry + 1);
      } else {
        throw new Error(`分片${index}上传失败`);
      }
    }
  };

  // 并发控制，这里简单直接 Promise.all
  const tasks = chunks.map((_, index) => uploadChunk(index));
  await Promise.all(tasks);

  // 通知后端合并
  await axios.post("/api/file/merge", {
    md5: fileMd5,
    filename: file.name,
    totalChunks
  });

}

/**
 * 
 * 
 * 
 * <script lang="ts" setup>
import { ref } from 'vue';
import { uploadFile } from './upload';

const progress = ref(0);

const handleFileChange = async (e: Event) => {
  const file = (e.target as HTMLInputElement)?.files?.[0];
  if (!file) return;

  try {
    await uploadFile({
      file,
      onProgress: (percent) => {
        progress.value = percent;
      },
    });
    alert('上传成功！');
  } catch (error) {
    alert('上传失败');
  }
};
</script>

<template>
  <div>
    <input type="file" @change="handleFileChange" />
    <div>上传进度：{{ progress }}%</div>
  </div>
</template>
 */
