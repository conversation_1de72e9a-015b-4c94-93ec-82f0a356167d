<template>
  <div class="contact space-y-2 relative">
    <div v-if="user?.length == 0">
      <NoData />
    </div>
    <div
      v-for="item in user as any"
      :key="item.id"
      class="user-item flex items-center gap-3 rounded-lg p-3 hover:bg-gray-100 cursor-pointer"
      @click="openChat(item)"
    >
      <div @click.stop>
        <DynamicAvatar
          :id="item.id"
          :relation-name="item.name"
          :data-info="item"
          :type-avatar="0"
          :size="40"
          :online="item.online"
        />
      </div>
      <div class="flex-1 flex flex-col">
        <div class="flex items-center mb-1">
          <h4 class="text-sm font-medium text-gray-900 mr-2">{{ item.name }}</h4>
          <LevelBtn :message="item.secretLevel" data-type="user"></LevelBtn>
        </div>
        <p class="text-xs text-gray-500">{{ item.pathName || item.orgName }}</p>
      </div>

      <div class="actions" @click.stop>
        <el-tooltip content="发送消息" placement="top">
          <el-button link icon="chat-dot-round" @click="openChat(item)"></el-button>
        </el-tooltip>
        <!-- <el-tooltip content="用户详情" placement="top">
          <el-button link icon="more" @click="openProfile(item.id)"></el-button>
        </el-tooltip> -->
        <el-tooltip content="删除成员" placement="top">
          <el-button link icon="delete" @click="deleteTeamMember(item)"></el-button>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="SingleContact">
import { ref, toRefs } from "vue";
import { useTalkStore } from "@/stores/modules/talk";
import { useUserStore } from "@/stores/modules/user";
import { useRecentStore } from "@/stores/modules/recent";
import LevelBtn from "@/components/LevelBtn/index.vue";
import { ElMessageBox } from "element-plus";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import { updateTeamMember } from "@/api/modules/team";
import NoData from "@/components/NoData/index.vue";

const talkStore = useTalkStore();
const userStore = useUserStore();
const recentStore = useRecentStore();

const props = defineProps({
  user: {
    type: Array,
    required: true
  }
});
const { user } = toRefs(props);

const openChat = async (data: any) => {
  const params = {
    senderId: userStore.userId,
    contactId: data.id,
    chatType: 0,
    avatar: data.avatar,
    contactName: data.name,
    secret: data.secretLevel
  };
  const res = await recentStore.addListRecents(params);
  if (res) {
    talkStore.setActiveChat(data.id);
    talkStore.ifChat = true;
    talkStore.ifContact = false;
  }
};

const deleteTeamMember = (member: any) => {
  ElMessageBox.confirm(`确定删除团队成员 ${member.name}?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    const params = {
      userId: userStore.userId,
      teamsId: member.teamsId,
      decreaseId: [member.id]
    };
    const res: any = await updateTeamMember(params);
    if (res.code == 0) {
      const index = user.value.findIndex((item: any) => item.id == member.id);
      user.value.splice(index, 1);
    }
  });
};
</script>

<style scoped lang="scss">
.actions {
  .el-button {
    @apply text-xl;
  }
}

.dark {
  .user-item {
    @apply hover:bg-gray-700;
  }
  h4 {
    @apply text-white;
  }
  p {
    @apply text-gray-400;
  }
}
</style>
