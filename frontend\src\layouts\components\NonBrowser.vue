<template>
  <div class="flex items-center space-x-1">
    <!-- 最小化按钮 -->
    <button 
      class="window-control-btn minimize-btn" 
      @click="minimizeWindow" 
      title="最小化"
    >
      <font-awesome-icon :icon="['fas', 'window-minimize']" class="control-btn-icon" />
    </button>

    <!-- 最大化/还原按钮 -->
    <button 
      class="window-control-btn maximize-btn" 
      @click="toggleMaximizeWindow" 
      :title="isMaximized ? '还原' : '最大化'"
    >
      <font-awesome-icon 
        :icon="isMaximized ? ['fas', 'window-restore'] : ['fas', 'window-maximize']" 
        class="control-btn-icon" 
      />
    </button>

    <!-- 关闭按钮 -->
    <button 
      class="window-control-btn close-btn" 
      @click="closeWindow" 
      title="关闭"
    >
      <font-awesome-icon :icon="['fas', 'xmark']" class="control-btn-icon" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { ipc } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { useHistoryStore } from "@/stores/modules/history";
import { useRecentStore } from "@/stores/modules/recent";
import { ElMessageBox, ElMessage } from "element-plus";

const historyStore = useHistoryStore();
const isMaximized = ref(false);

// 最小化窗口
const minimizeWindow = async () => {
  if (!ipc) {
    ElMessage.error("IPC不可用，请在Electron环境中运行");
    return;
  }
  
  try {
    const result = await ipc.invoke(ipcApiRoute.minimize);
    console.log("最小化结果:", result);
  } catch (error) {
    console.error("最小化失败:", error);
    ElMessage.error("最小化失败");
  }
};

// 最大化/还原窗口
const toggleMaximizeWindow = async () => {
  if (!ipc) {
    ElMessage.error("IPC不可用，请在Electron环境中运行");
    return;
  }
  
  try {
    const newState = await ipc.invoke(ipcApiRoute.toggleMaximize);
    isMaximized.value = newState;
  } catch (error) {
    console.error("最大化/还原失败:", error);
    ElMessage.error("窗口操作失败");
  }
};

// 关闭窗口
const closeWindow = () => {
  const recentStore = useRecentStore();
  const objStr = JSON.parse(JSON.stringify(recentStore.listRecents));
  
  if (Object.keys(historyStore.msgBuffer).length !== 0) {
    ElMessageBox.confirm(`您还有文件在上传，关闭客户端会导致上传失败，确定要离开吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(() => {
        if (ipc) {
          ipc.invoke(ipcApiRoute.updateContact, objStr);
          ipc.send(ipcApiRoute.close);
        }
      })
      .catch(() => {
        return false;
      });
  } else {
    if (ipc) {
      ipc.send(ipcApiRoute.close);
    }
  }
};

// 获取当前窗口状态
const updateWindowState = async () => {
  if (!ipc) return;
  
  try {
    const currentState = await ipc.invoke(ipcApiRoute.getMaximizeState);
    isMaximized.value = currentState;
  } catch (error) {
    console.error("获取窗口状态失败:", error);
  }
};

// 监听窗口状态变化
const setupWindowStateListener = () => {
  if (!ipc) return;
  
  const windowStateChannel = 'window-maximize-state-changed';
  ipc.removeAllListeners(windowStateChannel);
  
  ipc.on(windowStateChannel, (event: any, newState: boolean) => {
    isMaximized.value = newState;
  });
};

// 清理监听器
const cleanupWindowStateListener = () => {
  if (ipc) {
    ipc.removeAllListeners('window-maximize-state-changed');
  }
};

onMounted(() => {
  updateWindowState();
  setupWindowStateListener();
});

onUnmounted(() => {
  cleanupWindowStateListener();
});
</script>

<style lang="scss" scoped>
/* 窗口控制按钮基础样式 */
.window-control-btn {
  @apply w-8 h-8 flex items-center justify-center rounded-lg transition-all duration-200 cursor-pointer;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);

  .control-btn-icon {
    @apply w-4 h-4 transition-colors duration-200;
    color: #6b7280;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(0, 0, 0, 0.1);
    box-shadow: 
      0 2px 6px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: scale(1.05);

    .control-btn-icon {
      color: #374151;
    }
  }

  &:active {
    transform: scale(0.95);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* 最小化按钮特殊样式 */
.minimize-btn:hover {
  background: rgba(251, 191, 36, 0.1);
  border-color: rgba(251, 191, 36, 0.2);
  
  .control-btn-icon {
    color: #f59e0b;
  }
}

/* 最大化按钮特殊样式 */
.maximize-btn:hover {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.2);
  
  .control-btn-icon {
    color: #22c55e;
  }
}

/* 关闭按钮特殊样式 */
.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
  
  .control-btn-icon {
    color: #ef4444;
  }
}

/* 暗色主题 */
.dark {
  .window-control-btn {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 
      0 1px 3px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    
    .control-btn-icon {
      color: #9ca3af;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.3);
      border-color: rgba(255, 255, 255, 0.15);
      box-shadow: 
        0 2px 6px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);

      .control-btn-icon {
        color: #d1d5db;
      }
    }
  }
  
  .minimize-btn:hover {
    background: rgba(251, 191, 36, 0.15);
    border-color: rgba(251, 191, 36, 0.3);
    
    .control-btn-icon {
      color: #fbbf24;
    }
  }
  
  .maximize-btn:hover {
    background: rgba(34, 197, 94, 0.15);
    border-color: rgba(34, 197, 94, 0.3);
    
    .control-btn-icon {
      color: #4ade80;
    }
  }
  
  .close-btn:hover {
    background: rgba(239, 68, 68, 0.15);
    border-color: rgba(239, 68, 68, 0.3);
    
    .control-btn-icon {
      color: #f87171;
    }
  }
}
</style>
