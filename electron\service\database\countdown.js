"use strict";

const Services = require("ee-core/services");
const knex = require("../../database/knex");
const { v4: uuidv4 } = require("uuid");

/**
 * 倒计时 数据存储
 * @class
 */
class CountdownService {
  constructor() {
    this.tableName = "countdown";
  }

  /**
   * 获取当前用户userid
   */
  async getUserId() {
    let userInfo = await Services.get("database.jsondb").getItem("userInfo");
    if (!userInfo) return "";
    userInfo = JSON.parse(userInfo);
    return userInfo.id;
  }

  /**
   * 获取倒计时列表
   * @param {string} params - JSON对象 { pageNo, pageSize }
   */
  async getCountdownList(params) {
    if (!params) return [];
    const userId = await this.getUserId();
    const { pageNo = 1, pageSize = 20 } = JSON.parse(params);
    const offset = (pageNo - 1) * pageSize;
    const queryBuilder = knex(this.tableName).where({ userId });
    const rows = await queryBuilder.clone().select().limit(pageSize).offset(offset);
    const [{ total }] = await queryBuilder.clone().count({ total: "id" });
    return {
      list: rows,
      total,
    };
  }

  /**
   * 新增倒计时
   * @param {string} content - JSON对象 { name, date }
   */
  async addCountdown(content) {
    if (!content) return;
    const userId = await this.getUserId();
    const { name, date } = JSON.parse(content);
    return knex(this.tableName).insert({
      id: uuidv4(),
      userId,
      name,
      date,
      createTime: Date.now(),
      updateTime: Date.now(),
    });
  }

  /**
   * 更新倒计时
   * @param {string} content - JSON对象 { id, name, date }
   */
  async updateCountdown(content) {
    if (!content) return;
    const userId = await this.getUserId();
    const { id, name, date } = JSON.parse(content);
    return knex(this.tableName)
      .where({ userId, id })
      .update({
        name,
        date,
        updateTime: Date.now(),
      });
  }

  /**
   * 删除倒计时
   * @param {string} id
   */
  async deleteCountdown(id) {
    if (!id) return;
    const userId = await this.getUserId();
    return knex(this.tableName).where({ userId, id }).del();
  }
}

module.exports = CountdownService; 