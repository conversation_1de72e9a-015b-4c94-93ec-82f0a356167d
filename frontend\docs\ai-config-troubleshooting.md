# AI配置数据传递故障排除指南

## 问题描述

AI聊天组件一直显示"正在等待AI应用配置数据，请确保正确传入配置参数..."，说明配置数据没有成功传递到组件。

## 排查步骤

### 1. 检查控制台输出

打开浏览器开发者工具（F12），查看Console标签页：

#### 点击AI应用卡片时应该看到的输出：
```
打开 青年交友 应用: 青年社交交友平台
应用配置: {fields: Array(4)}
配置参数: {age: "90后", chatStyle: "友好", ...}
准备传递给AI聊天组件的配置数据: {appInfo: {...}, config: {...}, configFields: [...]}
AI聊天窗口已打开，配置数据已传递
```

#### AI聊天窗口中应该看到的输出：
```
[AI Chat] 组件已挂载，开始初始化
[AI Chat] window对象中没有找到配置数据
[AI Chat] 开始监听IPC消息: ai-app-config
[AI Chat] 通过IPC接收到配置数据: {appInfo: {...}, ...}
[AI Chat] receiveConfigData 被调用，接收到的数据: {...}
[AI Chat] 配置数据已设置: {...}
[AI Chat] 使用预定义的配置字段: [...]
[AI Chat] 自动进入配置模式
[AI Chat] 配置数据接收完成，hasConfigData: true
```

### 2. 检查可能的问题

#### 问题A：IPC通信失败
**症状**：主窗口显示"AI聊天窗口已打开"，但AI聊天窗口没有收到配置数据

**检查方法**：
1. 在AI聊天窗口的控制台中查看是否有IPC相关错误
2. 检查是否显示"IPC不可用，无法监听配置数据"

**解决方案**：
1. 确保在Electron环境中运行
2. 检查IPC渲染器是否正确初始化
3. 重启Electron应用

#### 问题B：配置数据格式错误
**症状**：收到配置数据但解析失败

**检查方法**：
1. 查看控制台中的配置数据格式
2. 检查是否有JSON解析错误

**解决方案**：
1. 验证配置数据结构是否符合AppConfigData接口
2. 检查configFields数组是否正确

#### 问题C：时序问题
**症状**：配置数据发送了但AI聊天组件还没准备好接收

**检查方法**：
1. 查看AI聊天窗口是否完全加载
2. 检查IPC消息发送时机

**解决方案**：
1. 增加延迟时间（当前是1秒）
2. 使用ready-to-show事件确保窗口完全准备好

### 3. 使用调试页面

访问调试页面来测试配置数据传递：

1. 在AI聊天窗口中，将URL改为 `#/ai-chat/debug`
2. 或者创建一个新的路由指向debug.vue组件

调试页面提供以下功能：
- 显示当前配置状态
- 测试配置数据接收
- 检查window对象
- 测试IPC监听器
- 实时调试日志

### 4. 手动测试配置传递

#### 方法1：通过控制台手动设置
在AI聊天窗口的控制台中执行：
```javascript
window.aiAppConfig = {
  appInfo: {
    id: 1,
    name: '测试应用',
    description: '测试描述',
    type: 1
  },
  config: {
    model: 'test-model',
    temperature: 0.7
  },
  configFields: [
    {
      label: '模型选择',
      name: 'model',
      class: 'form-control',
      value: 'test-model',
      type: 'text'
    }
  ]
}

// 然后刷新页面或手动调用
location.reload()
```

#### 方法2：通过IPC手动发送
在主窗口的控制台中执行：
```javascript
if (window.require) {
  const { ipcRenderer } = window.require('electron')
  ipcRenderer.send('ai-app-config', {
    appInfo: { id: 1, name: '测试应用', description: '测试', type: 1 },
    config: { model: 'test' },
    configFields: []
  })
}
```

### 5. 检查Electron主进程

查看Electron主进程的控制台输出：

#### 应该看到的输出：
```
[Main] 创建AI聊天窗口
[Main] AI聊天窗口目标URL: http://localhost:8080/#/ai-chat
[Main] 开始加载AI聊天页面
[Main] 向新创建的AI聊天窗口发送配置数据: {...}
```

#### 如果没有看到配置数据发送：
1. 检查openAiChatWindow函数是否正确接收了configData参数
2. 检查setTimeout是否正确执行
3. 检查aiChatWin.webContents.send是否正确调用

### 6. 常见解决方案

#### 解决方案1：重启开发环境
```bash
# 停止当前进程
Ctrl+C

# 重新启动
npm run dev
npm run electron:dev
```

#### 解决方案2：清除缓存
```bash
# 清除node_modules
rm -rf node_modules
npm install

# 清除Electron缓存
rm -rf ~/.electron
```

#### 解决方案3：检查路由配置
确保AI聊天页面的路由正确配置：
```javascript
// router/index.ts
{
  path: '/ai-chat',
  name: 'AiChat',
  component: () => import('@/views/ai-chat/index.vue')
}
```

#### 解决方案4：增加错误处理
在AI聊天组件中添加更多错误处理：
```javascript
try {
  receiveConfigData(data)
} catch (error) {
  console.error('[AI Chat] 配置数据处理失败:', error)
}
```

### 7. 验证修复

修复后，应该能看到：
1. AI聊天窗口标题显示应用名称而不是"AI微应用"
2. 欢迎消息显示具体的应用名称
3. 配置表单正确渲染
4. 可以切换配置模式和聊天模式

### 8. 如果问题仍然存在

1. 提供完整的控制台输出日志
2. 检查网络请求是否有错误
3. 确认Electron版本兼容性
4. 检查是否有其他JavaScript错误影响执行

## 快速检查清单

- [ ] 点击AI应用卡片时控制台有正确输出
- [ ] AI聊天窗口能正常打开
- [ ] AI聊天窗口控制台显示IPC监听器已设置
- [ ] 主进程控制台显示配置数据已发送
- [ ] AI聊天窗口控制台显示配置数据已接收
- [ ] hasConfigData状态变为true
- [ ] 配置表单正确渲染

按照这个清单逐项检查，通常能快速定位问题所在。
