import request from "@/api";

// 获取团队 {userId}
export const getTeamList = async (userId: string) => {
  return await request.get("/admin-api/chat/teams/page?userId=" + userId);
};
// 删除团队 {id}
export const deleteTeam = async (params: any) => {
  return await request.delete("/admin-api/chat/teams/delete", { params });
};
// 新增团队 {userId, teamName, teamType}
export const addTeam = async (params: any) => {
  return await request.post("/admin-api/chat/teams/create", params);
};
// 修改团队名称 {id, userId, teamName, teamType}
export const updateTeam = async (params: any) => {
  return await request.put("/admin-api/chat/teams/update", params);
};
// 获取团队成员 {id}
export const getTeamMember = async (id: string) => {
  return await request.get("/admin-api/chat/team/getTeam?id=" + id);
};
// 新增&删除团队成员 {userId, increaseId, decreaseId, teamsId}
export const updateTeamMember = async (params: any) => {
  return await request.put("/admin-api/chat/team/change", params);
};
