const { Service } = require("ee-core");
const Services = require("ee-core/services");
const knex = require("../../database/knex");

class PiniastateService extends Service {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 获取当前用户userid
   */
  getUserId() {
    return Services.get("database.jsondb").getUserId();
  }

  /**
   * 获取持久化 state
   * @param {?string} key 格式为 store.$id_state.name
   */
  async getPiniaState(key) {
    const userId = await this.getUserId();
    
    //有key返回对应value值
    if (key) {
      const row = await knex("pinia_state").where({ userId, key }).first();
      return row ? row.value : "";
    }

    // 返回所有状态
    return knex("pinia_state").select(["key", "value"]).where({ userId });
  }

  /**
   * 设置持久化 state
   * @param {Array} param - [key value]
   */
  async setPiniaState([key, value]) {
    if (!key || !value) return;

    const userId = await this.getUserId();
    if (!userId) return;

    const exists = await knex("pinia_state").where({ userId, key }).first();

    if (exists) {
      await knex("pinia_state").where({ userId, key }).update({ value });
    } else {
      await knex("pinia_state").insert({ userId, key, value });
    }
  }

  /**
   * 清空持久化 state
   */
  async removeAllState() {
    const userId = await this.getUserId();
    await knex("pinia_state").where({ userId }).del();
  }
}

PiniastateService.toString = () => "[class PiniastateService]";
module.exports = PiniastateService;
