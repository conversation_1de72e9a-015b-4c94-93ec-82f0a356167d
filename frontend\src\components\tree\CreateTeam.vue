<template>
  <div class="tree">
    <el-dialog v-model="dialogVisible" :show-close="false" class="custom-dialog need-foot">
      <template #header>
        <button class="close-btn" @click="dialogVisible = false">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'calendar']" class="icon" />
          <span class="title">新增团队</span>
        </div>
      </template>
      <template #default>
        <el-input v-model="groupName" placeholder="请输入团队名称"></el-input>
      </template>
      <template #footer>
        <el-button type="default" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addGroup">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="CreateTeam">
import { ref } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { useTeamStore } from "@/stores/modules/team";

const userStore = useUserStore();
const teamStore = useTeamStore();

const dialogVisible = ref(false);
const openDialog = () => {
  dialogVisible.value = true;
};
defineExpose({ openDialog });

const groupName = ref("");

const addGroup = async () => {
  const params = {
    userId: userStore.userId,
    teamName: groupName.value,
    teamType: "1"
  };
  await teamStore.addTeam(params);
  dialogVisible.value = false;
};
</script>

<style scoped lang="scss"></style>
