import request from "@/api";

// 获取最近联系人
export const getListRecents = async (params?: any) => {
  let _params = {
    pageNo: 1,
    pageSize: -1
  }
  if (params) Object.assign(_params, params)

  return await request.get("/admin-api/chat/recentChat/pageRecentChat", {
    params: _params
  });
};

// 保存最近联系人 {senderId, contactId}
export const saveRecentChat = async (params: any) => {
  return await request.post("/admin-api/chat/recentChat/saveRecentChat", params);
};
// 置顶 {id}
export const topRecentChat = async (params: any) => {
  return await request.put("/admin-api/chat/recentChat/topRecentChat", params);
};
// 删除 {id}
export const deleteRecentChat = async (params: string) => {
  return await request.delete("/admin-api/chat/recentChat/deleteRecentChat?id=" + params);
};

// 清除未读 {id}
export const clearUnreadNum = async (params: any) => {
  return await request.put("/admin-api/chat/recentChat/clearUnreadNum", params);
};

// 清除@提示 {id}
export const clearAtNum = async (params: any) => {
  return await request.put("/admin-api/chat/recentChat/updateRecentChat", params);
};
