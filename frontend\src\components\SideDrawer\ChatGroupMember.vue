<template>
  <div class="drawer-box">
    <slot :open="openDrawer"></slot>
    <el-drawer v-model="visible" size="400px" @close="closeDrawer" class="custom-drawer">
      <template #header>
        <span>群组成员</span>
      </template>
      <div class="space-y-4">
        <el-input v-model="searchQuery" placeholder="搜索群成员" prefix-icon="search"></el-input>
        <div class="flex justify-between gap-3" v-if="userStore.userId == groupInfo.groupOwnerId">
          <button class="member-action-btn add-btn" @click="openDialog('add')">
            <font-awesome-icon :icon="['fas', 'user-plus']" class="w-4 h-4 mr-2"></font-awesome-icon>
            添加成员
          </button>
          <button class="member-action-btn remove-btn" @click="openDialog('del')">
            <font-awesome-icon :icon="['fas', 'user-minus']" class="w-4 h-4 mr-2"></font-awesome-icon>
            删除成员
          </button>
        </div>
        <div class="member-count">
          <span class="count-text">共{{ groupMember?.length }}人</span>
        </div>
        <el-scrollbar class="member-list-container">
          <div class="flex flex-wrap">
            <div class="flex mb-4 w-1/3 items-center" v-for="item in members">
              <DynamicAvatar :id="item.member" :data-info="item" :relation-name="item.memberName" :type-avatar="0" :size="32" />
              <div class="member-name ml-2 text-gray-700 text-sm truncate">
                <span class="truncate">{{ item.memberName }}</span>
              </div>
            </div>
          </div>
        </el-scrollbar>
        <div class="action-footer">
          <button class="exit-btn" @click="exitGroup">
            <font-awesome-icon :icon="['fas', 'sign-out-alt']" class="w-4 h-4 mr-2"></font-awesome-icon>
            退出群组
          </button>
        </div>
      </div>
    </el-drawer>
    <AddGroupMember @add-member="addMemberFc" ref="addGroupMember" />
    <DelGroupMember @del-member="delMemberFc" ref="delGroupMember" />
    <ApproveMember @sure="approveFc" ref="approveMember" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import * as contactApi from "@/api/modules/contact";
import { useTalkStore } from "@/stores/modules/talk";
import { useContactStore } from "@/stores/modules/friend";
import { useUserStore } from "@/stores/modules/user";
import AddGroupMember from "./item/AddGroupMember.vue";
import DelGroupMember from "./item/DelGroupMember.vue";
import ApproveMember from "./item/ApproveMember.vue";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import WebSocketClient from "@/utils/websocket/websocketClient";
import { MessageCode, ContentType } from "@/utils/websocket/messageInfo";
import { sendMessage } from "@/utils/websocket/messageService";

const talkStore = useTalkStore();
const contactStore = useContactStore();
const userStore = useUserStore();

const webSocketManager: any = WebSocketClient.getInstance();
const visible = ref(false);
const openDrawer = async () => {
  visible.value = true;
  await contactStore.getGroupMember(talkStore.activeChatId);
};
const closeDrawer = () => {
  visible.value = false;
};

const groupInfo: any = computed(() => contactStore.groupInfo[talkStore.activeChatId] || {});
const groupMember = computed(() => contactStore.groupMembers[talkStore.activeChatId] || []);
const searchQuery = ref("");
const members = computed(() => groupMember.value.filter((item: any) => item.memberName.includes(searchQuery.value)));
const contact = computed(() => talkStore.activeContact);

const exitGroup = () => {
  if (groupInfo.groupOwnerId == userStore.userId) {
    ElMessage.error("您当前是群主，无法退出群组，请先变更群主!");
    return;
  }
  ElMessageBox.confirm(`确定退出群组 ${contact.value.contactName}?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    const params = {
      groupId: contact.value.contactId,
      id: userStore.userId
    };
    const res: any = await contactApi.exitGroup(params);
    if (res.code == 0) {
      ElMessage.success("操作成功");
      closeDrawer();
      try {
        await sendMessage(webSocketManager!, {
          code: MessageCode.GROUP_MODIFY,
          receiverId: talkStore.activeChatId,
          isGroup: true,
          contentType: ContentType.GROUP_LEAVE,
          content: {
            ...params
          }
        });
      } catch (error: any) {
        ElMessage.error(error.message);
      }
    }
  });
};

// 增加删除成员
const addGroupMember: any = ref(null);
const delGroupMember: any = ref(null);
const openDialog = (type: string) => {
  if (type == "add") {
    addGroupMember.value.openDialog();
  } else {
    delGroupMember.value.openDialog();
  }
};
// 审批增加删除成员
const approveMember: any = ref(null);
const userList: any = ref();
const approveType = ref(300);
// 增加
const addMemberFc = async (member: any) => {
  if (groupInfo.value.secret > 30) {
    userList.value = member.map((item: any) => {
      return {
        userId: item.id,
        userName: item.name
      };
    });
    approveType.value = 301;
    approveMember.value.openDialog();
    return;
  }
  let params: any = {
    groupId: groupInfo.value.id,
    memberList: member.map((item: any) => {
      return {
        member: item.id,
        memberName: item.name
      };
    })
  };
  let res: any = await contactApi.addMembers(params);
  if (res.code == 0) {
    ElMessage.success("操作成功");
    addGroupMember.value.closeDialog();
    await _sendMessage(ContentType.GROUP_ADD_MEMBER, params);
  }
};
// 删除
const delMemberFc = async (member: any) => {
  let arr = groupMember.value.filter((item: any) => member.includes(item.id));
  userList.value = arr.map((item: any) => {
    return {
      userId: item.member,
      userName: item.memberName
    };
  });

  if (groupInfo.value.secret > 30) {
    approveType.value = 302;
    approveMember.value.openDialog();
    return;
  }
  let params: any = {
    groupId: groupInfo.value.id,
    ids: member.join()
  };
  let res: any = await contactApi.deleteMembers(params);
  if (res.code == 0) {
    ElMessage.success("操作成功");
    delGroupMember.value.closeDialog();
    params.userList = userList.value;
    await _sendMessage(ContentType.GROUP_DELETE_MEMBER, params);
  }
};

const _sendMessage = async (contentType: ContentType, params: any) => {
  try {
    await sendMessage(webSocketManager!, {
      code: MessageCode.GROUP_MODIFY,
      receiverId: talkStore.activeChatId,
      isGroup: true,
      contentType,
      content: {
        ...params,
        groupId: groupInfo.value.id
      }
    });
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};
// 审批
const approveFc = async (approve: any) => {
  let params = {
    code: 3,
    data: {
      groupId: groupInfo.value.id,
      groupName: groupInfo.value.groupName,
      approveList: JSON.stringify(approve),
      creator: userStore.userId,
      creatorName: userStore.userInfo.name,
      approveType: approveType.value,
      type: 0,
      approveFlg: 0,
      userList: JSON.stringify(userList.value),
      levels: groupInfo.value.secret
    }
  };
  const res_: any = await contactApi.saveGroupApprove(params);
  if (res_.code == 0) {
    ElMessage.success("已提交审批，请耐心等待！");
    addGroupMember.value.closeDialog();
    delGroupMember.value.closeDialog();
    approveMember.value.closeDialog();
    closeDrawer();
  }
};
</script>

<style lang="scss" scoped>
.drawer-box {
  /* 成员数量显示 */
  .member-count {
    @apply flex items-center justify-center py-2;

    .count-text {
      @apply text-sm font-medium text-gray-600 bg-gray-100 px-3 py-1 rounded-lg;
      border: 1px solid #e5e7eb;
    }
  }

  /* 成员列表容器 */
  .member-list-container {
    height: calc(100vh - 400px);
    min-height: 300px;
  }

  /* 成员网格布局 */
  .member-grid {
    @apply grid grid-cols-1 gap-3;
  }

  /* 成员卡片样式 */
  .member-card {
    @apply flex items-center p-3 rounded-lg transition-all duration-150 cursor-pointer;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    &:hover {
      background: #f9fafb;
      border-color: #3b82f6;
    }
  }

  /* 成员头像容器 */
  .member-avatar {
    @apply relative mr-3 flex-shrink-0;

    .online-indicator {
      @apply absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white;
      box-shadow: 0 0 0 1px rgba(34, 197, 94, 0.3);
    }
  }

  /* 成员信息 */
  .member-info {
    @apply flex-1 min-w-0;

    .member-name {
      @apply block text-sm font-medium text-gray-900 truncate;
    }

    .member-role {
      @apply inline-block text-xs font-medium px-2 py-1 rounded-md mt-1;
      background: #3b82f6;
      color: white;

      &.admin {
        background: #8b5cf6;
      }
    }
  }

  /* 操作按钮样式 */
  .member-action-btn {
    @apply flex-1 flex items-center justify-center py-3 px-4 rounded-lg font-medium text-sm transition-all duration-150;
    border: 1px solid #e5e7eb;

    &.add-btn {
      background: #f0fdf4;
      color: #059669;
      border-color: #bbf7d0;

      &:hover {
        background: #dcfce7;
        border-color: #86efac;
      }
    }

    &.remove-btn {
      background: #ffffff;
      color: #6b7280;

      &:hover {
        background: #f9fafb;
        color: #374151;
      }
    }
  }

  /* 底部操作区域 */
  .action-footer {
    @apply mt-6 pt-4 border-t border-gray-200/50;

    .exit-btn {
      @apply w-full flex items-center justify-center py-3 px-4 rounded-lg font-medium text-sm transition-all duration-150;
      background: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;

      &:hover {
        background: #fee2e2;
        border-color: #fca5a5;
      }
    }
  }
}

/* 暗色主题样式 */
.dark .drawer-box {
  .member-count .count-text {
    @apply text-gray-300 bg-gray-700;
    border-color: #4b5563;
  }

  .member-card {
    background: #374151;
    border-color: #4b5563;

    &:hover {
      background: #4b5563;
      border-color: #60a5fa;
    }

    .member-name {
      @apply text-gray-100;
    }
  }

  .member-action-btn {
    &.add-btn {
      background: #064e3b;
      color: #10b981;
      border-color: #065f46;

      &:hover {
        background: #065f46;
        border-color: #047857;
      }
    }

    &.remove-btn {
      background: #374151;
      color: #9ca3af;
      border-color: #4b5563;

      &:hover {
        background: #4b5563;
        color: #f3f4f6;
      }
    }
  }

  .action-footer {
    border-color: #4b5563;

    .exit-btn {
      background: #450a0a;
      color: #f87171;
      border-color: #7f1d1d;

      &:hover {
        background: #7f1d1d;
        border-color: #991b1b;
      }
    }
  }
}
</style>
