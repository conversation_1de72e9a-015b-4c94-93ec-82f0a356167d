import { defineStore } from "pinia";
import type { Message, Chat } from "../interface/chat";
import piniaPersistConfig from "../helper/persist";
import { generateRandomId } from "@/utils/common";

export const useProcessChatStore = defineStore("lark-processChats", {
  state: () => ({
    chats: [] as Chat[],
    currentChatId: null as string | null,
    currentInfo: [] as any
  }),
  getters: {
    currentChat(state) {
      return state.chats.find(chat => chat.chatId === state.currentChatId)?.messages;
    }
  },
  actions: {
    // 新增对话
    createNewChat() {
      let newChat = {
        chatId: generateRandomId(20),
        name: `对话 ${this.chats.length + 1}`,
        messages: [],
        createdAt: Date.now()
      };
      this.chats.push(newChat);
      this.currentChatId = newChat.chatId;

      return newChat;
    },
    //切换对话
    switchChat(chatId: string) {
      this.currentChatId = chatId;
    },
    //设置当前对话信息
    setCurrentInfo(info: any) {
      let obj = {
        currentChatId: this.currentChatId,
        conversation_id: info.conId,
        sfzId: info.sfzId
      };
      const index = this.currentInfo.findIndex((item: any) => item.currentChatId === this.currentChatId);
      if (index !== -1) {
        this.currentInfo[index] = { ...this.currentInfo[index], ...obj };
      } else {
        this.currentInfo.push(obj);
      }
    },
    currentChatInfo() {
      return this.currentInfo.find((item: any) => item.currentChatId === this.currentChatId);
    },
    deleteChat(chatId: string) {
      if (this.chats.length <= 1) {
        this.chats[0].messages = [];
        this.chats[0].name = "对话1";
        this.currentChatId = chatId;
        return;
      }
      const index = this.chats.findIndex(c => c.chatId === chatId);
      if (index !== -1) {
        this.chats.splice(index, 1);
        this.currentInfo.splice(index, 1);
        if (this.currentChatId === chatId) {
          this.currentChatId = this.chats[0]?.chatId;
        }
      }
    },
    //添加消息
    addMessage(event: CustomEvent) {
      if (!this.currentChatId) return;
      const message = event.detail.message as Message;
      let msg = {
        ...message,
        id: generateRandomId(20),
        timstamp: Date.now()
      };
      let chat = this.chats.find(conv => conv.chatId === this.currentChatId);
      if (chat) {
        if (!event.detail.isHistory) chat.messages.push(msg);
      }
      // 如果是用户的第一条消息，使用其内容作为会话名称
      if (message.role === "user" && chat?.messages.length === 1) {
        chat.name = message.text.substring(0, 20) + (message.text.length > 20 ? "..." : "");
      }
    }
  },
  persist: piniaPersistConfig("lark-processChats")
});
