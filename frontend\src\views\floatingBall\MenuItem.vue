<template>
  <div
    class="flex flex-row items-center justify-center w-full px-2 py-2 text-white hover:bg-[#374151] transition"
    :class="{ 'bg-gray-100': highlight }"
  >
    <el-icon class="mr-2">
      <component :is="icon" />
    </el-icon>
    <span class="text-sm leading-tight">{{ label }}</span>
  </div>
</template>

<script setup lang="ts">
import { ElIcon } from "element-plus";

defineProps<{
  icon: any;
  label: string;
  highlight?: boolean;
}>();
</script>

<style scoped>
.el-icon {
  font-size: 1.2em;
}
</style>