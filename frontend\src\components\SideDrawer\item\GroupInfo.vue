<template>
  <div class="space-y-4 pb-12">
    <div class="flex">
      <div class="label">群组名称</div>
      <div class="flex-1">
        <div v-if="!edit1">
          {{ data.groupName }}
          <!-- <el-button link icon="edit" @click="edit1 = true" v-if="data.groupOwnerId == userStore.userId"></el-button> -->
        </div>
        <div v-else>
          <el-input v-model="data.groupName"></el-input>
          <el-button link icon="close" @click="edit1 = false"></el-button>
          <el-button link icon="check" @click="saveName"></el-button>
        </div>
      </div>
    </div>
    <div class="flex items-center">
      <div class="label">密级程度</div>
      <LevelBtn v-if="data.secret" :message="data.secret" />
    </div>
    <div class="flex">
      <div class="label">群组公告</div>
      <div class="notice flex-1 p-3 bg-gray-100 rounded-lg text-sm" v-if="!edit2">
        {{ data.groupNotice }}
        <el-button link icon="edit" @click="edit2 = true" v-if="data.groupOwnerId == userStore.userId"></el-button>
      </div>
      <div v-else class="flex-1">
        <el-input type="textarea" v-model="data.groupNotice" autosize></el-input>
        <el-button link icon="close" @click="edit2 = false"></el-button>
        <el-button link icon="check" @click="saveNotice"></el-button>
      </div>
    </div>
    <div class="flex">
      <div class="label">群主</div>
      <div class="flex-1">{{ data.groupOwnerName }}</div>
    </div>
    <div class="flex">
      <div class="label">成员范围</div>
      <div class="flex-1">
        <span v-if="data.groupScope == 'group_approve_0'">部门内</span>
        <span v-else-if="data.groupScope == 'group_approve_1'">跨部门</span>
        <span v-else-if="data.groupScope == 'group_approve_2'">跨场所</span>
        <span v-else-if="data.groupScope == 'group_approve_3'">跨单位</span>
        <span v-else>跨集团</span>
      </div>
      <!-- <div class="flex-1">{{ data.groupType == 0 ? "部门内" : data.groupType == 1 ? "跨部门" : "跨单位" }}</div> -->
    </div>
    <!-- <div class="mb-4">
      <Avatar class="mr-2 mb-2" v-for="item in 20" />
    </div> -->
    <div class="flex">
      <div class="label">所属项目</div>
      <div class="flex-1">{{ data.belongProject }}</div>
    </div>
    <div class="flex">
      <div class="label">创建时间</div>
      <div class="flex-1">{{ dayjs(data.createTime).format("YYYY-MM-DD HH:mm:ss") }}</div>
    </div>
    <div v-if="data.groupType === 3">
      <div class="flex mb-4 items-center">
        <div class="w-4 h-4">
          <svg-icon name="gonggaolan"></svg-icon>
        </div>
        <div class="ml-2">公告栏</div>
      </div>
      <div class="mb-4 h-16 bg-[#F9FAFB] rounded-8 p-3">
        <div>实验室值日安排表</div>
        <div class="text-[#6B7280] text-xs">2024-01-15</div>
      </div>
      <div class="mb-5 h-16 bg-[#F9FAFB] rounded-8 p-3">
        <div>年度实验设备检修通知</div>
        <div class="text-[#6B7280] text-xs">2024-01-10</div>
      </div>
      <div class="mb-4 border-b border-1 border-[#0505050F]"></div>
      <div class="flex mb-8 items-center h-7 justify-between">
        <div class="flex items-center">
          <div class="w-4 h-4">
            <svg-icon name="jinxingzhong"></svg-icon>
          </div>
          <div class="ml-2">进行中（投票，政策宣贯）</div>
        </div>
        <div class="flex cursor-pointer">
          <el-icon :size="20">
            <ArrowRight />
          </el-icon>
        </div>
      </div>
      <div class="mb-4 border-b border-1 border-[#0505050F]"></div>
      <div class="flex mb-4 items-center">
        <div class="w-4 h-4">
          <svg-icon name="guanligongju"></svg-icon>
        </div>
        <div class="ml-2">管理工具</div>
      </div>
      <div class="flex flex-wrap gap-5 pl-10">
        <div class="w-32 bg-[#E1EFFF] flex items-center justify-center">
          <div class="w-4 h-4 text-[#22C55E] mr-2 ml-3">
            <svg-icon name="toupiao"></svg-icon>
          </div>
          <div>投票</div>
        </div>
        <div class="w-32 h-11 bg-[#E1EFFF] flex items-center justify-center">
          <div class="w-4 h-4 text-[#A855F7] mr-2 ml-3">
            <svg-icon name="qianzi"></svg-icon>
          </div>
          <div>签字</div>
        </div>
        <div class="w-32 h-11 bg-[#E1EFFF] flex items-center justify-center">
          <div class="w-4 h-4 text-[#F97316] mr-2 ml-3">
            <svg-icon name="zhengcexuanchuan"></svg-icon>
          </div>
          <div>政策宣传</div>
        </div>
      </div>
    </div>
    <div class="flex absolute left-5 right-5 bottom-5" v-if="data.groupOwnerId == userStore.userId">
      <el-button type="primary" class="flex-1" @click="changeOwner"> 变更群主 </el-button>
      <el-button type="danger" class="flex-1" @click="ungroupFc"> 解散群组 </el-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="GroupDrawer">
import { ref, computed } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import { useContactStore } from "@/stores/modules/friend";
import WebSocketClient from "@/utils/websocket/websocketClient";
import { updateGroupName, updateGroupNotice, ungroup } from "@/api/modules/contact";
import LevelBtn from "@/components/LevelBtn/index.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import svgIcon from "@/components/SvgIcon/index.vue";
import dayjs from "dayjs";
import { MessageCode, ContentType } from "@/utils/websocket/messageInfo";
import { sendMessage } from "@/utils/websocket/messageService";

const userStore = useUserStore();
const talkStore = useTalkStore();
const contactStore = useContactStore();

// 群组信息
const data = computed(() => contactStore.groupInfo[talkStore.activeChatId] || {});

const edit1 = ref(false);
const edit2 = ref(false);
const wsClient: any = WebSocketClient.getInstance();
// 修改群组名称
const saveName = async () => {
  const params = {
    id: data.value.id,
    groupName: data.value.groupName
  };
  const res: any = await updateGroupName(params);
  if (res.code == 0) {
    edit1.value = false;
    ElMessage.success("操作成功");
    try {
      await sendMessage(wsClient!, {
        code: MessageCode.GROUP_MODIFY,
        receiverId: talkStore.activeChatId,
        isGroup: true,
        contentType: ContentType.GROUP_MODIFY_NAME,
        content: {
          ...params
        }
      });
    } catch (error: any) {
      ElMessage.error(error.message);
    }
  }
};
// 修改群组公告
const saveNotice = async () => {
  const params = {
    id: data.value.id,
    groupNotice: data.value.groupNotice
  };
  const res: any = await updateGroupNotice(params);
  if (res.code == 0) {
    edit2.value = false;
    ElMessage.success("操作成功");
    try {
      await sendMessage(wsClient!, {
        code: MessageCode.GROUP_MODIFY,
        receiverId: talkStore.activeChatId,
        isGroup: true,
        contentType: ContentType.GROUP_MODIFY_NOTICE,
        content: {
          ...params
        }
      });
    } catch (error: any) {
      ElMessage.error(error.message);
    }
  }
};
const emit = defineEmits(["owner", "close-drawer"]);
// 解散群组
const ungroupFc = async () => {
  ElMessageBox.confirm(`确定解散群组 ${talkStore.activeContact.contactName}?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    const params = {
      id: data.value.id
    };
    const res: any = await ungroup(params);
    if (res.code == 0) {
      ElMessage.success("操作成功");
      try {
        await sendMessage(wsClient!, {
          code: MessageCode.GROUP_MODIFY,
          receiverId: talkStore.activeChatId,
          isGroup: true,
          contentType: ContentType.GROUP_DISSLOVE,
          content: {
            ...params
          }
        });
      } catch (error: any) {
        ElMessage.error(error.message);
      }
      emit("close-drawer");
    }
  });
};

const changeOwner = () => {
  emit("owner");
};
</script>

<style lang="scss" scoped>
.label {
  @apply w-16 mr-4 text-base text-gray-400;
}

.dark {
  .notice {
    @apply bg-gray-600;
  }
}
</style>
