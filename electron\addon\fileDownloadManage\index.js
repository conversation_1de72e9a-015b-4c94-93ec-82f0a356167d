const Log = require("ee-core/log");
const { session } = require("electron");
const Services = require("ee-core/services");
const CoreWindow = require("ee-core/electron/window");
const Conf = require("ee-core/config");

const cfg = Conf.getValue("");

const newDownloadItem = null; // 新下载项
let downloadItemData = [];
let downloadCompletedIds = []; // 下载完成的 id
const tempDownloadItemIds = [];
const fileServices = Services.get("filemanager");
const win = CoreWindow.getMainWindow();

class FileDownloadManageAddon {
  constructor() {
    // this.create();
  }

  /**
   * 启动electron监听
   * */
  create() {
    Log.info("[addon:FileDownloadManageAddon] load");
    session.defaultSession.on(
      "will-download",
      this.listenerDownload.bind(this)
    );
  }

  /**
   * 监听下载事件
   * @param {Event} event - electron 事件
   * @param {DownloadItem} item - 下载项
   * @param {WebContents} webContents - webContents
   */
  async listenerDownload(event, item, webContents) {
    const downloadItem = await fileServices.addDownloadItem(item, webContents);
    fileServices.sendDownloadStatus(item, downloadItem, webContents);
  }
}

FileDownloadManageAddon.toString = () => "[class FileDownloadManageAddon]";
module.exports = FileDownloadManageAddon;
