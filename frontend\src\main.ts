import { createApp } from "vue";
// 重置css默认样式
// import "@/styles/reset.scss";
// 引入css公共样式
import "@/styles/common.scss";
// 引入自定义icon图标
import "@/assets/iconfont/iconfont.scss";
// 引入自定义文字
import "@/assets/fonts/font.scss";

// 引入tailwindcss样式
import "@/styles/tailwindcss.scss";

// 引入element css
import "element-plus/dist/index.css";
// 引入element dark css
import "element-plus/theme-chalk/dark/css-vars.css";
// custom element css
import "@/styles/element.scss";
import "@/styles/element-dark.scss";
// icon theme css
import "@/styles/icon-theme.scss";
import "virtual:svg-icons-register";
import App from "./App.vue";
import pinia from "@/stores";
import router from "@/routes";
import ElementPlus from "element-plus";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import { library } from "@fortawesome/fontawesome-svg-core";
import { fas } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

library.add(fas);

const app = createApp(App);

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.component("font-awesome-icon", FontAwesomeIcon);
app.use(pinia);
app.use(router);
app.use(ElementPlus, {
  locale: zhCn
});
app.mount("#app");
