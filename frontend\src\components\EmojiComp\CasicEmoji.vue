<template>
  <ul class="casic-emoji">
    <li v-for="item in talkStore.casicEmojiList" :key="item">
      <img :alt="item" :src="facesMap[item]" :title="item" @click="onSelect(item)" />
    </li>
  </ul>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useTalkStore } from "@/stores/modules/talk";

const talkStore = useTalkStore();

const facesMap = computed(() => talkStore.casicEmoji);

const emit = defineEmits(["sendEmoji"]);
const onSelect = (title: any) => {
  // 触发发送表情事件
  emit("sendEmoji", `casic[${title}]`);
};
</script>

<style lang="less" scoped>
.casic-emoji {
  // 覆盖ul默认样式
  list-style: none;
  display: block;
  padding: 0;
  margin: 0;
  height: 400px;
  li {
    width: 88px;
    height: 88px;
    display: inline-block;
    margin: 16px 12px;
    img {
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
  }
}
</style>
