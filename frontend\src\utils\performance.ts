/**
 * 性能监控工具
 */
export class PerformanceMonitor {
  private static timers: Map<string, number> = new Map();

  /**
   * 开始计时
   */
  static start(label: string): void {
    this.timers.set(label, performance.now());
  }

  /**
   * 结束计时并输出结果
   */
  static end(label: string): number {
    const startTime = this.timers.get(label);
    if (!startTime) {
      return 0;
    }

    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.timers.delete(label);
    
    return duration;
  }

  /**
   * 测量异步函数执行时间
   */
  static async measure<T>(label: string, fn: () => Promise<T>): Promise<T> {
    this.start(label);
    try {
      const result = await fn();
      this.end(label);
      return result;
    } catch (error) {
      this.end(label);
      throw error;
    }
  }

  /**
   * 测量同步函数执行时间
   */
  static measureSync<T>(label: string, fn: () => T): T {
    this.start(label);
    try {
      const result = fn();
      this.end(label);
      return result;
    } catch (error) {
      this.end(label);
      throw error;
    }
  }
} 