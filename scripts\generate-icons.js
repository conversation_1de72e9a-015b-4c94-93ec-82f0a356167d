/**
 * 图标生成脚本
 * 从 SVG 源文件生成所有平台和场景需要的图标
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const config = {
  // 源文件
  sourceIcon: 'frontend/src/assets/images/logo.svg',
  
  // 输出目录
  buildIconsDir: 'build/icons',
  publicImagesDir: 'public/images',
  
  // 需要生成的图标尺寸和格式
  icons: [
    // Windows ICO 文件 (多尺寸)
    { name: 'icon.ico', sizes: [16, 32, 48, 256], format: 'ico' },
    { name: 'favicon.ico', sizes: [16, 32], format: 'ico' },
    
    // macOS ICNS 文件
    { name: 'icon.icns', sizes: [16, 32, 64, 128, 256, 512, 1024], format: 'icns' },
    
    // PNG 文件 (各种尺寸)
    { name: 'icon.png', size: 512, format: 'png' },
    { name: '16x16.png', size: 16, format: 'png' },
    { name: '32x32.png', size: 32, format: 'png' },
    { name: '64x64.png', size: 64, format: 'png' },
    { name: '128x128.png', size: 128, format: 'png' },
    { name: '256x256.png', size: 256, format: 'png' },
    { name: '512x512.png', size: 512, format: 'png' },
    
    // 应用程序特定图标
    { name: 'logo-16.png', size: 16, format: 'png', target: 'public/images/tray.png' },
    { name: 'logo-32.png', size: 32, format: 'png', target: 'public/images/logo-32.png' },
    { name: 'logo-64.png', size: 64, format: 'png' },
    { name: 'logo-256.png', size: 256, format: 'png' },
    { name: 'logo-512.png', size: 512, format: 'png' },
    
    // 托盘图标 (深色主题支持)
    { name: 'tray.png', size: 16, format: 'png', target: 'public/images/tray.png' },
    { name: '<EMAIL>', size: 32, format: 'png', target: 'public/images/<EMAIL>' },
    { name: 'tray_empty.png', size: 16, format: 'png', target: 'public/images/tray_empty.png', transparent: true },
    { name: '<EMAIL>', size: 32, format: 'png', target: 'public/images/<EMAIL>', transparent: true },

    // 深色主题托盘图标
    { name: 'tray_dark.png', size: 16, format: 'png', target: 'public/images/tray_dark.png', darkTheme: true },
    { name: '<EMAIL>', size: 32, format: 'png', target: 'public/images/<EMAIL>', darkTheme: true },
    { name: 'tray_dark_empty.png', size: 16, format: 'png', target: 'public/images/tray_dark_empty.png', transparent: true, darkTheme: true },
    { name: '<EMAIL>', size: 32, format: 'png', target: 'public/images/<EMAIL>', transparent: true, darkTheme: true },
  ]
};

/**
 * 检查依赖工具
 */
function checkDependencies() {
  const tools = ['magick']; // ImageMagick
  
  for (const tool of tools) {
    try {
      execSync(`${tool} -version`, { stdio: 'ignore' });
      console.log(`✓ ${tool} 已安装`);
    } catch (error) {
      console.error(`✗ ${tool} 未安装或不在 PATH 中`);
      console.error(`请安装 ImageMagick: https://imagemagick.org/script/download.php`);
      process.exit(1);
    }
  }
}

/**
 * 确保目录存在
 */
function ensureDirectories() {
  const dirs = [config.buildIconsDir, config.publicImagesDir, 'public/images/notice'];
  
  for (const dir of dirs) {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`创建目录: ${dir}`);
    }
  }
}

/**
 * 生成 PNG 图标
 */
function generatePNG(inputFile, outputFile, size, transparent = false, darkTheme = false) {
  try {
    let command = `magick "${inputFile}" -background none -resize ${size}x${size}`;

    if (darkTheme) {
      // 深色主题：反转颜色，使图标在深色背景下可见
      command += ` -negate`;
    }

    if (transparent) {
      // 创建透明版本 (用于托盘闪烁效果)
      command += ` -alpha set -channel A -evaluate set 30%`;
    } else {
      // 确保保持透明背景
      command += ` -alpha set`;
    }

    // 强制使用支持透明度的 PNG 格式
    command += ` -type TrueColorAlpha -define png:color-type=6`;

    command += ` "${outputFile}"`;

    execSync(command, { stdio: 'inherit' });
    console.log(`生成 PNG: ${outputFile} (${size}x${size})${darkTheme ? ' [深色主题]' : ''}${transparent ? ' [透明]' : ''}`);
  } catch (error) {
    console.error(`生成 PNG 失败: ${outputFile}`, error.message);
  }
}

/**
 * 生成 ICO 文件
 */
function generateICO(inputFile, outputFile, sizes) {
  try {
    // 先生成各个尺寸的 PNG 文件
    const tempFiles = [];
    for (const size of sizes) {
      const tempFile = `temp_${size}.png`;
      generatePNG(inputFile, tempFile, size);
      tempFiles.push(tempFile);
    }
    
    // 合并为 ICO 文件
    const command = `magick ${tempFiles.join(' ')} "${outputFile}"`;
    execSync(command, { stdio: 'inherit' });
    
    // 清理临时文件
    tempFiles.forEach(file => {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
      }
    });
    
    console.log(`生成 ICO: ${outputFile} (${sizes.join(', ')})`);
  } catch (error) {
    console.error(`生成 ICO 失败: ${outputFile}`, error.message);
  }
}

/**
 * 生成 ICNS 文件 (macOS)
 */
function generateICNS(inputFile, outputFile, sizes) {
  try {
    // 创建临时目录
    const tempDir = 'temp_iconset';
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true });
    }
    fs.mkdirSync(tempDir);
    
    // 生成各个尺寸的 PNG 文件
    const iconSizes = [
      { size: 16, name: 'icon_16x16.png' },
      { size: 32, name: '<EMAIL>' },
      { size: 32, name: 'icon_32x32.png' },
      { size: 64, name: '<EMAIL>' },
      { size: 128, name: 'icon_128x128.png' },
      { size: 256, name: '<EMAIL>' },
      { size: 256, name: 'icon_256x256.png' },
      { size: 512, name: '<EMAIL>' },
      { size: 512, name: 'icon_512x512.png' },
      { size: 1024, name: '<EMAIL>' }
    ];
    
    for (const icon of iconSizes) {
      const tempFile = path.join(tempDir, icon.name);
      generatePNG(inputFile, tempFile, icon.size);
    }
    
    // 使用 iconutil 生成 ICNS (仅在 macOS 上可用)
    if (process.platform === 'darwin') {
      execSync(`iconutil -c icns "${tempDir}" -o "${outputFile}"`, { stdio: 'inherit' });
    } else {
      // 在非 macOS 系统上，使用 ImageMagick 生成 ICNS
      const pngFiles = iconSizes.map(icon => path.join(tempDir, icon.name));
      const command = `magick ${pngFiles.join(' ')} "${outputFile}"`;
      execSync(command, { stdio: 'inherit' });
    }
    
    // 清理临时目录
    fs.rmSync(tempDir, { recursive: true });
    
    console.log(`生成 ICNS: ${outputFile}`);
  } catch (error) {
    console.error(`生成 ICNS 失败: ${outputFile}`, error.message);
    
    // 如果 ICNS 生成失败，生成一个 PNG 作为备用
    generatePNG(inputFile, outputFile.replace('.icns', '.png'), 512);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🎨 开始生成应用程序图标...\n');
  
  // 检查源文件
  if (!fs.existsSync(config.sourceIcon)) {
    console.error(`源图标文件不存在: ${config.sourceIcon}`);
    process.exit(1);
  }
  
  // 检查依赖
  checkDependencies();
  
  // 确保目录存在
  ensureDirectories();
  
  console.log('\n📁 生成图标文件...\n');
  
  // 生成所有图标
  for (const icon of config.icons) {
    const outputPath = icon.target || path.join(config.buildIconsDir, icon.name);
    
    if (icon.format === 'ico') {
      generateICO(config.sourceIcon, outputPath, icon.sizes);
    } else if (icon.format === 'icns') {
      generateICNS(config.sourceIcon, outputPath, icon.sizes);
    } else if (icon.format === 'png') {
      generatePNG(config.sourceIcon, outputPath, icon.size, icon.transparent, icon.darkTheme);
    }
  }
  
  console.log('\n✅ 图标生成完成！');
  console.log('\n生成的文件:');
  console.log(`- build/icons/ - 打包用图标`);
  console.log(`- public/images/ - 应用程序运行时图标`);
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main, config };
