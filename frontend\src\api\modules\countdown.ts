import { ipc } from '@/utils/ipcRenderer'

const SERVICE_NAME = 'database.countdown'

/**
 * @name 倒计时模块
 */

// 定义倒计时项的接口
export interface CountdownItem {
  id: string
  userId: string
  name: string
  date: string
  createTime: number
  updateTime: number
}

// 获取倒计时列表
export const getCountdownList = async (): Promise<{ list: CountdownItem[]; total: number }> => {
  // 请求一个足够大的页面以获取所有数据，因为UI上没有分页
  const params = { pageNo: 1, pageSize: 1000 }
  return ipc.invoke('controller.db.all', {
    service: SERVICE_NAME,
    method: 'getCountdownList',
    args: [JSON.stringify(params)]
  })
}

// 添加倒计时
export const addCountdown = (data: { name: string; date: string }): Promise<[string]> => {
  return ipc.invoke('controller.db.all', {
    service: SERVICE_NAME,
    method: 'addCountdown',
    args: [JSON.stringify(data)]
  })
}

// 删除倒计时
export const deleteCountdown = (id: string): Promise<number> => {
  return ipc.invoke('controller.db.all', {
    service: SERVICE_NAME,
    method: 'deleteCountdown',
    args: [id]
  })
} 