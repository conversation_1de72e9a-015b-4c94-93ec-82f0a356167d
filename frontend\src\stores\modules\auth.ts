import { defineStore } from "pinia";
import { AuthState } from "../interface";
import { getAllBreadcrumbList, getFlatMenuList, getShowMenuList } from "@/utils";
import { getAuthButtonListApi, getAuthMenuListApi } from "@/api/modules/login";
import { Login } from "@/api/interface/login";

export const useAuthStore = defineStore("lark-auth", {
  state: (): AuthState => ({
    // 按钮权限列表
    authButtonList: {} as Login.ResAuthButtons,
    // 菜单权限列表
    authMenuList: [],
    // 当前页面的routerName，用来做按钮权限筛选
    routeName: ""
  }),
  getters: {
    // 按钮权限列表
    authButtonListGet: state => state.authButtonList,
    // 菜单权限列表 ==> 这里的菜单没有经过任何处理
    authMenuListGet: state => state.authMenuList,
    // 菜单权限列表 ==> 左侧菜单栏渲染，需要剔除 isHide == true
    showMenuListGet: state => getShowMenuList(state.authMenuList),
    // 菜单权限列表 ==> 主要用来添加动态路由
    flatMenuListGet: state => getFlatMenuList(state.authMenuList),
    // 递归处理后的所有面包屑导航列表
    breadcrumbListGet: state => getAllBreadcrumbList(state.authMenuList)
  },
  actions: {
    // 获取按钮权限列表
    async getAuthButtonList() {
      const { data } = await getAuthButtonListApi();
      this.authButtonList = data;
    },
    // 获取菜单权限列表
    async getAuthMenuList() {
      const { data } = await getAuthMenuListApi();
      this.authMenuList = data;
    },
    // 设置路由名字
    async setRouteName(name: string) {
      this.routeName = name;
    }
  }
});
