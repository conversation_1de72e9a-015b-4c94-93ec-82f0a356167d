<template>
  <el-container class="desktop-icons-container">
    <!-- 如果图标为空，显示提示 -->
    <div v-if="currentIcons.length === 0" class="empty-tip">当前没有桌面图标，请点击配置进行添加。</div>

    <!-- 图标列表 -->
    <el-row :gutter="20">
      <el-col v-for="icon in currentIcons" :key="icon.id" :span="5" @click="openFile(icon.path)" class="desktop-icon">
        <el-tooltip :content="icon.name" placement="top">
          <el-image draggable="false" :src="icon.icon" class="icon-image" />
        </el-tooltip>
      </el-col>
    </el-row>

    <!-- 分页按钮 -->
    <div class="pagination" v-if="totalPages > 1">
      <el-button @click="prevPage" :disabled="currentPage === 1">上一页</el-button>
      <span>{{ currentPage }} / {{ totalPages }}</span>
      <el-button @click="nextPage" :disabled="currentPage === totalPages">下一页</el-button>
    </div>
    <!-- </el-main> -->
  </el-container>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from "vue";
import { ElButton, ElImage, ElTooltip, ElRow, ElCol } from "element-plus";
import { ipc } from "@/utils/ipcRenderer";
import type { ISoftware } from "@/interfaces";
import { ipcApiRoute } from "@/ipc/ipcApi";

import { throttle } from "@/utils/common";

// 接收 props
const props = defineProps({
  itemsPerRow: {
    type: Number,
    default: 3 // 默认每行显示 3 个图标
  }
});

// 软件列表
const softList = ref<ISoftware[]>([]);

// 分页逻辑
const perPage = computed(() => props.itemsPerRow * 2); // 每页显示的图标数量（每行 X 个，最多 2 行）
const currentPage = ref(1);
const totalPages = computed(() => Math.ceil(softList.value.length / perPage.value));
const currentIcons = computed(() => {
  const start = (currentPage.value - 1) * perPage.value;
  const end = start + perPage.value;
  return softList.value.slice(start, end);
});

onMounted(async () => {
  // 获取数据
  ipc.invoke(ipcApiRoute.getSoftwareList).then((data: ISoftware[]) => {

    if (data && Array.isArray(data) && data.length) {
      softList.value = data;
    }
  });
});

// 打开图标对应的路径
const openFile = throttle((path: string) => {
  // if (isEdit.value) return;
  ipc.send(ipcApiRoute.openTool, path);
}, 1000);

// 上一页
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

// 下一页
const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

// 监听 itemsPerRow 变化，重置当前页
watch(
  () => props.itemsPerRow,
  () => {
    currentPage.value = 1; // 重置为第一页
  }
);
</script>

<style scoped>
.desktop-icons-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: transparent;
  border-bottom: 1px solid #eee;
}

.header-text {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.header-icon {
  font-size: 20px;
  cursor: pointer;
  transition: color 0.2s;
}

.header-icon:hover {
  color: #409eff;
}

.main {
  flex: 1;
  padding: 20px;
  background-color: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.empty-tip {
  font-size: 14px;
  color: #888;
  text-align: center;
}

/* .icon-grid {
  display: grid;
  grid-template-columns: repeat(v-bind(props.itemsPerRow), 1fr);
  grid-gap: 20px;
  width: 100%;
} */

.desktop-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s;
}

.desktop-icon:hover {
  transform: scale(1.1);
}

.icon-image {
  width: 45px;
  height: 45px;
  border-radius: 8px; /* 圆角 */
  object-fit: contain;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.pagination button {
  padding: 5px 10px;
  font-size: 14px;
  cursor: pointer;
  background-color: #409eff;
  color: #fff;
  border: none;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.pagination button:hover {
  background-color: #66b1ff;
}

.pagination button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
</style>
