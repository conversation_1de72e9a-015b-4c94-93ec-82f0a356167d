<template>
  <el-dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">
      <el-form-item label="TITLE" prop="title">
        <el-input v-model="formData.title" placeholder="请输入TITLE" />
      </el-form-item>
      <el-form-item label="链接" prop="linkAddress">
        <el-input v-model="formData.linkAddress" placeholder="请输入链接" />
      </el-form-item>
      <el-form-item label="链接状态" prop="state">
        <el-select v-model="formData.state" placeholder="请选择">
          <el-option v-for="item in data.state" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="浏览器" prop="image">
        <el-select v-model="formData.image" placeholder="请选择浏览器">
          <el-option v-for="item in data.image" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="链接参数" prop="param">
        <el-input v-model="formData.param" placeholder="请输入链接参数" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts" name="MoreForm">
import { ref, reactive } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { LinkApi } from "@/api/modules/link";
import { ElMessage } from "element-plus";
import { getChatSingle } from "@/api/modules/contact";

const userStore = useUserStore();

const dialogVisible = ref(false); // 弹窗的是否展示
const dialogTitle = ref(""); // 弹窗的标题
const formLoading = ref(false); // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref(""); // 表单的类型：create - 新增；update - 修改
const formData: any = ref({
  id: undefined,
  title: undefined,
  linkAddress: undefined,
  state: undefined,
  image: undefined,
  personal: undefined,
  userId: undefined,
  param: undefined,
  description: undefined,
  orgCode: undefined,
  phone: undefined
});
const formRules = reactive({
  title: [{ required: true, message: "TITLE不能为空", trigger: "blur" }],
  linkAddress: [{ required: true, message: "链接不能为空", trigger: "blur" }],
  state: [{ required: true, message: "链接状态不能为空", trigger: "blur" }],
  image: [{ required: true, message: "链接标志不能为空", trigger: "blur" }]
});
const data = {
  state: [
    {
      value: "0",
      label: "未启用"
    },
    {
      value: "1",
      label: "已启用"
    }
  ],
  personal: [
    {
      value: "0",
      label: "系统"
    },
    {
      value: "1",
      label: "个人"
    }
  ],
  image: [
    {
      value: "chrome",
      label: "谷歌"
    },
    {
      value: "ie",
      label: "IE"
    },
    {
      value: "firefox",
      label: "火狐"
    },
    {
      value: "edge",
      label: "Edge"
    },
    {
      value: "liebao",
      label: "猎豹"
    }
  ]
};

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true;
  if (type === "create") {
    dialogTitle.value = "新增";
  } else {
    dialogTitle.value = "编辑";
  }
  formType.value = type;
  resetForm();
  // 修改时，设置数据
  if (id) {
    formLoading.value = true;
    try {
      const res = await LinkApi.getLink(id);
      formData.value = res.data;
    } finally {
      formLoading.value = false;
    }
  }
};
defineExpose({ open }); // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(["success"]); // 定义 success 事件，用于操作成功后的回调
const formRef = ref(); // 表单 Ref
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate();
  // 提交请求
  formLoading.value = true;
  try {
    const data = formData.value;
    if (formType.value === "create") {
      await LinkApi.addLink(data);
      ElMessage.success("新增成功");
    } else {
      await LinkApi.updateLink(data);
      ElMessage.success("修改成功");
    }
    dialogVisible.value = false;
    // 发送操作成功的事件
    emit("success");
  } finally {
    formLoading.value = false;
  }
};

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    title: undefined,
    linkAddress: undefined,
    state: undefined,
    image: undefined,
    personal: undefined,
    userId: undefined,
    param: undefined,
    description: undefined,
    orgCode: undefined,
    phone: undefined
  };
  formRef.value?.resetFields();
};

/** 个人标志改变事件 */
const handlePersonalChange = (value: any) => {
  if (value == 0) {
    formData.value.userId = undefined;
  } else {
    formData.value.userId = userStore.userId;
  }
};

/** 个人标志-系统-搜索用户 */
const options: any = ref([]);
const loading = ref(false);
const fetchOptions = async (query: any) => {
  if (!query) {
    options.value = [];
    return;
  }

  loading.value = true;

  try {
    const res: any = await getChatSingle({ name: query });
    if (res.code == 0) {
      const user = res.data.list;
      options.value = user.map((item: any) => {
        return {
          value: item.id,
          label: item.name
        };
      });
    }
  } catch (error: any) {
    ElMessage.error(error.message);
    options.value = [];
  } finally {
    loading.value = false;
  }
};
</script>
