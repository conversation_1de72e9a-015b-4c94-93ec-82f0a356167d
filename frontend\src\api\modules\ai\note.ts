import request from "@/api";
import { AiNoteService } from "@/api/config/servicePort";
import { AiNote, Note } from "@/api/interface/note";

// ==================== AI智能笔记接口 ====================

/**
 * 保存AI智能笔记（统一接口，后端根据ID判断新建还是更新）
 * @param params 保存参数
 * @returns Promise<number> 拦截器已优化，直接返回笔记ID
 */
export const saveAiNoteApi = (params: AiNote.AiNoteSaveRequest) => {
  return request.post<number>(AiNoteService.SAVE_NOTE, params);
};

/**
 * @deprecated 使用 saveAiNoteApi 替代
 * 创建AI智能笔记
 */
export const createAiNoteApi = (params: AiNote.AiNoteSaveRequest) => {
  return saveAiNoteApi(params);
};

/**
 * @deprecated 使用 saveAiNoteApi 替代
 * 更新AI智能笔记
 */
export const updateAiNoteApi = (params: AiNote.AiNoteSaveRequest) => {
  return saveAiNoteApi(params);
};

/**
 * 删除AI智能笔记
 * @param id 笔记ID
 * @returns Promise<AiNote.CommonResult<boolean>>
 */
export const deleteAiNoteApi = (id: number) => {
  return request.delete<AiNote.CommonResult<boolean>>(AiNoteService.DELETE_NOTE, { params: { id } });
};

/**
 * 获取AI智能笔记详情
 * @param id 笔记ID
 * @returns Promise<AiNote.CommonResult<AiNote.AiNoteItem>>
 */
export const getAiNoteApi = (id: number) => {
  return request.get<AiNote.CommonResult<AiNote.AiNoteItem>>(AiNoteService.GET_NOTE, { params: { id } });
};

/**
 * 获取AI智能笔记分页列表
 * @param params 分页参数
 * @returns Promise<AiNote.CommonResult<AiNote.PageResult<AiNote.AiNoteItem>>>
 */
export const getAiNotePageApi = (params: AiNote.AiNotePageRequest) => {
  return request.get<AiNote.CommonResult<AiNote.PageResult<AiNote.AiNoteItem>>>(AiNoteService.GET_NOTE_PAGE, { params });
};

/**
 * AI智能生成笔记
 * @param params 生成参数
 * @returns Promise<AiNote.CommonResult<AiNote.AiNoteItem>>
 */
export const generateAiNoteApi = (params: AiNote.AiNoteGenerateRequest) => {
  return request.post<AiNote.CommonResult<AiNote.AiNoteItem>>(AiNoteService.GENERATE_NOTE, params);
};

/**
 * AI智能摘要生成
 * @param content 要生成摘要的内容
 * @returns Promise<AiNote.AiNoteItem> 拦截器已优化，直接返回AI笔记数据
 */
export const generateSummaryApi = (content: string) => {
  return request.post<AiNote.AiNoteItem>(AiNoteService.GENERATE_SUMMARY, null, { params: { content } });
};

/**
 * AI内容优化建议
 * @param noteId 笔记ID
 * @returns Promise<AiNote.CommonResult<string>>
 */
export const generateOptimizationApi = (noteId: number) => {
  return request.post<AiNote.CommonResult<string>>(AiNoteService.OPTIMIZE_NOTE, null, { params: { noteId } });
};

/**
 * 批量删除AI智能笔记
 * @param ids 笔记ID数组
 * @returns Promise<AiNote.CommonResult<boolean>>
 */
export const batchDeleteAiNotesApi = (ids: number[]) => {
  return request.delete<AiNote.CommonResult<boolean>>(AiNoteService.BATCH_DELETE, { params: { ids } });
};

/**
 * 导出AI智能笔记
 * @param id 笔记ID
 * @param format 导出格式
 * @returns Promise<AiNote.CommonResult<string>>
 */
export const exportAiNoteApi = (id: number, format: string) => {
  return request.get<AiNote.CommonResult<string>>(AiNoteService.EXPORT_NOTE, { params: { id, format } });
};

/**
 * 获取用户笔记统计信息
 * @returns Promise<AiNote.CommonResult<AiNote.NoteStats>>
 */
export const getAiNoteStatsApi = () => {
  return request.get<AiNote.CommonResult<AiNote.NoteStats>>(AiNoteService.GET_STATS);
};

// ==================== 简化版笔记接口 (用于闪记功能) ====================

/**
 * 保存简单笔记 (闪记)
 * @param content 笔记内容
 * @param title 笔记标题 (可选)
 * @param id 笔记ID (可选，用于更新)
 * @returns Promise<AiNote.CommonResult<number>>
 */
export const saveQuickNoteApi = (content: string, title?: string, id?: number) => {
  const params: AiNote.AiNoteSaveRequest = {
    title: title || content.substring(0, 20) + (content.length > 20 ? "..." : ""),
    content,
    noteType: "quick", // 闪记类型
    status: 1, // 正常状态
    isPublic: false, // 默认私有
    originalContent: content
  };

  // 如果提供了ID，则添加到参数中
  if (id) {
    params.id = id;
  }

  return saveAiNoteApi(params);
};

/**
 * 获取简单笔记列表 (闪记)
 * @param params 查询参数
 * @returns Promise<AiNote.CommonResult<AiNote.PageResult<AiNote.AiNoteItem>>>
 */
export const getQuickNotesApi = (params?: { pageNo?: number; pageSize?: number }) => {
  const queryParams: AiNote.AiNotePageRequest = {
    pageNo: params?.pageNo || 1,
    pageSize: params?.pageSize || 20,
    noteType: "quick", // 只查询闪记类型
    status: 1 // 只查询正常状态
  };
  return getAiNotePageApi(queryParams);
};

// ==================== 兼容性接口 (保持原有接口) ====================

/**
 * 保存笔记
 * @param content 笔记内容
 * @returns Promise<Note.SaveNoteResponse>
 */
export const saveNoteApi = (content: string) => {
  return request.post<Note.SaveNoteResponse>(AiNoteService.SAVE_NOTE, { content });
};

/**
 * 获取笔记列表
 * @param params 查询参数（可选）
 * @returns Promise<Note.GetNotesResponse>
 */
export const getNotesApi = (params?: Note.GetNotesRequest) => {
  return request.get<Note.GetNotesResponse>(AiNoteService.GET_NOTE_PAGE, { params });
};

/**
 * 根据ID获取笔记详情
 * @param id 笔记ID
 * @returns Promise<Note.NoteItem>
 */
export const getNoteByIdApi = (id: number) => {
  return request.get<Note.NoteItem>(AiNoteService.GET_NOTE, { params: { id } });
};

/**
 * 删除笔记
 * @param id 笔记ID
 * @returns Promise<Note.DeleteNoteResponse>
 */
export const deleteNoteApi = (id: number) => {
  return request.delete<Note.DeleteNoteResponse>(AiNoteService.DELETE_NOTE, { params: { id } });
};

/**
 * 批量删除笔记
 * @param params 批量删除参数
 * @returns Promise<Note.DeleteNoteResponse>
 */
export const batchDeleteNotesApi = (params: Note.BatchDeleteRequest) => {
  return request.delete<Note.DeleteNoteResponse>(AiNoteService.BATCH_DELETE, { params: { ids: params.ids } });
};

/**
 * 更新笔记
 * @param params 更新参数
 * @returns Promise<Note.UpdateNoteResponse>
 */
export const updateNoteApi = (params: Note.UpdateNoteRequest) => {
  return request.put<Note.UpdateNoteResponse>(AiNoteService.SAVE_NOTE, params);
};

/**
 * 搜索笔记
 * @param keyword 搜索关键词
 * @param params 其他查询参数
 * @returns Promise<Note.GetNotesResponse>
 */
export const searchNotesApi = (keyword: string, params?: Omit<Note.GetNotesRequest, "keyword">) => {
  return request.get<Note.GetNotesResponse>(AiNoteService.GET_NOTE_PAGE, {
    params: { ...params, keyword }
  });
};
