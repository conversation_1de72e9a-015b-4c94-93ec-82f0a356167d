<template>
  <div class="link-preview-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="link-preview-loading">
      <div class="loading-skeleton">
        <div class="skeleton-image"></div>
        <div class="skeleton-content">
          <div class="skeleton-title"></div>
          <div class="skeleton-description"></div>
          <div class="skeleton-url"></div>
        </div>
      </div>
    </div>

    <!-- 预览卡片 -->
    <div v-else-if="previewData && !error" class="link-preview-card" @click="openLink">
      <div class="preview-image-container" v-if="previewData.image">
        <img 
          :src="previewData.image" 
          :alt="previewData.title"
          class="preview-image"
          @error="handleImageError"
        />
      </div>
      
      <div class="preview-content">
        <div class="preview-header">
          <h3 class="preview-title">{{ previewData.title || url }}</h3>
          <Icon icon="mdi:open-in-new" class="external-link-icon" />
        </div>
        
        <p v-if="previewData.description" class="preview-description">
          {{ previewData.description }}
        </p>
        
        <div class="preview-footer">
          <div class="preview-domain">
            <Icon icon="mdi:web" class="domain-icon" />
            <span>{{ getDomain(url) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态或简单链接 -->
    <div v-else class="simple-link">
      <a :href="url" target="_blank" rel="noopener noreferrer" class="link-text">
        <Icon icon="mdi:link" class="link-icon" />
        {{ url }}
        <Icon icon="mdi:open-in-new" class="external-icon" />
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Icon } from '@iconify/vue'

interface LinkPreviewData {
  title?: string
  description?: string
  image?: string
  url?: string
  siteName?: string
}

interface Props {
  url: string
}

const props = defineProps<Props>()

const loading = ref(false)
const error = ref(false)
const previewData = ref<LinkPreviewData | null>(null)

// 缓存机制
const previewCache = new Map<string, LinkPreviewData>()

// 获取链接预览数据
const fetchLinkPreview = async (url: string): Promise<LinkPreviewData | null> => {
  // 检查缓存
  if (previewCache.has(url)) {
    return previewCache.get(url) || null
  }

  try {
    // 首先尝试生成基本预览信息（演示用）
    const urlObj = new URL(url)
    const domain = urlObj.hostname

    // 为常见网站生成模拟预览数据
    let mockPreview: LinkPreviewData | null = null

    if (domain.includes('github.com')) {
      mockPreview = {
        title: 'GitHub - 代码托管平台',
        description: '全球最大的代码托管平台，为开发者提供Git仓库托管服务。',
        image: 'https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png',
        url: url,
        siteName: 'GitHub'
      }
    } else if (domain.includes('baidu.com')) {
      mockPreview = {
        title: '百度一下，你就知道',
        description: '全球最大的中文搜索引擎、致力于让网民更便捷地获取信息，找到所求。',
        url: url,
        siteName: '百度'
      }
    } else if (domain.includes('google.com')) {
      mockPreview = {
        title: 'Google',
        description: '全球最大的搜索引擎，提供网页、图片、视频等搜索服务。',
        url: url,
        siteName: 'Google'
      }
    } else if (domain.includes('zhihu.com')) {
      mockPreview = {
        title: '知乎 - 有问题，就会有答案',
        description: '中文互联网高质量的问答社区和创作者聚集的原创内容平台。',
        url: url,
        siteName: '知乎'
      }
    } else if (domain.includes('youtube.com')) {
      mockPreview = {
        title: 'YouTube',
        description: '全球最大的视频分享平台，观看和分享原创视频内容。',
        url: url,
        siteName: 'YouTube'
      }
    }

    if (mockPreview) {
      previewCache.set(url, mockPreview)
      return mockPreview
    }

    // 如果不是常见网站，尝试使用API（可能会失败）
    const apiUrl = `https://jsonlink.io/api/extract?url=${encodeURIComponent(url)}`

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      }
    })

    if (!response.ok) {
      throw new Error('API请求失败')
    }

    const data = await response.json()

    const previewData: LinkPreviewData = {
      title: data.title || data.domain,
      description: data.description,
      image: data.images?.[0] || data.favicon,
      url: data.url || url,
      siteName: data.domain
    }

    // 缓存结果
    previewCache.set(url, previewData)

    return previewData
  } catch (err) {
    console.warn('链接预览获取失败，使用备用方案:', err)

    // 备用方案：生成基本预览信息
    try {
      const urlObj = new URL(url)
      const basicPreview: LinkPreviewData = {
        title: urlObj.hostname,
        description: `来自 ${urlObj.hostname} 的链接`,
        url: url,
        siteName: urlObj.hostname
      }

      // 缓存基本预览
      previewCache.set(url, basicPreview)
      return basicPreview
    } catch {
      return null
    }
  }
}

// 获取域名
const getDomain = (url: string): string => {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    return url
  }
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 打开链接
const openLink = () => {
  window.open(props.url, '_blank', 'noopener,noreferrer')
}

// 初始化
onMounted(async () => {
  // 验证URL格式
  try {
    new URL(props.url)
  } catch {
    error.value = true
    return
  }

  loading.value = true

  try {
    // 添加延迟以避免过于频繁的API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    const data = await fetchLinkPreview(props.url)
    if (data) {
      previewData.value = data
    } else {
      error.value = true
    }
  } catch (err) {
    console.error('获取链接预览失败:', err)
    error.value = true
  } finally {
    loading.value = false
  }
})
</script>

<style scoped lang="scss">
.link-preview-container {
  margin: 8px 0;
  max-width: 100%;
}

// 加载状态
.link-preview-loading {
  .loading-skeleton {
    display: flex;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    
    .skeleton-image {
      width: 80px;
      height: 80px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      border-radius: 6px;
      flex-shrink: 0;
    }
    
    .skeleton-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      .skeleton-title {
        height: 16px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
        width: 80%;
      }
      
      .skeleton-description {
        height: 12px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
        width: 100%;
      }
      
      .skeleton-url {
        height: 12px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
        width: 60%;
      }
    }
  }
}

@keyframes loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// 预览卡片
.link-preview-card {
  display: flex;
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 500px;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
    border-color: var(--theme-color, #8957e5);
  }
  
  .preview-image-container {
    width: 120px;
    height: 120px;
    flex-shrink: 0;
    overflow: hidden;
    background: #f8f9fa;
    
    .preview-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.2s ease;
    }
  }
  
  .preview-content {
    flex: 1;
    padding: 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-width: 0;
    
    .preview-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      gap: 8px;
      margin-bottom: 8px;
      
      .preview-title {
        font-size: 14px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        flex: 1;
      }
      
      .external-link-icon {
        color: #6c757d;
        font-size: 14px;
        flex-shrink: 0;
        margin-top: 2px;
      }
    }
    
    .preview-description {
      font-size: 12px;
      color: #6c757d;
      line-height: 1.4;
      margin: 0 0 8px 0;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .preview-footer {
      margin-top: auto;
      
      .preview-domain {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 11px;
        color: #9ca3af;
        
        .domain-icon {
          font-size: 12px;
        }
        
        span {
          font-weight: 500;
        }
      }
    }
  }
}

// 简单链接样式
.simple-link {
  .link-text {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    color: var(--theme-color, #8957e5);
    text-decoration: none;
    font-weight: 500;
    padding: 6px 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    word-break: break-all;
    
    &:hover {
      background: rgba(137, 87, 229, 0.1);
      text-decoration: underline;
    }
    
    .link-icon {
      font-size: 14px;
      flex-shrink: 0;
    }
    
    .external-icon {
      font-size: 12px;
      flex-shrink: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .link-preview-card {
    flex-direction: column;
    max-width: 100%;
    
    .preview-image-container {
      width: 100%;
      height: 160px;
    }
    
    .preview-content {
      padding: 16px;
    }
  }
  
  .loading-skeleton {
    flex-direction: column !important;
    
    .skeleton-image {
      width: 100% !important;
      height: 120px !important;
    }
  }
}
</style>
