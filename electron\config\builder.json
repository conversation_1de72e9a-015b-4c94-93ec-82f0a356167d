{"productName": "易知云雀", "appId": "com.lark.desktop", "copyright": "© 2025 云雀团队 Technology Co., Ltd.", "directories": {"output": "out"}, "asar": true, "files": ["**/*", "!frontend/", "!run/", "!logs/", "!go/", "!python/", "!data/"], "extraResources": {"from": "build/extraResources/", "to": "extraResources"}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "installerIcon": "build/icons/icon.ico", "uninstallerIcon": "build/icons/icon.ico", "installerHeaderIcon": "build/icons/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "云雀客户端"}, "publish": [{"provider": "generic", "url": "http://10.12.253.201:8066/desktop-update/"}], "mac": {"icon": "build/icons/icon.icns", "artifactName": "${productName}-${os}-${version}-${arch}.${ext}", "darkModeSupport": true, "hardenedRuntime": false}, "win": {"icon": "build/icons/icon.ico", "artifactName": "${productName}-${os}-${version}-${arch}.${ext}", "target": [{"target": "nsis"}]}, "linux": {"icon": "build/icons/icon.png", "artifactName": "${productName}-${os}-${version}-${arch}.${ext}", "target": ["deb"], "category": "Utility", "executableName": "lark-desktop", "desktop": {"Name": "易知云雀", "Icon": "lark-desktop", "Type": "Application", "Categories": "Utility;", "StartupWMClass": "易知云雀", "Terminal": "false", "MimeType": "x-scheme-handler/lark"}}, "deb": {"afterInstall": "./build/script/postinst"}}