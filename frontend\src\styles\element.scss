:root {
  //   --el-color-primary-rgb: 64, 158, 255;
  //   --el-color-success-rgb: 103, 194, 58;
  //   --el-color-warning-rgb: 230, 162, 60;
  //   --el-color-danger-rgb: 245, 108, 108;
  //   --el-color-error-rgb: 245, 108, 108;
  //   --el-color-info-rgb: 144, 147, 153;
  //   --el-font-size-extra-large: 20px;
  //   --el-font-size-large: 18px;
  //   --el-font-size-medium: 16px;
  //   --el-font-size-base: 14px;
  //   --el-font-size-small: 13px;
  //   --el-font-size-extra-small: 12px;
  //   --el-font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial,
  //     sans-serif;
  --el-font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, <PERSON><PERSON><PERSON> UI, Roboto, Helvetica Neue, Arial, Noto Sans,
    sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  //   --el-font-weight-primary: 500;
  //   --el-font-line-height-primary: 24px;
  //   --el-index-normal: 1;
  //   --el-index-top: 1000;
  //   --el-index-popper: 2000;
  //   --el-border-radius-base: 4px;
  //   --el-border-radius-small: 2px;
  //   --el-border-radius-round: 20px;
  //   --el-border-radius-circle: 100%;
  //   --el-transition-duration: 0.3s;
  //   --el-transition-duration-fast: 0.2s;
  //   --el-transition-function-ease-in-out-bezier: cubic-bezier(0.645, 0.045, 0.355, 1);
  //   --el-transition-function-fast-bezier: cubic-bezier(0.23, 1, 0.32, 1);
  //   --el-transition-all: all var(--el-transition-duration) var(--el-transition-function-ease-in-out-bezier);
  //   --el-transition-fade: opacity var(--el-transition-duration) var(--el-transition-function-fast-bezier);
  //   --el-transition-md-fade: transform var(--el-transition-duration) var(--el-transition-function-fast-bezier),
  //     opacity var(--el-transition-duration) var(--el-transition-function-fast-bezier);
  //   --el-transition-fade-linear: opacity var(--el-transition-duration-fast) linear;
  //   --el-transition-border: border-color var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);
  //   --el-transition-box-shadow: box-shadow var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);
  //   --el-transition-color: color var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);
  //   --el-component-size-large: 40px;
  //   --el-component-size: 32px;
  //   --el-component-size-small: 24px;
  //   color-scheme: light;
  //   --el-color-white: #ffffff;
  //   --el-color-black: #000000;
  --el-color-primary: rgb(29, 78, 216);
  --el-color-primary-light-3:  rgb(30, 64, 175);
  //   --el-color-primary-light-5: #a0cfff;
  //   --el-color-primary-light-7: #c6e2ff;
  //   --el-color-primary-light-8: #d9ecff;
  //   --el-color-primary-light-9: #ecf5ff;
  //   --el-color-primary-dark-2: #337ecc;
  //   --el-color-success: #67c23a;
  //   --el-color-success-light-3: #95d475;
  //   --el-color-success-light-5: #b3e19d;
  //   --el-color-success-light-7: #d1edc4;
  //   --el-color-success-light-8: #e1f3d8;
  //   --el-color-success-light-9: #f0f9eb;
  //   --el-color-success-dark-2: #529b2e;
  //   --el-color-warning: #e6a23c;
  //   --el-color-warning-light-3: #eebe77;
  //   --el-color-warning-light-5: #f3d19e;
  //   --el-color-warning-light-7: #f8e3c5;
  //   --el-color-warning-light-8: #faecd8;
  //   --el-color-warning-light-9: #fdf6ec;
  //   --el-color-warning-dark-2: #b88230;
  //   --el-color-danger: #f56c6c;
  //   --el-color-danger-light-3: #f89898;
  //   --el-color-danger-light-5: #fab6b6;
  //   --el-color-danger-light-7: #fcd3d3;
  //   --el-color-danger-light-8: #fde2e2;
  //   --el-color-danger-light-9: #fef0f0;
  //   --el-color-danger-dark-2: #c45656;
  //   --el-color-error: #f56c6c;
  //   --el-color-error-light-3: #f89898;
  //   --el-color-error-light-5: #fab6b6;
  //   --el-color-error-light-7: #fcd3d3;
  //   --el-color-error-light-8: #fde2e2;
  //   --el-color-error-light-9: #fef0f0;
  //   --el-color-error-dark-2: #c45656;
  //   --el-color-info: #909399;
  //   --el-color-info-light-3: #b1b3b8;
  //   --el-color-info-light-5: #c8c9cc;
  //   --el-color-info-light-7: #dedfe0;
  //   --el-color-info-light-8: #e9e9eb;
  //   --el-color-info-light-9: #f4f4f5;
  //   --el-color-info-dark-2: #73767a;
  --el-bg-color: #ffffff;
  //   --el-bg-color-page: #f2f3f5;
  //   --el-bg-color-overlay: #ffffff;
  //   --el-text-color-primary: #303133;
  --el-text-color-regular: rgb(0, 0, 0);
  --el-text-color-secondary: rgb(75, 85, 99);
  --el-text-color-placeholder: rgb(75, 85, 99);
  //   --el-text-color-disabled: #c0c4cc;
  --el-border-color: rgb(229, 231, 235);
  --el-border-color-light: rgb(229, 231, 235);
  //   --el-border-color-lighter: #ebeef5;
  //   --el-border-color-extra-light: #f2f6fc;
  //   --el-border-color-dark: #d4d7de;
  //   --el-border-color-darker: #cdd0d6;
  //   --el-fill-color: #f0f2f5;
  --el-fill-color-light: rgb(229, 231, 235);
  //   --el-fill-color-lighter: #fafafa;
  //   --el-fill-color-extra-light: #fafcff;
  //   --el-fill-color-dark: #ebedf0;
  //   --el-fill-color-darker: #e6e8eb;
  //   --el-fill-color-blank: #ffffff;
  //   --el-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
  //   --el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.12);
  //   --el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.12);
  //   --el-box-shadow-dark: 0px 16px 48px 16px rgba(0, 0, 0, 0.08), 0px 12px 32px rgba(0, 0, 0, 0.12),
  //     0px 8px 16px -8px rgba(0, 0, 0, 0.16);
  //   --el-disabled-bg-color: var(--el-fill-color-light);
  //   --el-disabled-text-color: var(--el-text-color-placeholder);
  //   --el-disabled-border-color: var(--el-border-color-light);
  //   --el-overlay-color: rgba(0, 0, 0, 0.8);
  //   --el-overlay-color-light: rgba(0, 0, 0, 0.7);
  //   --el-overlay-color-lighter: rgba(0, 0, 0, 0.5);
  //   --el-mask-color: rgba(255, 255, 255, 0.9);
  //   --el-mask-color-extra-light: rgba(255, 255, 255, 0.3);
  //   --el-border-width: 1px;
  //   --el-border-style: solid;
  //   --el-border-color-hover: var(--el-text-color-disabled);
  //   --el-border: var(--el-border-width) var(--el-border-style) var(--el-border-color);
  //   --el-svg-monochrome-grey: var(--el-border-color);
  --el-loading-spinner-size: 80px;
}

// /* 设置 notification、message 层级在 loading 之上 */
// .el-message,
// .el-notification {
//   z-index: 2058 !important;
// }

// /* el-alert */
// .el-alert {
//   border: 1px solid;
// }

// /* 当前页面最大化 css */
// .main-maximize {
//   .aside-split,
//   .el-aside,
//   .el-header,
//   .el-footer,
//   .tabs-box {
//     display: none !important;
//   }
// }

// /* mask image */
// .mask-image {
//   padding-right: 50px;
//   mask-image: linear-gradient(90deg, #000000 0%, #000000 calc(100% - 50px), transparent);
// }

// /* custom card */
// .card {
//   box-sizing: border-box;
//   padding: 20px;
//   overflow-x: hidden;
//   background-color: var(--el-bg-color);
//   border: 1px solid var(--el-border-color-light);
//   border-radius: 6px;
//   box-shadow: 0 0 12px rgb(0 0 0 / 5%);
// }

// /* ProTable 不需要 card 样式（在组件内使用 ProTable 会使用到） */
// .no-card {
//   .card {
//     padding: 0;
//     background-color: transparent;
//     border: none;
//     border-radius: 0;
//     box-shadow: none;
//   }
//   .table-search {
//     padding: 18px 0 0 !important;
//     margin-bottom: 0 !important;
//   }
// }

// /* content-box (常用内容盒子) */
// .content-box {
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   height: 100%;
//   .text {
//     margin: 20px 0 30px;
//     font-size: 23px;
//     font-weight: bold;
//     color: var(--el-text-color-regular);
//   }
//   .el-descriptions {
//     width: 100%;
//     padding: 40px 0 0;
//     .el-descriptions__title {
//       font-size: 18px;
//     }
//     .el-descriptions__label {
//       width: 200px;
//     }
//   }
// }

// /* main-box (树形表格 treeFilter 页面会使用，左右布局 flex) */
// .main-box {
//   display: flex;
//   width: 100%;
//   height: 100%;
//   .table-box {
//     // 这里减去的 230px 是 treeFilter 组件宽度
//     width: calc(100% - 230px);
//   }
// }

// /* proTable */
// .table-box,
// .table-main {
//   display: flex;
//   flex: 1;
//   flex-direction: column;
//   width: 100%;
//   height: 100%;

//   // table-search 表格搜索样式
//   .table-search {
//     padding: 18px 18px 0;
//     margin-bottom: 10px;
//     .el-form {
//       .el-form-item__content > * {
//         width: 100%;
//       }

//       // 去除时间选择器上下 padding
//       .el-range-editor.el-input__wrapper {
//         padding: 0 10px;
//       }
//     }
//     .operation {
//       display: flex;
//       align-items: center;
//       justify-content: flex-end;
//       margin-bottom: 18px;
//     }
//   }

//   // 表格 header 样式
//   .table-header {
//     .header-button-lf {
//       float: left;
//     }
//     .header-button-ri {
//       float: right;
//     }
//     .el-button {
//       margin-bottom: 15px;
//     }
//   }

//   // el-table 表格样式
//   .el-table {
//     flex: 1;

//     // 修复 safari 浏览器表格错位 https://github.com/HalseySpicy/Geeker-Admin/issues/83
//     table {
//       width: 100%;
//     }
//     .el-table__header th {
//       height: 45px;
//       font-size: 15px;
//       font-weight: bold;
//       color: var(--el-text-color-primary);
//       background: var(--el-fill-color-light);
//     }
//     .el-table__row {
//       height: 45px;
//       font-size: 14px;
//       .move {
//         cursor: move;
//         .el-icon {
//           cursor: move;
//         }
//       }
//     }

//     // 设置 el-table 中 header 文字不换行，并省略
//     .el-table__header .el-table__cell > .cell {
//       // white-space: nowrap;
//       white-space: wrap;
//     }

//     // 解决表格数据为空时样式不居中问题(仅在element-plus中)
//     .el-table__empty-block {
//       position: absolute;
//       top: 50%;
//       left: 50%;
//       transform: translate(-50%, -50%);
//       .table-empty {
//         line-height: 30px;
//       }
//     }

//     // table 中 image 图片样式
//     .table-image {
//       width: 50px;
//       height: 50px;
//       border-radius: 50%;
//     }
//   }

//   // 表格 pagination 样式
//   .el-pagination {
//     display: flex;
//     justify-content: flex-end;
//     margin-top: 20px;
//   }
// }

// /* el-table 组件大小 */
// .el-table--small {
//   .el-table__header th {
//     height: 40px !important;
//     font-size: 14px !important;
//   }
//   .el-table__row {
//     height: 40px !important;
//     font-size: 13px !important;
//   }
// }
// .el-table--large {
//   .el-table__header th {
//     height: 50px !important;
//     font-size: 16px !important;
//   }
//   .el-table__row {
//     height: 50px !important;
//     font-size: 15px !important;
//   }
// }

// /* el-drawer */
// .el-drawer {
//   .el-drawer__header {
//     padding: 16px 20px;
//     margin-bottom: 0;
//     border-bottom: 1px solid var(--el-border-color-lighter);
//     span {
//       font-size: 17px;
//       line-height: 17px;
//       color: var(--el-text-color-primary) !important;
//     }
//   }
//   .el-drawer__footer {
//     border-top: 1px solid var(--el-border-color-lighter);
//   }

//   // select 样式
//   .el-select {
//     width: 100%;
//   }

//   // drawer-form 中存在两列 form-item 样式
//   .drawer-multiColumn-form {
//     display: flex;
//     flex-wrap: wrap;
//     .el-form-item {
//       width: 47%;
//       &:nth-child(2n-1) {
//         margin-right: 5%;
//       }
//     }
//   }
// }

// /* el-dialog */
// .el-dialog {
//   .el-dialog__header {
//     padding: 15px 20px;
//     margin: 0;
//     border-bottom: 1px solid var(--el-border-color-lighter);
//     .el-dialog__title {
//       font-size: 17px;
//     }
//   }
// }