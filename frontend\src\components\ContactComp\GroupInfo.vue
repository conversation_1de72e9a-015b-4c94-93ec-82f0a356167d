<template>
  <div class="min-h-full rounded-lg p-4">
    <div class="mb-6">
      <div class="flex justify-end mb-2"></div>
      <div class="mb-4 flex items-center justify-center">
        <div
          class="group-icon flex w-24 h-24 items-center justify-center rounded-full text-indigo-600"
          :class="{ offical: groupInfo.groupType == 1 }"
        >
          <DynamicAvatar
            :id="groupInfo.id"
            :relation-name="groupInfo.groupName"
            :type-avatar="groupInfo.groupType == 0 ? 1 : 2"
            :size="80"
          />
          <section></section>
        </div>
      </div>
      <h3 class="mb-2 text-center text-xl font-medium text-gray-900">{{ groupInfo.groupName }}</h3>
      <p class="mb-4 text-center text-sm text-gray-500">{{ groupInfo.memberNum }} 名成员</p>
      <h4 class="mb-2 text-base font-medium text-gray-900">群组介绍</h4>
      <p class="group-des mb-4 rounded-lg border border-gray-200 bg-gray-50/50 p-4 text-sm text-gray-600">
        由群主：{{ groupInfo.groupOwnerName }}创建，群名：{{ groupInfo.groupName }}，共{{ groupInfo.memberNum }}名成员
        <LevelBtn :message="groupInfo.secret"></LevelBtn>
      </p>
      <div class="flex justify-center gap-3">
        <button
          @click="openChat(groupInfo)"
          class="into-group rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 cursor-pointer"
        >
          进入群组
        </button>
      </div>
    </div>
    <!-- <div class="activity border-t border-gray-200 pt-4">
      <h4 class="mb-2 text-base font-medium text-gray-900">最近活动</h4>
      <div class="space-y-2">
        <div class="act-item rounded-lg border border-gray-200 p-3" v-for="i in 3">
          <div class="mb-2 flex items-center gap-2">
            <DynamicAvatar id="123" :type-avatar="0" :size="32" />
            <span class="act-user text-sm font-medium text-gray-900">用户 123</span>
            <span class="act-time text-xs text-gray-500">1 小时前</span>
          </div>
          <p class="text-sm text-gray-600">分享了一个关于官方群组 1的重要通知，请大家查看并及时回复。</p>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script setup lang="ts" name="GroupInfo">
import { ref, watch } from "vue";
import { useContactStore } from "@/stores/modules/friend";
import { useRecentStore } from "@/stores/modules/recent";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import DynamicAvatar from "../Avatar/DynamicAvatar.vue";
import LevelBtn from "@/components/LevelBtn/index.vue";

const contactStore = useContactStore();
const recentStore = useRecentStore();
const userStore = useUserStore();
const talkStore = useTalkStore();

const props = defineProps({
  groupId: {
    type: String,
    required: true
  }
});

const groupInfo: any = ref({});
watch(
  () => props.groupId,
  async newVal => {
    if (newVal) {
      if (!contactStore.groupInfo[newVal]) {
        await contactStore.getGroupInfo(newVal);
      }
      groupInfo.value = contactStore.groupInfo[newVal];
    }
  },
  { immediate: true }
);

const openChat = async (data: any) => {
  const params = {
    senderId: userStore.userId,
    contactId: data.id,
    chatType: data.groupType + 1,
    avatar: null,
    contactName: data.groupName,
    secret: data.secret
  };
  const res = await recentStore.addListRecents(params);
  if (res) {
    talkStore.setActiveChat(data.id);
    talkStore.ifChat = true;
    talkStore.ifContact = false;
  }
};
</script>

<style scoped lang="scss">
.group-icon {
  position: relative;
  section {
    width: 90px;
    height: 90px;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    z-index: 100;
  }
}
.dark {
  h3,
  h4,
  act-user {
    @apply text-white;
  }
  p,
  .act-time {
    @apply text-gray-400;
  }
  .group-icon {
    @apply bg-indigo-900/30 text-indigo-500;
  }
  .group-icon.offical {
    @apply bg-red-900/30 text-red-500;
  }
  .group-des {
    @apply border-gray-700 bg-gray-800/30;
  }
  .into-group {
    @apply bg-blue-500 hover:bg-blue-600;
  }
  .activity,
  .act-item {
    @apply border-gray-700;
  }
}
</style>
