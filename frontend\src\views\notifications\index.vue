<template>
  <div class="h-full p-4 rounded-t-xl">
    <!-- 标签页切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane name="notice">
        <template #label>
          <el-badge :value="noticeNum" :hidden="noticeNum == 0" :offset="[10, 0]">系统通知</el-badge>
        </template>
      </el-tab-pane>
      <el-tab-pane name="approve">
        <template #label>
          <el-badge :value="approveNum" :hidden="approveNum == 0" :offset="[10, 0]">我的审批</el-badge>
        </template>
      </el-tab-pane>
    </el-tabs>

    <!-- 动态组件部分 -->
    <component :is="currentComponent" v-if="currentComponent"></component>
  </div>
</template>

<script setup lang="ts" name="notifications">
import { ref, computed, onMounted } from "vue";
import { defineAsyncComponent } from "vue";
import { useRoute } from "vue-router";
import { useNoticeStore } from "@/stores/modules/notices";

const route = useRoute();
const noticeStore = useNoticeStore();

const activeTab = ref("notice");
const noticeNum = computed(() => noticeStore.noticeCount);
const approveNum = computed(() => noticeStore.approveCount);
const NoticeList = defineAsyncComponent(() => import("./components/NoticeList.vue"));
const ApproveList = defineAsyncComponent(() => import("./components/ApproveList.vue"));
const componentsMap: any = {
  notice: NoticeList,
  approve: ApproveList
};

const currentComponent = computed(() => {
  return componentsMap[activeTab.value];
});

const handleTabClick = (tab: any) => {
  activeTab.value = tab.props.name;
};

onMounted(() => {
  if (route.query.type == "0") {
    activeTab.value = "approve";
  } else {
    activeTab.value = "notice";
  }
});
</script>
