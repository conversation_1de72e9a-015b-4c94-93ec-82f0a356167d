/* 
使用说明：
数值转标签
SecretLevelConverter.fromNumber(40, 'obj') // 返回 "秘密"
SecretLevelConverter.fromNumber(40, 'user')      // 返回 "重要"

标签转数值
SecretLevelConverter.fromLabel('机密', 'obj') // 返回 60
SecretLevelConverter.fromLabel('核心', 'user')     // 返回 50

获取选项列表
SecretLevelConverter.getOptions('obj')
 返回：
[
  { value: 60, label: '非密' },
  { value: 68, label: '普通商密' },
  { value: 70, label: '秘密' },
  { value: 45, label: '核心商密' },
  { value: 60, label: '机密' }
]
*/
export class SecretLevelConverter {
  // 消息密级
  private static readonly OBJ_MAP = new Map<number, string>([
    [30, "非密"],
    [31, "内部"],
    [32, "普通商密"],
    [33, "秘密"],
    [55, "核心商密"],
    [60, "机密"]
  ]);
  // 用户密级
  private static readonly USER_MAP = new Map<number, string>([
    [30, "非密"],
    [40, "一般"],
    [50, "一般"],
    [60, "重要"],
    [70, "重要"],
    [80, "核心"],
    [90, "核心"]
  ]);
  // 密级样式
  private static readonly CSS_ClASS_MAP = new Map<number, string>([
    [30, "feimi"],
    [31, "neibu"],
    [32, "putongshangmi"],
    [33, "mimi"],
    [40, "mimi"],
    [50, "mimi"],
    [55, "hexinshangmi"],
    [60, "jimi"],
    [70, "jimi"],
    [80, "jimi"],
    [90, "jimi"]
  ]);

  // 反向映射表
  private static readonly REVERSE_OBJ = new Map(Array.from(this.OBJ_MAP).map(([k, v]) => [v, k]));

  private static readonly REVERSE_USER = new Map(Array.from(this.USER_MAP).map(([k, v]) => [v, k]));

  /**
   * 数字转中文标签
   * @param level 安全等级数值
   * @param type 分类类型：'obj'|'user'
   */
  static fromNumber(level: number, type: "obj" | "user"): string {
    const map = type === "obj" ? this.OBJ_MAP : this.USER_MAP;
    const label = map.get(level);
    if (!label) throw new Error(`无效的安全等级数值: ${level}`);
    return label;
  }

  /**
   * 中文标签转数字
   * @param label 中文标签
   * @param type 分类类型：'obj'|'user'
   */
  static fromLabel(label: string, type: "obj" | "user"): number {
    const map = type === "obj" ? this.REVERSE_OBJ : this.REVERSE_USER;
    const level = map.get(label);
    if (level === undefined) throw new Error(`无效的安全等级标签: ${label}`);
    return level;
  }

  /**
   * 获取指定类型的所有选项
   * @param type 分类类型：'obj'|'user'
   */
  static getOptions(type: "obj" | "user") {
    const map = type === "obj" ? this.OBJ_MAP : this.USER_MAP;
    return Array.from(map).map(([value, label]) => ({ value, label, className: this.CSS_ClASS_MAP.get(value) }));
  }

  /**
   * 获取指定类型的classname
   * @param level 安全等级数值
   * @returns classname
   */
  static getCssName(level: number) {
    const label = this.CSS_ClASS_MAP.get(level);
    if (!label) throw new Error(`无效的安全等级数值: ${level}`);
    return label;
  }

  /**
   * 获取指定类型等级信息
   * @param level 人员安全等级数值
   * @returns
   */
  static getLevelInfo(level: number, type: "obj" | "user" = "obj") {
    const map = type === "obj" ? this.OBJ_MAP : this.USER_MAP;
    return { label: map.get(level), className: this.CSS_ClASS_MAP.get(level) };
    // if (level < 40) {
    //   return { label: map.get(30), className: this.CSS_ClASS_MAP.get(30) };
    // } else if (level < 60) {
    //   return { label: map.get(40), className: this.CSS_ClASS_MAP.get(40) };
    // } else {
    //   return { label: map.get(60), className: this.CSS_ClASS_MAP.get(60) };
    // }
  }
}
