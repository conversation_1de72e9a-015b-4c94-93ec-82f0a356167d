<template>
  <div class="widget-container h-full">
    <!-- 悬浮标题和按钮容器 -->
    <div class="absolute top-0 left-4 -translate-y-1/2 flex items-center gap-2 z-10">
      <!-- 小组件标题 -->
      <div
        class="widget-title bg-white dark:bg-gray-800 px-3 py-1.5 rounded-full shadow-md border border-gray-200 dark:border-gray-700 flex items-center gap-2"
      >
        <Icon :icon="checklistBoldDuotone" class="text-lg text-green-500" />
        <span class="text-xs font-bold text-gray-700 dark:text-gray-200">事项清单</span>
        <span class="text-xs opacity-80 text-gray-500 dark:text-gray-400 ml-1">
          {{ completedCount }}/{{ todos.length }}
        </span>
      </div>
      <!-- 操作按钮 -->
      <button
        @click="openAddDialog"
        @mousedown.prevent
        class="w-7 h-7 rounded-full bg-green-500 text-white flex items-center justify-center hover:bg-green-600 transition-colors shadow-md"
        title="添加TODO"
      >
        <font-awesome-icon :icon="['fas', 'plus']" class="w-5 h-5" />
      </button>
      <button
        @click="maximizeWidget"
        class="w-7 h-7 rounded-full bg-green-500 text-white flex items-center justify-center hover:bg-green-600 transition-colors shadow-md"
        title="全屏查看"
      >
        <font-awesome-icon :icon="['fas', 'expand']" class="w-4 h-4" />
      </button>
    </div>

    <div class="p-3 pt-6 h-full flex flex-col">
      <!-- 待办列表 -->
      <div class="flex-1 overflow-y-auto overflow-x-visible space-y-1 relative z-40 p-1">
        <!-- 空状态显示：居中显示图标和文字 -->
        <div v-if="displayTodos.length === 0" class="flex flex-col items-center justify-center h-full text-gray-400 dark:text-gray-500">
          <Icon :icon="checklistBoldDuotone" class="text-4xl text-green-500 mb-2" />
          <span class="text-sm">暂无TODO</span>
        </div>

        <div
          v-else
          v-for="(todo, index) in displayTodos"
          :key="todo.id || index"
          class="relative bg-white dark:bg-gray-800 rounded-lg p-2.5 border border-gray-200 dark:border-gray-700 shadow flex items-center group"
        >
          <el-checkbox
            :model-value="todo.completed"
            @change="toggleTodoById(todo.id)"
            class="mr-4 scale-100 flex-shrink-0 relative z-[60] checkbox-elegant"
          />
          <div class="flex-1 min-w-0">
            <div
              :class="[
                'text-base leading-relaxed break-words',
                todo.completed ? 'line-through text-gray-500 dark:text-gray-500' : 'text-gray-800 dark:text-gray-200'
              ]"
            >
              {{ todo.content }}
            </div>
          </div>
          <Icon
            :icon="trashBinTrashLinear"
            width="16"
            height="16"
            class="cursor-pointer text-gray-400 dark:text-gray-500 ml-3 flex-shrink-0 relative z-[60] hover:text-red-500 dark:hover:text-red-400 transition-colors"
            @click="deleteTodoById(todo.id)"
          />
        </div>
      </div>
    </div>
  </div>

  <!-- 全屏对话框 -->
  <el-dialog
    v-model="isMaximized"
    width="800px"
    top="10vh"
    class="custom-dialog fullscreen-todo-dialog"
    :show-close="false"
    :close-on-click-modal="false"
    append-to-body
  >
    <!-- Header -->
    <template #header>
      <button class="close-btn" @click="closeMaximize">
        <Icon :icon="minimizeOutline" />
      </button>
      <div class="flex items-center gap-2">
        <Icon :icon="checklistBoldDuotone" class="icon text-green-500" />
        <span class="title">事项清单</span>
        <span class="text-sm opacity-80 text-gray-500 dark:text-gray-400 ml-2">
          {{ completedCount }}/{{ todos.length }}
        </span>
      </div>
    </template>

    <!-- Body -->
    <div class="p-2">
      <div class="flex items-center justify-between mb-4">
        <!-- 滑块切换器 -->
        <div class="category-slider-wrapper is-fullscreen">
          <div class="slider-tabs">
            <div class="slider-track">
              <div class="slider-indicator" :style="sliderStyle"></div>
            </div>
            <div 
              v-for="filter in filterOptions" 
              :key="filter"
              class="slider-tab"
              :class="{ active: activeFilter === filter }"
              @click="selectFilter(filter)"
            >
              {{ filter }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 待办列表 - 两列扁平风格 -->
      <div class="overflow-y-auto max-h-96">
        <!-- 空状态提示 -->
        <div v-if="filteredTodos.length === 0" class="flex items-center justify-center h-48">
          <div class="text-center text-gray-500">
            <div class="mb-4">
              <Icon :icon="clipboardCheck" class="text-6xl text-emerald-500 dark:text-emerald-400 opacity-70" />
            </div>
            <div class="text-lg">暂无TODO</div>
          </div>
        </div>
        
        <div v-else class="grid grid-cols-2 gap-4">
          <div v-for="(todo, index) in filteredTodos" :key="index" class="p-4 bg-yellow-100 dark:bg-yellow-900 rounded-lg border border-yellow-200 dark:border-yellow-800 flex flex-col justify-between">
            <!-- 内容区域 -->
            <div class="flex-1">
              <p :class="['text-base leading-relaxed break-words', todo.completed ? 'line-through text-yellow-600 dark:text-yellow-400' : 'text-yellow-900 dark:text-yellow-100']">
                {{ todo.content }}
              </p>
            </div>
            
            <!-- 底部区域：时间和按钮 -->
            <div class="flex items-center justify-between mt-3">
              <span class="text-sm text-yellow-700 dark:text-yellow-300">{{ todo.time }}</span>
              <div class="flex items-center space-x-2">
                <button @click="deleteTodoById(todo.id)" class="p-1 text-yellow-600 hover:text-red-500 dark:text-yellow-400 dark:hover:text-red-400 transition-colors">
                  <Icon :icon="trashBinTrashLinear" width="18" height="18" />
                </button>
                <button 
                  @click="toggleTodoById(todo.id)" 
                  :class="[
                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                    todo.completed 
                      ? 'text-emerald-600 bg-emerald-50 hover:bg-emerald-100 dark:text-emerald-400 dark:bg-emerald-900/20 dark:hover:bg-emerald-900/30' 
                      : 'text-gray-700 bg-transparent hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-800/50'
                  ]"
                >
                  <span>{{ todo.completed ? '已完成' : '完成' }}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
  <!-- 已移至全局组件 TodoAddDialog.vue -->
</template>

<script setup lang="ts">
import { ref, computed, shallowRef, watch, nextTick, onMounted, inject } from 'vue'
import { Plus, Check } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'

// 导入离线图标
import minimizeOutline from "@iconify-icons/solar/minimize-outline";
import trashBinTrashLinear from "@iconify-icons/solar/trash-bin-trash-linear";
import checklistBoldDuotone from "@iconify-icons/solar/checklist-bold-duotone";
import clipboardCheck from "@iconify-icons/material-symbols/checklist";

// 导入API和Store
import { difyChat, type DifyChatRequest } from '@/api/modules/dify'
import { sendAiStreamChatMessage, type AiStreamChatRequest } from '@/api/modules/ai/chat'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage } from 'element-plus'
import {
  getTodosList,
  addTodo as apiAddTodo,
  updateTodoStatus,
  deleteTodo as apiDeleteTodo,
  type TodoItem
} from "@/api/modules/todos";

interface Todo {
  id?: string;
  content: string;
  completed: boolean;
  time: string;
}

const newTodo = ref('')
const showAddDialog = ref(false) // 控制添加对话框
const inputRef = ref<HTMLTextAreaElement>()
const isGenerating = ref(false)

// 获取用户信息
const userStore = useUserStore();

const todos = shallowRef<Todo[]>([]);

// 滑块相关状态
const filterOptions = ["全部", "未完成", "已完成"];
const activeFilter = ref("全部");

const completedCount = computed(() => {
  return todos.value.filter(todo => todo.completed).length;
});

// 滑块样式计算
const currentIndex = computed(() => {
  return filterOptions.findIndex(f => f === activeFilter.value);
});

const sliderStyle = computed(() => {
  const tabCount = filterOptions.length;
  const tabWidth = 100 / tabCount;
  return {
    width: `${tabWidth}%`,
    transform: `translateX(${currentIndex.value * 100}%)`
  };
});

// 根据滑块筛选待办事项
const filteredTodos = computed(() => {
  if (activeFilter.value === "全部") {
    return todos.value;
  } else if (activeFilter.value === "未完成") {
    return todos.value.filter(todo => !todo.completed);
  } else {
    return todos.value.filter(todo => todo.completed);
  }
});

// 小窗口只显示未完成的待办事项，最多4个
const displayTodos = computed(() => {
  const uncompletedTodos = todos.value.filter(todo => !todo.completed);
  return uncompletedTodos.slice(0, 4);
});

// 选择筛选器
const selectFilter = (filter: string) => {
  activeFilter.value = filter;
};

// 加载待办事项列表
const loadTodos = async () => {
  try {
    const response = await getTodosList();
    const todoItems = response.list.map((item: TodoItem) => ({
      id: item.id,
      content: item.content,
      completed: item.completed,
      time: formatTime(item.createTime)
    }));
    todos.value = todoItems;
  } catch (error) {
    console.error("加载待办事项失败:", error);
    ElMessage.error("加载待办事项失败");
  }
};

// 格式化时间
const formatTime = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  const oneDay = 24 * 60 * 60 * 1000;

  if (diff < oneDay) {
    return "今天";
  } else if (diff < 2 * oneDay) {
    return "昨天";
  } else if (diff < 7 * oneDay) {
    return `${Math.floor(diff / oneDay)}天前`;
  } else {
    return new Date(timestamp).toLocaleDateString();
  }
};

// 打开添加对话框 - 通过注入的方法
const openAddDialog = inject('openTodoDialog', () => {
  console.warn('openTodoDialog not provided')
})

// 旧的内嵌添加模式已废弃，直接使用对话框
const toggleAddMode = () => {
  openAddDialog()
}

const addTodo = async () => {
  // 如果正在生成AI内容，提示等待
  if (isGenerating.value) {
    ElMessage.warning("AI正在生成内容，请等待完成后再添加");
    return;
  }

  if (newTodo.value.trim()) {
    try {
      await apiAddTodo({ content: newTodo.value.trim() });
      ElMessage.success("待办事项添加成功");

      // 重新加载列表
      await loadTodos()
      
      newTodo.value = ''
      // 添加成功后关闭对话框通过父组件处理
    } catch (error) {
      console.error("添加待办事项失败:", error);
      ElMessage.error("添加待办事项失败，请稍后重试");
    }
  }
};

// 处理回车键事件
const handleEnterKey = (event: KeyboardEvent) => {
  // 阻止默认的换行行为
  event.preventDefault();

  // 如果正在生成AI内容，提示等待
  if (isGenerating.value) {
    ElMessage.warning("AI正在生成内容，请等待完成后再添加");
    return;
  }

  // 添加待办事项
  addTodo();
};

const toggleTodo = async (index: number) => {
  const todo = todos.value[index];
  if (!todo.id) return;

  try {
    const newCompleted = !todo.completed;
    await updateTodoStatus({ id: todo.id, completed: newCompleted });

    // 本地更新状态
    const newTodos = [...todos.value];
    newTodos[index] = { ...newTodos[index], completed: newCompleted };
    todos.value = newTodos;
  } catch (error) {
    console.error("更新待办状态失败:", error);
    ElMessage.error("更新待办状态失败");
  }
};

const deleteTodo = async (index: number) => {
  const todo = todos.value[index];
  if (!todo.id) return;

  try {
    await apiDeleteTodo(todo.id);
    ElMessage.success("待办事项已删除");

    // 本地删除
    todos.value = todos.value.filter((_, i) => i !== index);
  } catch (error) {
    console.error("删除待办事项失败:", error);
    ElMessage.error("删除待办事项失败");
  }
};

// 通过ID删除待办事项（用于全屏模式）
const deleteTodoById = async (id: string | undefined) => {
  if (!id) return;

  try {
    await apiDeleteTodo(id);
    ElMessage.success("待办事项已删除");

    // 本地删除
    todos.value = todos.value.filter(todo => todo.id !== id);
  } catch (error) {
    console.error("删除待办事项失败:", error);
    ElMessage.error("删除待办事项失败");
  }
};

// 通过ID切换待办状态（用于全屏模式）
const toggleTodoById = async (id: string | undefined) => {
  if (!id) return;

  const todoIndex = todos.value.findIndex(todo => todo.id === id);
  if (todoIndex === -1) return;

  const todo = todos.value[todoIndex];

  try {
    const newCompleted = !todo.completed;
    await updateTodoStatus({ id: todo.id!, completed: newCompleted });

    // 本地更新状态
    const newTodos = [...todos.value];
    newTodos[todoIndex] = { ...newTodos[todoIndex], completed: newCompleted };
    todos.value = newTodos;
  } catch (error) {
    console.error("更新待办状态失败:", error);
    ElMessage.error("更新待办状态失败");
  }
};

const hideInputDelayed = () => {
  // 已废弃：原内嵌输入框的隐藏逻辑
};

const cancelInput = () => {
  // 已废弃：原内嵌输入框的取消逻辑
};

// 打字机效果函数
const typewriterEffect = async (text: string, speed: number = 50) => {
  return new Promise<void>(resolve => {
    let index = 0;
    const timer = setInterval(() => {
      if (index < text.length) {
        newTodo.value += text[index];
        index++;
      } else {
        clearInterval(timer);
        resolve();
      }
    }, speed);
  });
};

// AI生成待办事项（全屏模式旧方法）
const generateTodoWithAI = async () => {
  if (!newTodo.value.trim() || isGenerating.value) return;

  isGenerating.value = true;

  try {
    const params: DifyChatRequest = {
      inputs: {
        user_input: newTodo.value.trim(),
        context: "用户需要基于输入内容生成详细的待办事项列表"
      },
      response_mode: "blocking",
      user: userStore.userId || "anonymous",
      query: newTodo.value.trim()
    };

    const response = await difyChat(params);

    if (response && response.data && response.data.answer) {
      // 获取AI返回的文本内容
      const aiText = response.data.answer.trim();

      if (aiText) {
        // 清空输入框，准备打字机效果
        newTodo.value = "";

        // 打字机效果显示AI生成的内容
        await typewriterEffect(aiText);

        ElMessage.success("AI已优化您的待办内容");

        // 生成完成后，重新聚焦到输入框
        nextTick(() => {
          inputRef.value?.focus();
        });
      } else {
        ElMessage.info("AI处理完成");
      }
    } else {
      ElMessage.warning("AI处理失败，请重试");
    }
  } catch (error) {
    console.error("AI生成待办事项失败:", error);
    ElMessage.error("AI服务暂时不可用，请重试");
  } finally {
    isGenerating.value = false;
  }
};

// AI规划待办（对话框）
const aiPlanTodo = () => {
  if (!newTodo.value.trim() || isGenerating.value) return
  isGenerating.value = true
  let resultText = ''
  const request: AiStreamChatRequest = {
    query: newTodo.value.trim(),
    app_id: 'todo',
    inputs: {
      context: '根据用户输入规划待办事项'
    },
    shouldProcessNodeStarted: false
  }
  sendAiStreamChatMessage(request, {
    onData: (chunk) => {
      resultText += chunk.content
      newTodo.value = resultText
    },
    onError: (err) => {
      console.error('AI规划失败', err)
      ElMessage.error('AI规划失败，请稍后重试')
      isGenerating.value = false
    },
    onComplete: () => {
      isGenerating.value = false
      ElMessage.success('AI规划完成')
      nextTick(() => {
        inputRef.value?.focus()
      })
    }
  })
}

// 添加并关闭对话框
const addTodoAndClose = async () => {
  await addTodo()
  if (!isGenerating.value) {
    showAddDialog.value = false
  }
}

const isMaximized = ref(false)

const maximizeWidget = () => {
  isMaximized.value = true
}

const closeMaximize = () => {
  isMaximized.value = false
}

// 组件挂载时加载数据
onMounted(() => {
  loadTodos()
})

// 暴露方法给父组件
defineExpose({
  loadTodos
})
</script>

<style scoped lang="scss">
.widget-container {
  overflow: visible;
}

// 移除所有样式，使用父级样式

// 移除暗色模式样式

// 图标静态效果
.icon-shadow {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.todo-item {
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 30;
  // 移除所有hover效果
}


// 便签纸复选框样式
.checkbox-note {
  :deep(.el-checkbox__inner) {
    background-color: #fef3c7;
    border: 2px solid #d97706;
    border-radius: 3px;
    width: 16px;
    height: 16px;
    transition: all 0.2s ease;

    &:hover {
      border-color: #b45309;
      background-color: #fde68a;
    }
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: #059669;
    border-color: #059669;

    &::after {
      border-color: #ffffff;
      border-width: 2px;
      width: 4px;
      height: 7px;
      left: 5px;
      top: 1px;
    }
  }

  // 暗黑模式
  :deep(.dark .el-checkbox__inner) {
    background-color: #92400e;
    border-color: #f59e0b;

    &:hover {
      border-color: #fbbf24;
      background-color: #b45309;
    }
  }

  :deep(.dark .el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: #10b981;
    border-color: #10b981;
  }
}

// 简洁的复选框样式（全屏模式）
.checkbox-simple {
  :deep(.el-checkbox__inner) {
    background-color: #ffffff;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    width: 18px;
    height: 18px;
    transition: all 0.2s ease;

    &:hover {
      border-color: #9ca3af;
    }
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: #10b981;
    border-color: #10b981;

    &::after {
      border-color: #ffffff;
      border-width: 2px;
      width: 4px;
      height: 8px;
      left: 6px;
      top: 2px;
    }
  }

  // 暗黑模式
  :deep(.dark .el-checkbox__inner) {
    background-color: #374151;
    border-color: #6b7280;

    &:hover {
      border-color: #9ca3af;
    }
  }
}

// 优雅的复选框样式（小组件模式）
.checkbox-elegant {
  :deep(.el-checkbox__inner) {
    background-color: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    width: 18px;
    height: 18px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      border-color: rgba(255, 255, 255, 0.9);
      background-color: rgba(255, 255, 255, 0.95);
      transform: scale(1.05);
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    }
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: #10b981;
    border-color: #10b981;

    &::after {
      border-color: #ffffff;
      border-width: 2px;
      width: 5px;
      height: 9px;
      left: 6px;
      top: 2px;
    }

    &:hover {
      background-color: #059669;
      border-color: #059669;
      transform: scale(1.05);
    }
  }

  :deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
    background-color: #10b981;
    border-color: #10b981;
  }

  // 聚焦状态
  :deep(.el-checkbox__input.is-focus .el-checkbox__inner) {
    border-color: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  }
}

.widget-btn {
  // Light theme (default)
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease-out;
  color: rgba(0, 0, 0, 0.7);
  position: relative;
  z-index: 60;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);

  &:active {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(0.95);
    transition-duration: 0.05s;
  }

  // Dark theme
  :global(.dark) & {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.8);

    &:active {
      background: rgba(255, 255, 255, 0.15);
      transform: scale(0.95);
    }
  }
}

// 全屏对话框样式
.fullscreen-todo-dialog {
  .el-dialog__body {
    max-height: 60vh;
    overflow-y: auto;
  }
}

// 输入框容器样式
.todo-input-container {
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;

  &:hover {
    background: rgba(255, 255, 255, 0.5);
    border-color: rgba(255, 255, 255, 0.3);
  }

  &:focus-within {
    background: rgba(255, 255, 255, 0.6);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
  }

  // AI生成时的流动光带效果
  &.ai-generating {
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(
        45deg,
        transparent,
        rgba(59, 130, 246, 0.8),
        rgba(147, 51, 234, 0.8),
        rgba(236, 72, 153, 0.8),
        rgba(59, 130, 246, 0.8),
        transparent
      );
      background-size: 300% 300%;
      border-radius: 10px;
      z-index: -1;
      animation: flowingLight 2s linear infinite;
    }

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: inherit;
      border-radius: 8px;
      z-index: -1;
    }
  }

  textarea {
    min-height: 24px;
    max-height: 24px; // 限制最大高度为1行
    line-height: 1.5;
    overflow: hidden; // 防止多行显示
    
    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
}

// 全屏输入框容器样式
.fullscreen-input-container {
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;

  &:hover {
    background: rgba(255, 255, 255, 0.5);
    border-color: rgba(255, 255, 255, 0.3);
  }

  &:focus-within {
    background: rgba(255, 255, 255, 0.6);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
  }

  // AI生成时的流动光带效果
  &.ai-generating {
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(
        45deg,
        transparent,
        rgba(59, 130, 246, 0.8),
        rgba(147, 51, 234, 0.8),
        rgba(236, 72, 153, 0.8),
        rgba(59, 130, 246, 0.8),
        transparent
      );
      background-size: 300% 300%;
      border-radius: 12px;
      z-index: -1;
      animation: flowingLight 2s linear infinite;
    }

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: inherit;
      border-radius: 10px;
      z-index: -1;
    }
  }

  textarea {
    min-height: 27px; // 18px字体 * 1.5行高 = 27px
    max-height: 27px; // 限制最大高度为1行
    line-height: 1.5;
    overflow: hidden; // 防止多行显示
    
    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
}

// 移除滑入动画

// Sparkle按钮样式
.sparkle-button {
  &:hover {
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}



// 流动光带动画
@keyframes flowingLight {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// 滑块样式
.category-slider-wrapper {
  @apply w-32 ml-2;
  .slider-tabs {
    @apply relative w-full bg-gray-200/80 dark:bg-gray-800/80 rounded-lg p-0.5 flex;
    .slider-track {
      @apply absolute inset-0.5 pointer-events-none;
      .slider-indicator {
        @apply h-full bg-white dark:bg-gray-700 rounded-md shadow-sm transition-transform duration-300 ease-out;
      }
    }
    .slider-tab {
      @apply relative z-10 text-center py-1 px-2 text-xs font-medium cursor-pointer transition-colors duration-200 flex-1 whitespace-nowrap;
      color: #6b7280;
      &:hover {
        color: #374151;
      }
      &.active {
        color: #3b82f6;
        font-weight: 600;
      }
    }
  }

  &.is-fullscreen {
    @apply w-auto;
    width: 180px;
    height: 36px;
    .slider-tabs {
      @apply p-1;
      height: 100%;
      .slider-indicator {
        @apply rounded-lg;
      }
      .slider-tab {
        @apply py-1.5 px-3 text-sm;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.dark .category-slider-wrapper {
  .slider-tabs {
    .slider-tab {
      color: #9ca3af;
      &:hover {
        color: #f3f4f6;
      }
      &.active {
        color: #60a5fa;
      }
    }
  }
}
</style>
