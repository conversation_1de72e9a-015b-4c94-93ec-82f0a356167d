<template>
  <div>
    <slot :open="openDialog"></slot>
    <el-dialog v-model="createVisible" :show-close="false" class="custom-dialog">
      <template #header>
        <button class="close-btn" @click="createVisible = false">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'plus-circle']" class="icon" />
          <span class="title">创建</span>
        </div>
      </template>
      <div class="p-6">
        <div class="grid grid-cols-2 gap-5">
          <button
            @click="openCreateGroup"
            class="create-btn create-btn1 flex flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-5 text-center transition-all hover:border-blue-200 hover:bg-blue-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <div
              class="mb-3 flex h-14 w-14 items-center justify-center rounded-full bg-gradient-to-br from-blue-400 to-blue-600 p-3 text-white shadow-md transition-transform hover:scale-110"
            >
              <font-awesome-icon :icon="['fas', 'users']" class="w-8 h-8" />
            </div>
            <h4 class="text-base font-semibold text-gray-900">创建群组</h4>
            <p class="mt-2 text-sm text-gray-600">创建新的工作群组</p>
          </button>
          <!-- <button
            class="create-btn create-btn2 flex flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-5 text-center transition-all hover:border-green-200 hover:bg-green-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            <div
              class="mb-3 flex h-14 w-14 items-center justify-center rounded-full bg-gradient-to-br from-green-400 to-green-600 p-3 text-white shadow-md transition-transform hover:scale-110"
            >
              <font-awesome-icon :icon="['fas', 'clipboard-check']" class="w-8 h-8" />
            </div>
            <h4 class="text-base font-semibold text-gray-900">创建任务</h4>
            <p class="mt-2 text-sm text-gray-600">创建新的工作任务</p>
          </button> -->
          <!-- <button
            class="create-btn create-btn3 flex flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-5 text-center transition-all hover:border-purple-200 hover:bg-purple-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <div
              class="mb-3 flex h-14 w-14 items-center justify-center rounded-full bg-gradient-to-br from-purple-400 to-purple-600 p-3 text-white shadow-md transition-transform hover:scale-110"
            >
              <font-awesome-icon :icon="['fas', 'file']" class="w-8 h-8" />
            </div>
            <h4 class="text-base font-semibold text-gray-900">创建文档</h4>
            <p class="mt-2 text-sm text-gray-600">创建新的文档资料</p>
          </button> -->
          <button
            @click="openCreateToDo"
            class="create-btn create-btn4 flex flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-5 text-center transition-all hover:border-amber-200 hover:bg-amber-100 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-amber-500"
          >
            <div
              class="mb-3 flex h-14 w-14 items-center justify-center rounded-full bg-gradient-to-br from-orange-400 to-orange-600 p-3 text-white shadow-md transition-transform hover:scale-110"
            >
              <font-awesome-icon :icon="['fas', 'calendar-plus']" class="w-8 h-8" />
            </div>
            <h4 class="text-base font-semibold text-gray-900">创建日程</h4>
            <p class="mt-2 text-sm text-gray-600">安排新的工作日程</p>
          </button>
        </div>
      </div>
    </el-dialog>
    <CreateGroup ref="groupDialog" />
    <CreateToDo :add="true" ref="toDoDialog" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import CreateGroup from "@/components/CreateGroup/index.vue";
import CreateToDo from "@/components/CalendarComp/index.vue";
// 创建
const createVisible = ref(false);
const openDialog = () => {
  createVisible.value = true;
};
// 创建群组
const groupDialog: any = ref(null);
const openCreateGroup = () => {
  if (groupDialog.value) groupDialog.value.openDialog();
  createVisible.value = false;
};
// 创建日程
const toDoDialog: any = ref(null);
const openCreateToDo = () => {
  if (toDoDialog.value) toDoDialog.value.openDialog();
  createVisible.value = false;
};
</script>

<style lang="scss" scoped>
.dark {
  .create-btn {
    @apply border-gray-700 bg-gray-800 hover:bg-gray-700;
    h4 {
      @apply text-white;
    }
    p {
      @apply text-gray-400;
    }
  }
  .create-btn1 {
    @apply hover:border-blue-800;
  }
  .create-btn2 {
    @apply hover:border-green-800;
  }
  .create-btn3 {
    @apply hover:border-purple-800;
  }
  .create-btn4 {
    @apply hover:border-amber-800;
  }
}
</style>
