"use strict";

const Services = require("ee-core/services");
const knex = require("../../database/knex");
const { v4: uuidv4 } = require("uuid");

/**
 * 待办事项 数据存储
 * @class
 */
class TodosService {
  constructor() {
    this.tableName = "todos";
  }

  /**
   * 获取当前用户userid
   */
  async getUserId() {
    let userInfo = await Services.get("database.jsondb").getItem("userInfo");
    if (!userInfo) return "";
    userInfo = JSON.parse(userInfo);
    return userInfo.id;
  }

  /**
   * 获取待办事项列表
   * @param {string} params - JSON对象 { pageNo, pageSize }
   */
  async getTodosList(params) {
    if (!params) return [];
    const userId = await this.getUserId();
    const { pageNo = 1, pageSize = 100 } = JSON.parse(params);
    const offset = (pageNo - 1) * pageSize;
    
    const queryBuilder = knex(this.tableName).where({ userId });
    
    // 按创建时间倒序排列，未完成的在前面
    const rows = await queryBuilder
      .clone()
      .select()
      .orderByRaw("completed ASC, createTime DESC")
      .limit(pageSize)
      .offset(offset);
      
    const [{ total }] = await queryBuilder.clone().count({ total: "id" });
    
    return {
      list: rows,
      total,
    };
  }

  /**
   * 新增待办事项
   * @param {string} content - JSON对象 { content }
   */
  async addTodo(content) {
    if (!content) return;
    const userId = await this.getUserId();
    const { content: todoContent } = JSON.parse(content);
    
    return knex(this.tableName).insert({
      id: uuidv4(),
      userId,
      content: todoContent,
      completed: false,
      createTime: Date.now(),
      updateTime: Date.now(),
    });
  }

  /**
   * 更新待办事项状态
   * @param {string} content - JSON对象 { id, completed }
   */
  async updateTodoStatus(content) {
    if (!content) return;
    const userId = await this.getUserId();
    const { id, completed } = JSON.parse(content);
    
    return knex(this.tableName)
      .where({ userId, id })
      .update({
        completed,
        updateTime: Date.now(),
      });
  }

  /**
   * 更新待办事项内容
   * @param {string} content - JSON对象 { id, content }
   */
  async updateTodoContent(content) {
    if (!content) return;
    const userId = await this.getUserId();
    const { id, content: todoContent } = JSON.parse(content);
    
    return knex(this.tableName)
      .where({ userId, id })
      .update({
        content: todoContent,
        updateTime: Date.now(),
      });
  }

  /**
   * 删除待办事项
   * @param {string} id
   */
  async deleteTodo(id) {
    if (!id) return;
    const userId = await this.getUserId();
    return knex(this.tableName).where({ userId, id }).del();
  }
}

module.exports = TodosService; 