/**
 * @description 用户状态
 */
export interface Meeting {
  id: number;
  name: string;
}

export interface UserState {
  token: string;
  name: string;
  avatar: string;
  userId: string;
  userInfo: Record<string, any>;
  secretLevel: number;
  contactId: string;
}

export interface AuthState {
  routeName: string;
  authButtonList: {
    authButton: string[];
    useProTable: string[];
  };
  authMenuList: Menu.MenuOptions[];
}

export type LayoutType = "vertical" | "classic" | "transverse" | "columns";
export type AssemblySizeType = "large" | "default" | "small";
export type ThemeType = "light" | "dark";

/* GlobalState */
export interface GlobalState {
  layout: LayoutType;
  assemblySize: AssemblySizeType;
  maximize: boolean;
  primary: string;
  tabs: boolean;
  tabsIcon: boolean;
}
/* KeepAliveState */
export interface KeepAliveState {
  keepAliveName: string[];
}

/**
 * @description AI智能通知状态
 */
export interface AiSummaryNotification {
  id: number;
  templateId?: number;
  userId: string;
  title: string;
  content: string;
  type: number;
  sendStatus: number;
  sendTime?: number; // LocalDateTime from backend, serialized as timestamp
  createTime: number; // LocalDateTime from backend, serialized as timestamp
  
  // Frontend-specific fields, will be populated during mapping
  icon?: string[];
  priority?: 'high' | 'medium' | 'low';
  category?: string;
  time?: string; // Formatted time string for display
}

export interface AiNotificationStatistics {
  id: number;
  name: string;
  type: number;
  typeName: string;
  count: number;
  statisticsTime: string;
  accessUrl?: string;
}

export interface AiNoticeState {
  aiNoticeList: AiSummaryNotification[];
  aiNotificationStats: AiNotificationStatistics[];
}

/* noticesState--xhl */
export interface NoticesState {
  //总消息条数
  noticeList: Array<any>;
  noticeCount: number;
  approveList: Array<any>;
  approveCount: number;
}
/* noticesState--xhl */
export interface BusinessState {
  //总消息条数
  businessList: Array<any>;
}
/* TabsState */
export interface TabsState {
  tabsMenuList: TabsMenuProps[];
}

/* tabsMenuProps */
export interface TabsMenuProps {
  icon: string;
  title: string;
  path: string;
  name: string;
  close: boolean;
  isKeepAlive?: boolean;
}

/* 研讨 */
export interface Talk {
  activeChatId: string;
  activeContact: Record<string, any>;
  filterWords: Array<any>;
  createGroup: Record<string, any>;
  commonEmoStr: string;
  commonEmoji: Record<string, any>;
  casicEmoStr: string;
  casicEmoji: Record<string, any>;
  approveRole: Array<any>;
  ifChat: Boolean;
  ifContact: Boolean;
  quoteMsg: Object;
  isBol: Boolean;
  isBolStatus: Boolean;
  isStatus: Boolean;
}
/* 历史聊天 */
export interface History {
  msgHistory: Record<string, any>;
  msgIds: Record<string, any>;
  voteList: Record<string, any>;
  relayList: Record<string, any>;
  officialDocuments: Record<string, any>;
  msgBuffer: Record<string, any>;
  activity: Record<string, any>;
}
/* 最近联系人 */
export interface Recent {
  listRecents: Array<any>;
}
/* 群组管理 */
export interface Contact {
  groupInfo: Record<string, any>;
  groupMembers: Record<string, any>;
}
/*文件管理*/
export interface File {
  downloadList: Array<any>;
  uploadList: Array<any>;
  badge: number;
}

/*下载器*/
export interface Download {
  downloadList: Array<any>;
  progress: number;
  uploadList: Array<any>;
  badge: false;
  notifications: number;
  voteId: string;
  voteList: Array<any>;
}

/**
 * @description AI 微应用状态
 */
export interface AiAppState {
  myAppList: AiAppData[];
  loading: boolean;
}

export interface AiAppData {
  id: number;
  appId: string;
  name: string;
  icon: string;
  description?: string;
  category?: string;
  tags?: string[];
  type?: number;
  status?: number;
  createTime?: string;
  isHot?: boolean;
  rating?: number;
  themeColor?: string;
  inputs?: any;
  sort?: number;
}
