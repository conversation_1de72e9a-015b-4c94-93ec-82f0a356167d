"use strict";

const Log = require("ee-core/log");
const Ps = require("ee-core/ps");
const koffi = require("koffi");
const path = require("path");
const { Service } = require("ee-core");

const maxResults = 100;
/**
 * 示例服务（service层为单例）
 * @class
 */
class EveryThingService extends Service {
  constructor(ctx) {
    super(ctx);
    this.everything = null; // Everything DLL 实例
    this.funcs = {}; // 存储绑定的函数
    this.history = []; // 历史搜索记录
    this.init();
  }
  /**
   * 初始化 Everything
   */
  init() {
    try {
      const dllFile = "Everything64.dll";
      const dllPath = path.join(Ps.getExtraResourcesDir(), "dll", dllFile);

      this.everything = koffi.load(dllPath);

      // 绑定 Everything 函数
      this.funcs = {
        setSearch: this.everything.func(
          "Everything_SetSearchW",
          "void",
          ["string"],
          { abi: "stdcall" }
        ),
        query: this.everything.func("Everything_QueryW", "bool", ["bool"], {
          abi: "stdcall",
        }),
        getNumResults: this.everything.func(
          "Everything_GetNumResults",
          "uint",
          [],
          { abi: "stdcall" }
        ),
        getResultFullPath: this.everything.func(
          "Everything_GetResultFullPathNameW",
          "uint",
          ["uint", koffi.pointer("uint16"), "uint"],
          { abi: "stdcall" }
        ),
        cleanUp: this.everything.func("Everything_CleanUp", "void", [], {
          abi: "stdcall",
        }),
      };

      Log.info(
        "🚀 ~ EveryThingService ~ init ~ Everything initialized successfully"
      );
    } catch (error) {
      Log.error(
        "🚀 ~ EveryThingService ~ init ~ Error initializing Everything:",
        error
      );
      throw new Error("Failed to initialize Everything");
    }
  }

  /**
   * 新建搜索
   * @param {string} searchStr - 搜索字符串
   * @returns {Array<string>} - 搜索结果
   */
  async search(searchStr) {
    if (!this.everything || !this.funcs) {
      throw new Error("Everything is not initialized");
    }

    try {
      // 添加到历史记录
      this.addToHistory(searchStr);

      // 设置搜索字符串
      const searchBuffer = Buffer.from(`${searchStr}\0`, "utf16le");
      this.funcs.setSearch(searchBuffer);

      // 执行搜索
      const success = this.funcs.query(true);
      if (!success) {
        Log.error("🚀 ~ EveryThingService ~ search ~ Everything_QueryW failed");
        return [];
      }

      // 获取搜索结果数量
      const numResults = Math.min(this.funcs.getNumResults(), maxResults);
      Log.info("🚀 ~ EveryThingService ~ search ~ numResults:", numResults);

      // 转换搜索结果
      return this.convertResults(numResults);
    } catch (error) {
      Log.error(
        "🚀 ~ EveryThingService ~ search ~ Error during search:",
        error
      );
      return [];
    } finally {
      // 清理资源
      this.funcs.cleanUp();
    }
  }

  /**
   * 转换搜索结果
   * @param {number} numResults - 搜索结果数量
   * @returns {Array<Object>} - 转换后的搜索结果
   */
  convertResults(numResults) {
    const results = [];
    const wcharCount = 260; // 每个路径的缓冲区大小（260 wchar）
    const buffer = Buffer.alloc(wcharCount * 2); // UTF-16

    // 定义常用文件后缀
    const allowedExtensions = [
      ".txt",
      ".jpg",
      ".png",
      ".pdf",
      ".doc",
      ".docx",
      ".xls",
      ".xlsx",
      ".exe",
      ".mp4",
      ".mp3",
    ];

    for (let i = 0; i < numResults; i++) {
      buffer.fill(0); // 清空 buffer
      this.funcs.getResultFullPath(i, buffer, wcharCount);
      const fullPath = buffer.toString("ucs2").replace(/\0/g, ""); // 转换为字符串

      // 提取文件名和路径
      const fileName = path.basename(fullPath);
      const filePath = path.dirname(fullPath);

      // 获取文件类型
      const fileType = path.extname(fullPath).toLowerCase();

      // 过滤条件：没有后缀或后缀不在允许列表中
      if (!fileType) {
        continue;
      }

      // 构造结果对象
      results.push({
        name: fileName,
        icon: fileType, // 将 icon 改为文件后缀
        path: fullPath,
        type: fileType,
      });
    }

    Log.info("🚀 ~ EveryThingService ~ convertResults ~ results:", results);
    return results;
  }

  /**
   * 获取文件图标
   * @param {string} fileType - 文件类型（扩展名）
   * @returns {string} - 文件图标路径
   */
  getFileIcon(fileType) {
    const iconMap = {
      ".txt": "icons/text-file.png",
      ".jpg": "icons/image-file.png",
      ".png": "icons/image-file.png",
      ".pdf": "icons/pdf-file.png",
      ".doc": "icons/word-file.png",
      ".docx": "icons/word-file.png",
      ".xls": "icons/excel-file.png",
      ".xlsx": "icons/excel-file.png",
      ".exe": "icons/executable-file.png",
      ".mp4": "icons/video-file.png",
      ".mp3": "icons/audio-file.png",
      unknown: "icons/unknown-file.png",
    };

    return iconMap[fileType] || iconMap["unknown"];
  }

  /**
   * 添加到历史记录
   * @param {string} searchStr - 搜索字符串
   */
  addToHistory(searchStr) {
    if (!this.history.includes(searchStr)) {
      this.history.push(searchStr);
      Log.info(
        "🚀 ~ EveryThingService ~ addToHistory ~ Added to history:",
        searchStr
      );
    }
  }

  /**
   * 获取历史搜索记录
   * @returns {Array<string>} - 历史搜索记录
   */
  getHistory() {
    return this.history;
  }
}

EveryThingService.toString = () => "[class EveryThingService]";
module.exports = EveryThingService;
