// 请求响应参数（不包含data）
export interface ApiResult {
  code: number; // 状态码
  message: string; // 状态信息
}

// 请求响应参数（包含data）
export interface ApiResultData<T> extends ApiResult {
  data: T; // 返回数据
}

// 分页查询统一结果
export interface PageResult<T> {
  total: number; // 总条数
  list: T[]; // 数据列表
}

// 分页查询基本参数
export interface PageParam {
  pageNo: number; // 第几页
  pageSize?: number; // 每页多少条
  sort?: string; // 排序字段
  order?: string; // 排序方式：asc升序,desc降序
}
