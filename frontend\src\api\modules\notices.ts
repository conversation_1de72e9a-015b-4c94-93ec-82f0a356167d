import request from "@/api";
// 获取系统通知 {pageSize, pageNo, receiver}
export const getNoticesList = async (params?: any) => {
  return await request.get("/admin-api/chat/messageNotification/page", { params });
};
// 创建系统通知
export const createNotice = async (params: any) => {
  return await request.post("/admin-api/chat/messageNotification/create", params);
};
// 创建群组审批
export const saveGroupApprove = async (params: any) => {
  return await request.post("/admin-api/chat/groupApprove/add", params);
};
// 获取审批列表
export const getGroupApproveList = async (params: any) => {
  return await request.get("/admin-api/chat/groupApprove/getApproveList", { params });
};
// 审批操作
export const updateGroupApprove = async (params: any) => {
  return await request.put("/admin-api/chat/groupApprove/approve", params);
};
