html {
  font-family:
    Inter,
    ui-sans-serif,
    system-ui,
    -apple-system,
    system-ui,
    Segoe UI,
    Roboto,
    Helvetica Neue,
    Arial,
    Noto Sans,
    sans-serif,
    Apple Color Emoji,
    Segoe UI Emoji,
    Segoe UI Symbol,
    Noto Color Emoji;
}

.el-dialog.custom-dialog {
  width: 700px;
  margin-bottom: 0;
  @apply p-0 overflow-hidden rounded-xl transition-all;

  &.profile {
    width: 500px;
  }

  .el-dialog__header {
    @apply border-b border-gray-200 p-4;

    .icon {
      @apply p-1.5 text-xl text-blue-600;
    }

    .title {
      @apply text-lg font-medium text-gray-900;
    }

    .close-btn {
      @apply px-2 float-right text-xl border-0 rounded-lg bg-transparent text-gray-400 hover:bg-gray-200 hover:text-gray-900;
    }
  }

  .el-dialog__body {
    @apply p-4;
  }
}

.el-dialog.custom-dialog .el-dialog__body {
  /* 视口高度减去外层上下间距总和（120px+60px）及顶部head高度（60px） */
  max-height: calc(100vh - 180px - 60px);
  box-sizing: border-box;
  overflow-y: auto;
}

.el-dialog.need-foot {
  .el-dialog__footer {
    @apply p-4 border-t border-gray-200;
  }
}

.dark {
  .el-dialog.custom-dialog {
    .el-dialog__header {
      @apply border-gray-700;

      .icon {
        @apply text-blue-500;
      }

      .title {
        @apply text-white;
      }

      .close-btn {
        @apply hover:bg-gray-600 hover:text-white;
      }
    }
  }

  .el-dialog.need-foot {
    .el-dialog__footer {
      @apply border-gray-700;
    }
  }
}

.el-tree.custom-tree {
  @apply px-2 text-sm text-gray-900 bg-transparent;

  .el-tree-node {
    .el-tree-node__content {
      @apply my-1 rounded-md hover:bg-blue-50;
      padding-right: 18px;
    }

    &.is-current>.el-tree-node__content {
      @apply bg-blue-100;
    }
  }
}

.dark {
  .el-tree.custom-tree {
    @apply text-white;

    .el-tree-node {
      .el-tree-node__content {
        @apply hover:bg-blue-900/30;
      }

      &.is-current>.el-tree-node__content {
        @apply bg-blue-900/40;
      }
    }
  }
}

.el-transfer.custom-transfer {
  @apply text-sm text-gray-900;

  .el-transfer-panel {
    width: 160px;
    height: 280px;
    @apply border rounded-lg;

    .el-transfer-panel__header {
      @apply bg-transparent border-0 border-b;

      .el-checkbox__label {
        @apply text-sm;
      }
    }

    .el-transfer-panel__body {
      @apply border-0;
      height: 240px;

      .el-transfer-panel__filter {
        @apply p-2;
      }
    }
  }

  .el-transfer__buttons {
    @apply p-0;

    .el-button {
      @apply block m-2;
    }
  }

  .el-transfer-panel__list.is-filterable {
    &::-webkit-scrollbar {
      width: 6px;
      /* 滚动条宽度 */
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
      /* 滚动条轨道颜色 */
    }

    &::-webkit-scrollbar-thumb {
      display: none;
      background-color: rgba(154, 154, 154, 0.3);
      /* 滚动条滑块颜色 */
      border-radius: 6px;
      /* 滑块圆角 */
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #c1c1c1;
      /* 鼠标悬停时的滑块颜色 */
    }
  }

  .el-transfer-panel__list.is-filterable:hover {
    &::-webkit-scrollbar-thumb {
      display: block;
    }
  }
}

.el-dropdown-menu.side-drop-menu {
  @apply bg-white;

  .el-dropdown-menu__item {
    @apply bg-transparent flex items-center px-4 py-2 gap-2 text-gray-700 hover:bg-gray-50;

    &.logout {
      @apply text-red-600 hover:bg-red-50 hover:text-red-700;
    }
  }
}

.dark {
  .el-dropdown-menu.side-drop-menu {
    @apply bg-gray-700;

    .el-dropdown-menu__item {
      @apply text-gray-200 hover:text-white hover:bg-gray-600;

      &.logout {
        @apply text-red-500 hover:bg-gray-600;
      }
    }
  }
}

.el-dropdown__popper {
  border-radius: 12px !important;
  overflow: hidden;
}

.el-tabs.custom-tabs {
  width: 250px;
  height: 280px;
  @apply border border-gray-200 rounded-lg;

  .el-tabs__header {
    @apply mb-0 order-1;
  }

  .el-tabs__nav {
    @apply w-full;

    .el-tabs__item {
      @apply flex-1 px-0;

      .tab-item {
        @apply flex items-center;
      }
    }
  }
}

.el-drawer.custom-drawer {
  padding-top: 48px;
  border-right: 1px solid var(--el-border-color);
  border-bottom: 1px solid var(--el-border-color);
  .el-drawer__header {
    @apply px-4 py-2 m-0 border-b border-gray-200;
    color: var(--el-text-color-regular);
  }
}

.dark {
  .el-drawer.custom-drawer {
    .el-drawer__header {
      @apply border-gray-700;
    }
  }
}

.notice-menu.el-dropdown__popper {
  .el-scrollbar {
    @apply h-96 w-96;
  }
  .el-dropdown-menu__item {
    @apply p-0 hover:bg-transparent;
  }
}

.el-popover.el-popper {
  min-width: 20px !important;
}

.el-popover.el-popper.custom-popper {
  width: auto !important;
  @apply border-0 rounded-lg p-2 space-y-1;

  & > div {
    @apply cursor-pointer hover:opacity-75;
  }
}

.drawer-box .el-overlay {
  border-top: 1px solid var(--el-border-color);
}
