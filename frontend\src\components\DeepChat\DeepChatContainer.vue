<template>
  <div class="deep-chat-container">
    <div :class="{ 'conversation-section-container': true, hidden: !isConversationListVisible }">
      <el-card class="conversation-section" :body-style="{ padding: '10px' }" shadow="never">
        <div class="header">
          <el-button style="width: 100px; border-radius: 8px" type="primary" size="small" @click="createNewConversation">
            <el-icon>
              <Plus />
            </el-icon>
            新对话
          </el-button>
        </div>
        <el-scrollbar>
          <div class="conversation-list" :style="{ height: 'calc(100vh - 55px)' }">
            <el-tag
              v-for="conv in conversations"
              :key="conv.chatId"
              :effect="currentChatId === conv.chatId ? 'dark' : 'plain'"
              class="conversation-tag"
              closable
              @click="switchConversation(conv)"
              @close.stop="removeConversation(conv)"
            >
              <el-input
                v-if="editingId === conv.chatId"
                v-model="conv.name"
                size="small"
                class="editable-input"
                @blur="finishEditing"
                @keyup.enter="finishEditing"
              />
              <template v-else>
                <span @dblclick="editConversation(conv)">{{
                  conv.name.substring(0, 3) + (conv.name.length > 3 ? "..." : "")
                }}</span>
              </template>
            </el-tag>
          </div>
        </el-scrollbar>
      </el-card>
    </div>

    <div class="toggle-icon-container" @click="toggleConversationList">
      <font-awesome-icon :icon="['fas', 'list']" style="color: #efeff1" />
    </div>
    <div class="close-icon-container" @click="closeDialog">
      <font-awesome-icon :icon="['fas', 'close']" style="color: #efeff1" />
    </div>

    <div class="chat-wrapper">
      <deep-chat
        ref="deepChatRef"
        :key="currentChatId"
        :messageStyles="{
          default: {
            user: { bubble: { marginLeft: 0, maxWidth: '100%', borderRadius: '8px' } },
            ai: { bubble: { maxWidth: '100%', borderRadius: '8px' } }
          }
        }"
        :submitButtonStyles="{}"
        :avatars="{
          default: { styles: { position: 'left' } },
          ai: { src: aiAvatar, styles: { position: 'left' } }
        }"
        :inputAreaStyle="{
          position: 'fixed',
          top: '0',
          width: '440px',
          backgroundColor: 'rgba(0, 0, 0, 0.652)',
          borderRadius: '8px'
        }"
        :textInput="{
          styles: {
            text: { color: 'white' },
            container: { width: '85%', marginLeft: '4px', backgroundColor: '#717177', borderRadius: '8px' },
            focus: { border: '2px solid #a2a2ff' }
          },
          placeholder: { text: '请输入搜索内容', style: { color: 'white' } }
        }"
        :style="{
          top: '60px',
          border: 'none',
          position: 'fixed',
          width: '440px',
          left: '110px',
          borderRadius: '8px',
          height: 'calc(100vh - 60px)',
          backgroundColor: 'rgba(0, 0, 0, 0.652)'
        }"
        :history="currentMessages"
        :images="false"
        :responseInterceptor="responseInterceptor"
        :requestInterceptor="requestInterceptor"
        :connect="connectConfig"
        @new-message="onMessageAdded"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { Plus } from "@element-plus/icons-vue";
import "deep-chat";
import type { Chat } from "@/stores/interface/chat";
import aiAvatar from "@/assets/images/logo.png";
import { useKnowledgeChatStore } from "@/stores/modules/knowledgeChats";
import { ipc } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";
// import { fetchEventSource } from "@microsoft/fetch-event-source";
// Deep Chat API configuration
const chatStore = useKnowledgeChatStore();
const conversations = computed(() => chatStore.chats);
const editingId = ref<string | null>(null); // 当前正在编辑的会话索引
const deepChatRef = ref<HTMLElement | null>(null);
const currentChatId = computed(() => chatStore.currentChatId);
const currentMessages = computed(() => chatStore.currentChat);
const isConversationListVisible = ref(true);

const connectConfig = {
  url: "/chatRpc/streamChatRpc",
  method: "POST",
  headers: {
    "Content-Type": "application/json"
  },
  stream: {
    streaming: true
  }
};

function createNewConversation() {
  chatStore.createNewChat();
}
function switchConversation(chat: Chat) {
  chatStore.switchChat(chat.chatId);
}
function toggleConversationList() {
  isConversationListVisible.value = !isConversationListVisible.value;
}
// Removes a conversation
function removeConversation(chat: Chat) {
  chatStore.deleteChat(chat.chatId);
}

// Handles new messages from the chat
const onMessageAdded = (event: CustomEvent) => {
  chatStore.addMessage(event);
};

function requestInterceptor(requestDetails: any) {
  console.log("🚀 ~ requestInterceptor ~ requestDetails:", requestDetails.body);
  const userMessage = requestDetails.body.messages?.[requestDetails.body.messages.length - 1]?.text || "";

  // 构建后端期望的格式
  const transformedBody = {
    model: "qwen3:4b",
    query: userMessage, // 提取用户输入的文本
    history: []
    // 如果后端需要完整的对话历史，可以这样转换：
    // messages: details.body.messages.map(msg => ({
    //   role: msg.role,
    //   content: msg.text
    // }))
  };

  console.log("🚀 转换后的请求数据:", transformedBody);

  // 返回修改后的请求配置
  return {
    ...requestDetails,
    body: transformedBody
  };
}

async function responseInterceptor(response: any) {
  console.log("🚀 ~ responseInterceptor ~ response:", response);
  chatStore.setCurrentInfo(response);
  try {
    const choices = response.choices;
    if (Array.isArray(choices) && choices.length > 0) {
      const delta = choices[0].delta;
      if (delta && delta.content) {
        // 返回 Deep Chat 支持的格式
        return { text: delta.content };
      }
    }
  } catch (e) {
    console.error("responseInterceptor error:", e);
  }

  return { text: "" };
}

// 开始编辑会话名称
function editConversation(chat: Chat) {
  editingId.value = chat.chatId;
}

// 完成编辑会话名称
function finishEditing() {
  editingId.value = null;
}

function closeDialog() {
  ipc.send(ipcApiRoute.closeFloatingDialog, 1);
}

onMounted(() => {
  // Initialize with an empty conversation if none exists
  if (chatStore.chats.length === 0) {
    chatStore.createNewChat();
  }
  // deepChatRef.value.connect = {
  //   endpoint: "http://************:48099/admin-api/chat/llm/stream",
  //   method: "POST",
  //   headers: {
  //     "Content-Type": "application/json"
  //   },
  //   streaming: true
  // };
});
</script>

<style lang="scss" scoped>
.deep-chat-container {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: 100%;
}

.conversation-section {
  border: none;
  background-color: transparent;
}

.conversation-section-container {
  // width: 26%;
  width: 100px;
  background-color: #000000a6;
  border-radius: 8px;
  box-sizing: border-box;
  height: 100%;
  transition: transform 0.3s ease-in-out;
}

.conversation-section-container.hidden {
  visibility: hidden;
  opacity: 0;
}

.toggle-icon-container {
  position: fixed;
  top: 15px;
  left: 118px;
  cursor: pointer;
  z-index: 1000;
}

.close-icon-container {
  position: fixed;
  top: 15px;
  right: 8px;
  cursor: pointer;
  z-index: 1000;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.conversation-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
  flex-wrap: nowrap;
}

.conversation-tag {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  border-radius: 8px;
  min-height: 24px;
  flex-shrink: 0;
}

.editable-input {
  width: 100px;
}

.chat-wrapper {
  flex: 1;
  box-sizing: border-box;
  background-color: "rgba(0, 0, 0, 0.652)";
}
</style>
@/stores/modules/knowledgeChats
