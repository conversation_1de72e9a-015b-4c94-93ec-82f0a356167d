# 应用程序图标管理指南

本文档介绍如何管理和使用易知云雀应用程序的图标系统。

## 📁 图标文件结构

```
project/
├── public/images/logo.svg          # 源图标文件 (SVG 格式)
├── build/icons/                    # 构建用图标
│   ├── icon.ico                    # Windows 主图标
│   ├── icon.icns                   # macOS 主图标
│   ├── icon.png                    # Linux 主图标
│   ├── favicon.ico                 # 网页图标
│   └── *.png                       # 各种尺寸的 PNG 图标
├── public/images/                   # 运行时图标
│   ├── logo-32.png                 # 窗口图标
│   ├── tray*.png                   # 托盘图标 (浅色/深色主题)
│   └── notice/                     # 通知图标
└── scripts/                        # 图标管理脚本
    ├── generate-icons.js           # 图标生成脚本
    └── verify-icons.js             # 图标验证脚本
```

## 🎨 图标用途说明

### 1. 系统托盘图标
- **浅色主题**: `public/images/tray.png` (16x16)
- **深色主题**: `public/images/tray_dark.png` (16x16)
- **高分辨率**: `@2x` 版本 (32x32)
- **闪烁效果**: `tray_empty.png` 和 `tray_dark_empty.png`

### 2. 应用程序窗口图标
- **路径**: `public/images/logo-32.png`
- **尺寸**: 32x32 像素
- **格式**: PNG

### 3. 安装程序图标
- **Windows**: `build/icons/icon.ico` (多尺寸 ICO)
- **macOS**: `build/icons/icon.icns` (ICNS 格式)
- **Linux**: `build/icons/icon.png` (PNG 格式)

### 4. 网页图标
- **路径**: `build/icons/favicon.ico`
- **尺寸**: 16x16, 32x32
- **格式**: ICO

### 5. 通知图标
- **用户消息**: `public/images/notice/user_icon.png`
- **群组消息**: `public/images/notice/group_icon.png`
- **系统消息**: `public/images/notice/system_icon.png`

## 🛠️ 图标管理命令

### 生成图标
```bash
# 从 SVG 源文件生成所有图标
npm run icon

# 使用旧版 ee-bin 生成图标
npm run icon-legacy
```

### 验证图标
```bash
# 验证所有图标文件是否正确生成
npm run icon-verify
```

## 🎯 图标规范

### 尺寸要求
- **16x16**: 托盘图标、小图标
- **32x32**: 窗口图标、中等图标
- **64x64**: 大图标
- **128x128**: 高分辨率图标
- **256x256**: 超高分辨率图标
- **512x512**: 最高分辨率图标

### 格式要求
- **ICO**: Windows 平台图标 (支持多尺寸)
- **ICNS**: macOS 平台图标
- **PNG**: 通用图标格式，支持透明度

### 主题支持
- **浅色主题**: 使用标准图标
- **深色主题**: 使用反色图标 (仅托盘图标)
- **高分辨率**: 提供 @2x 版本

## 🔧 自定义图标

### 1. 更新源图标
1. 替换 `public/images/logo.svg` 文件
2. 运行 `npm run icon` 重新生成所有图标
3. 运行 `npm run icon-verify` 验证生成结果

### 2. 修改图标配置
编辑 `scripts/generate-icons.js` 文件中的配置：

```javascript
const config = {
  sourceIcon: 'public/images/logo.svg',  // 源文件路径
  icons: [
    // 添加新的图标配置
    { name: 'custom.png', size: 48, format: 'png' },
  ]
};
```

### 3. 更新应用配置
修改相关配置文件中的图标路径：
- `electron/config/builder.json` - 构建配置
- `electron/config/config.default.js` - 运行时配置

## 🌙 深色主题支持

应用程序会自动检测系统主题并选择合适的托盘图标：

```javascript
// 托盘插件会自动处理主题切换
nativeTheme.on('updated', () => {
  // 自动更新托盘图标
});
```

## 📱 高分辨率支持

对于支持高分辨率显示器的系统，应用程序会自动使用 @2x 版本的图标：

- `tray.png` → `<EMAIL>`
- `tray_dark.png` → `<EMAIL>`

## 🚀 最佳实践

1. **始终使用 SVG 作为源文件** - 确保图标在所有尺寸下都清晰
2. **运行验证脚本** - 每次修改后验证图标完整性
3. **测试不同主题** - 确保图标在浅色和深色主题下都可见
4. **检查高分辨率显示** - 验证 @2x 图标在高分辨率屏幕上的效果
5. **保持一致性** - 所有图标应使用相同的设计风格

## 🐛 故障排除

### 图标不显示
1. 检查文件路径是否正确
2. 验证文件权限
3. 运行 `npm run icon-verify` 检查完整性

### 图标模糊
1. 确保使用了正确尺寸的图标
2. 检查是否提供了高分辨率版本
3. 验证源 SVG 文件质量

### 深色主题图标不可见
1. 确保生成了深色主题图标
2. 检查系统主题检测是否正常工作
3. 验证图标颜色反转是否正确

## 📞 技术支持

如果遇到图标相关问题，请：
1. 运行 `npm run icon-verify` 获取详细报告
2. 检查控制台错误信息
3. 查看相关配置文件
