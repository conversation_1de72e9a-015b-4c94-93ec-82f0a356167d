<template>
  <div class="mt-4 bg-gray-700 p-2 rounded text-sm text-white">
    <!-- <div class="font-bold mb-2">历史会话</div> -->
    <ul class="space-y-1">
      <li
        v-for="(session, index) in sessions"
        :key="index"
        @click="selectSession(index)"
        class="cursor-pointer hover:text-blue-400"
      >
        会话 {{ index + 1 }}（{{ session.length }} 条）
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  sessions: string[][];
}>();

const emit = defineEmits<{
  (e: "select", index: number): void;
}>();

function selectSession(index: number) {
  emit("select", index);
}
</script>
