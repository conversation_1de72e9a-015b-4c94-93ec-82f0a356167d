<template>
  <el-form-item :label="label" class="toggle-item" @click="toggleSwitch">
    <div class="flex justify-end w-full">
      <el-switch v-model="localEnable"></el-switch>
    </div>
  </el-form-item>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElFormItem, ElSwitch } from "element-plus";

const props = defineProps<{
  label: string;
  modelValue: boolean;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
}>();

const localEnable = ref(props.modelValue);

watch(
  () => props.modelValue,
  newValue => (localEnable.value = newValue)
);

watch(localEnable, newValue => emit("update:modelValue", newValue));

// 点击空白区域也可切换
const toggleSwitch = (event: MouseEvent) => {
  if (!event.target || !(event.target as HTMLElement).closest(".el-switch")) {
    localEnable.value = !localEnable.value;
  }
};
</script>

<style lang="scss">
.toggle-item {
  margin-bottom: 0;
  &:hover {
    background-color: #ededed;
  }
}
</style>
