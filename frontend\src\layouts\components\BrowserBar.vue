<template>
  <div class="flex items-center space-x-2 browser">
    <NoticeComp #default="{ noticeNum }">
      <el-badge :value="noticeNum" :hidden="noticeNum == 0" :offset="[0, 6]">
        <button class="control-btn" title="系统通知">
          <Icon :icon="notificationsRounded" class="control-btn-icon" />
        </button>
      </el-badge>
    </NoticeComp>

    <CalendarComp ref="calendarDialog">
      <template #default="{ open, eventNum }">
        <el-badge :value="eventNum" :hidden="eventNum == 0" :offset="[0, 6]">
          <button class="control-btn" @click="open" title="日历">
            <Icon :icon="calendarMonthRounded" class="control-btn-icon" />
          </button>
        </el-badge>
      </template>
    </CalendarComp>

    <div>
      <el-badge :value="count" :hidden="count == 0" :offset="[0, 6]">
        <button class="control-btn" @click="openDownload" title="文件下载管理">
          <Icon :icon="downloadRounded" class="control-btn-icon" />
        </button>
      </el-badge>
      <DownloadDialog ref="dialogdown" />
    </div>

    <!-- <button class="control-btn" @click="toggleTheme" title="主题">
      <Icon :icon="isDark ? darkModeRounded : lightModeRounded" class="control-btn-icon" />
    </button> -->

    <button class="control-btn" @click="openWin(openApiUrl)" title="openApi">
      <Icon :icon="api" class="control-btn-icon" />
    </button>

    <button class="control-btn" @click="openWin(helpUrl)" title="帮助文档">
      <Icon :icon="documentScannerOutline" class="control-btn-icon" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useFileStore } from "@/stores/modules/file";
import { useUserStore } from "@/stores/modules/user";
import NoticeComp from "@/components/NoticeComp/index.vue";
import CalendarComp from "@/components/CalendarComp/index.vue";
import DownloadDialog from "@/components/Download/index.vue";
// import { useGlobalStore } from "@/stores/modules/global";
import { Icon } from "@iconify/vue";
import notificationsRounded from "@iconify-icons/material-symbols/notifications-rounded";
import calendarMonthRounded from "@iconify-icons/material-symbols/calendar-month-rounded";
import downloadRounded from "@iconify-icons/material-symbols/download-rounded";
// import darkModeRounded from "@iconify-icons/material-symbols/dark-mode-rounded";
// import lightModeRounded from "@iconify-icons/material-symbols/light-mode";
import api from "@iconify-icons/mdi/api";
import documentScannerOutline from "@iconify-icons/material-symbols/document-scanner-outline";
// const globalStore = useGlobalStore();

const fileStore = useFileStore();
const userStore = useUserStore();

// 文件管理计数
const count = computed(() => fileStore.badge);
// 下载上传面板
const dialogdown: any = ref(null);
const openDownload = () => {
  dialogdown.value.openDialog();
};

// 主题状态
// const isDark = computed(() => globalStore.theme === "dark");
// 切换主题函数
// const toggleTheme = () => {
//   globalStore.toggleGlobalTheme();
// };

const openApiUrl = import.meta.env.VITE_OPENAPI_URL + "/index?pid=" + userStore.userInfo.pid;
const helpUrl = import.meta.env.VITE_HELP_URL;

const openWin = (url: string) => {
  const screenWidth = 1200;
  const screenHeight = 800;
  window.open(url, "_blank", "width=" + screenWidth + ",height=" + screenHeight + ",resizable=yes");
};
</script>

<style lang="scss" scoped>
.control-btn {
  @apply w-8 h-8 flex items-center justify-center rounded-lg transition-all duration-200 hover:scale-105 active:scale-95 cursor-pointer;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);

  .control-btn-icon {
    @apply w-5 h-5 text-gray-600 transition-colors duration-200;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(0, 0, 0, 0.1);
    box-shadow:
      0 2px 6px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);

    .control-btn-icon {
      @apply text-gray-800;
    }
  }

  &:active {
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.dark {
  .control-btn {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    .control-btn-icon {
      @apply text-gray-400;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.3);
      border-color: rgba(255, 255, 255, 0.15);
      box-shadow:
        0 2px 6px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);

      .control-btn-icon {
        @apply text-white;
      }
    }
  }
}
</style>
