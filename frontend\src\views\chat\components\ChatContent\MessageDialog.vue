<template>
  <el-dialog v-model="talkStore.isBolStatus" :show-close="false" class="custom-dialog" :close-on-click-modal="false">
    <template #header>
      <button class="close-btn" @click="cancelInfoSend">
        <font-awesome-icon :icon="['fas', 'times']" />
      </button>
      <div :value="title" class="flex items-center gap-2">
        <font-awesome-icon :icon="['fas', iconName]" class="icon" />
        <span class="title">{{ title }}</span>
        <LevelBtn :message="contentText"></LevelBtn>
      </div>
    </template>
    <div class="mt-1">
      <div class="h-[500px]" v-if="url">
        <iframe class="w-full h-full p-0 m-0 border-none" :src="url" frameborder="0"></iframe>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { onMounted } from "vue";
import { useTalkStore } from "@/stores/modules/talk";
import { useUserStore } from "@/stores/modules/user";
import LevelBtn from "@/components/LevelBtn/index.vue";
import axios from "axios"

const talkStore = useTalkStore();
const userStore = useUserStore();
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  iconName: {
    type: String,
    required: true
  },
  contentText: {
    type: String,
    required: true
  },
  url: {
    type: String,
    required: true
  },
  itemNum: {
    type: Number,
    required: true
  }
});

const emitInfo = defineEmits(["dia-close"]);
const cancelInfoSend = () => {
  emitInfo("dia-close", false);
};

onMounted(()=>{
  
})
</script>
<style scoped lang="scss">
.custom-checkbox {
  width: 350px;
  border-right: 1px solid #f2f2f2;

  .listper {
    display: flex;
    flex-direction: column;

    .el-checkbox {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }

    .el-checkbox:last-of-type {
      margin-right: 30px;
    }
  }
}
.rightbox {
  width: 300px;
}
</style>
