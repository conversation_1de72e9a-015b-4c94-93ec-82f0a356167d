// 发送消息code:200100/200200
export interface ContentChatInfo {
  content: {
    msg?: string;
    time?: string;
    secret?: number;
    quoteSenderName?: string;
    quoteContent?: string;
    quoteMessageFile?: object;
  };
}
// 参与投票code:200201
export interface ContentVoteInfo {
  content: {
    optionId:number;
    resData: string;
    secretLevel: number;
    timestamp: string;
    voteId: string;
  };
}
// 参与接龙code:200202
export interface ContentRelayInfo {
  content: {
    avatar?: string;
    content?: string;
    createTime?: Date;
    creator?: string;
    creatorId: string;
    creatorName?: string;
    id: number;
    relayId: string;
    secretLevel: number;
    time?: Date;
    timestamp: string;
    updateTime?: Date;
    updater?: string;
    eventType?: string;
  };
}
// 通知code:200300
export interface ContentNoticeInfo {
  content: {
    content: string;
    title: string;
    createTime: string;
  };
}
// 群组消息变更code:200204
export interface ContentModifyInfo {
  content: {
    groupId?: string;
    groupName?: string;
    timestamp: string;
    secretLevel: string;
    ids?: string;
    memberList?: Array<any>;
    id?:string;
    groupOwnerId?:string;
    groupOwnerName?:string;
  };
}
