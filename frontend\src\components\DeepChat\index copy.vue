<template>
  <div class="chat-wrapper">
    <!-- 聊天区域 -->
    <div class="chat-panel">
      <deep-chat
        ref="deepChatRef"
        :request="{ url: 'https://your-api.com/chat', method: 'POST' }"
        :file-upload="true"
        :avatars="{
          default: { styles: { position: 'left' } },
          ai: { src: aiAvatar }
        }"
        :text-input="{ placeholder: { text: 'Send a message' } }"
        :history="[
          { text: 'Hey, how are you?', role: 'user' },
          { text: 'I am doing great, thanks.', role: 'ai' },
          { text: 'What is the meaning of life?', role: 'user' },
          { text: 'Seeking fulfillment and personal growth.', role: 'ai' }
        ]"
        :connect="{ stream: true }"
        :demo="true"
        @message-added="onMessageAdded"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import "deep-chat";
import aiAvatar from "@/assets/images/logo.png";
import { ref, reactive, onMounted, watch } from "vue";

const deepChatRef = ref<HTMLElement | null>(null);

interface ChatMessage {
  role: string;
  text: string;
}

interface ChatSession {
  title: string;
  messages: ChatMessage[];
}

const chatHistory = reactive<ChatSession[]>([]);
let currentSession: ChatSession = { title: "", messages: [] };
let lastSavedHash = "";

const createNewSession = () => {
  if (currentSession.messages.length > 0) {
    saveCurrentSession();
  }
  currentSession = { title: "", messages: [] };
  (deepChatRef.value as any)?.clearMessages?.();
};

const loadSession = (index: number) => {
  const session = chatHistory[index];
  currentSession = { title: session.title, messages: [...session.messages] };

  lastSavedHash = generateSessionHash(currentSession); // ✅ 标记已加载的历史

  if (deepChatRef.value) {
    (deepChatRef.value as any).history = [...currentSession.messages];
  }
};

const onMessageAdded = (event: CustomEvent) => {
  const message = event.detail.message as ChatMessage;
  currentSession.messages.push(message);

  if (!currentSession.title && message.role === "user") {
    currentSession.title = message.text.slice(0, 10);
  }

  saveToLocalStorage();
};

const generateSessionHash = (session: ChatSession): string => {
  return JSON.stringify(session); // 简单 hash，用字符串比较即可
};

const saveCurrentSession = () => {
  const hash = generateSessionHash(currentSession);

  if (hash === lastSavedHash) return; // 🔒 已保存，不重复

  chatHistory.push({
    title: currentSession.title || `会话 ${chatHistory.length + 1}`,
    messages: [...currentSession.messages]
  });

  lastSavedHash = hash; // ✅ 更新保存的 hash
  saveToLocalStorage();
};

const saveToLocalStorage = () => {
  localStorage.setItem("chatSessions", JSON.stringify(chatHistory));
};

const loadFromLocalStorage = () => {
  const data = localStorage.getItem("chatSessions");
  if (data) {
    const parsed: ChatSession[] = JSON.parse(data);
    chatHistory.push(...parsed);
  }
};

onMounted(() => {
  loadFromLocalStorage();
  if (deepChatRef.value) {
    (deepChatRef.value as any).addEventListener("message", onMessageAdded);
  }
});
watch(
  () => chatHistory,
  _newVal => {
  },
  { deep: true }
);
</script>

<style scoped>
/* 发送按钮样式 */
.deep-chat-send-button {
  background-color: #1a73e8;
  color: #e41717;
  border: none;
  border-radius: 50%;
  width: 110px;
  height: 40px;
  margin-left: 8px;
  cursor: pointer;
}

.chat-wrapper {
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  height: 100vh; /* 占满整个视口高度 */
  background-color: transparent; /* 背景透明 */
}

.chat-panel {
  width: 100%;
  max-width: 350px; /* 限制聊天区域最大宽度 */
  padding: 1rem;
}

.custom-deep-chat {
  border-radius: 10px;
  border: 1px solid #e4e4e4;
  background: transparent; /* 背景透明 */
  transition: box-shadow 0.3s ease;
}

.custom-deep-chat:hover {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.user-message-text {
  color: rgb(255, 255, 255);
  background-color: rgb(0, 132, 255);
  margin-right: 0px;
  /* margin-left: auto; */
}
</style>
