<template>
  <el-dialog v-model="dialogVisible" :show-close="false" class="custom-dialog">
    <template #header>
      <button class="close-btn" @click="closeDialog">
        <font-awesome-icon :icon="['fas', 'times']" />
      </button>
      <div class="flex items-center gap-2">
        <font-awesome-icon :icon="['fas', 'cloud-download']" class="icon" />
        <span class="title">文件下载管理</span>
      </div>
    </template>
    <el-scrollbar height="400px">
      <div class="space-y-4">
        <div class="file-item rounded-lg border border-gray-200 p-4 shadow-sm"
          v-for="(item, index) in downloadList.slice(0, 20)" :key="item.file_id">
          <div class="mb-2 flex items-center justify-between">
            <div class="flex items-center">
              <div class="file-icon mr-2 flex size-10 items-center justify-center rounded-full">
                <fileAvatar v-if="!item.icon" :type-avatar="getFileExtensionWithDot(item.fileName)" />
                <img v-else :src="item.icon" class="not-img" alt="404" />
              </div>
              <div>
                <h5 class="title text-sm font-medium text-gray-800">{{ item.fileName }}</h5>
                <p v-if="item.state !== 'error'" class="text-xs">
                  {{ bytesToKB(item.receivedBytes) }} /
                  {{ bytesToKB(item.totalBytes) }}
                </p>
                <p v-if="item.progress !== 1" class="text-xs">速度: {{ formatSpeed(item.speed) }}/s</p>
              </div>
            </div>
            <span class="file-status rounded-full w-18 bg-green-100 px-3 py-0.5 text-xs font-medium text-green-800"
              :class="{
    'bg-green-100 text-green-800': item.progress == 1,
    'bg-yellow-100 text-yellow-800': item.paused,
    'bg-red-100 text-red-800': item.state === 'error',
    'bg-blue-100 text-blue-800': !(item.progress == 1 || item.paused || item.state === 'error')
  }">
              {{ item.progress == 1 ? "已完成" : item.paused ? "已暂停" : item.state === 'error' ? "下载失败" : '下载中' }}
            </span>
          </div>
          <div>
            <el-progress :text-inside="true" :stroke-width="16"
              :percentage="parseFloat((item.progress * 100).toFixed(0))" status="success">
            </el-progress>
          </div>
          <div class="flex space-x-2 mt-2">
            <el-button v-if="item.state !== 'error' && item.paused && item.progress !== 1" title="继续"
              @click="togglePause(index)" size="small">
              <font-awesome-icon :icon="['fas', 'play']" class="icon" />
            </el-button>
            <el-button v-if="item.state !== 'error' && !item.paused && item.progress !== 1" title="暂停"
              @click="togglePause(index)" size="small">
              <font-awesome-icon :icon="['fas', 'pause-circle']" class="icon" />
            </el-button>
            <el-button v-if="item.state !== 'error' && item.progress == 1" @click="openFile(item.path)" title="打开文件"
              size="small">
              <font-awesome-icon :icon="['fas', 'file-alt']" class="icon" />
            </el-button>
            <el-button v-if="item.state !== 'error' && item.progress == 1" @click="openFileLocation(item.path)"
              title="在文件夹中显示" size="small">
              <font-awesome-icon :icon="['fas', 'folder-open']" class="icon" />
            </el-button>
            <el-button @click="cancelDownload(item, index)" size="small" type="danger">移除</el-button>
          </div>
        </div>
        <!-- 显示提示信息 -->
        <div v-if="downloadList.length > 20" class="text-center text-gray-500 mt-4">最多显示20条记录</div>
      </div>
    </el-scrollbar>
    <!-- 标签页组件 -->
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref, computed } from "vue";
import { useFileStore } from "@/stores/modules/file";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { ipc, isEE } from "@/utils/ipcRenderer";
import fileAvatar from "@/components/Avatar/FileAvatar.vue";
import dayjs from "dayjs";
// 获取store实例
const fileStore = useFileStore();

const dialogVisible = ref(false);
const openDialog = () => {
  dialogVisible.value = true;
  fileStore.setBadge();
};
const closeDialog = () => {
  dialogVisible.value = false;
};
defineExpose({ openDialog, closeDialog });
const downloadList = computed(() => {
  return fileStore.downloadList || [];
});
const getFileExtensionWithDot = (filename: any) => {
  // 使用 lastIndexOf 找到最后一个点的位置
  const dotIndex = filename.lastIndexOf('.');
  // 如果找到了点并且它不是字符串的第一个字符，返回从该位置到字符串末尾的部分
  if (dotIndex > 0 && dotIndex < filename.length - 1) {
    return filename.substring(dotIndex);
  }
  // 如果没有找到点或者点在字符串的最后一个字符位置，返回空字符串
  return '';
}
const formatSpeed = (speed: any) => {
  if (speed < 1024) {
    return `${speed} B`;
  } else if (speed < 1024 * 1024) {
    return `${(speed / 1024).toFixed(2)} KB`;
  } else {
    return `${(speed / (1024 * 1024)).toFixed(2)} MB`;
  }
};
const togglePause = (_index: number) => {
  //ipc.invoke('pause-download', id);
  //downloadList.value[index].paused = !downloadList.value[index].paused;
};
const cancelDownload = (item: any, index: number) => {
  if (isEE) {
    ipc.invoke(ipcApiRoute.delDownloadFileById, item.file_id);
  }
  downloadList.value.splice(index, 1);
};
const openFile = (path: any) => {
  ipc.invoke(ipcApiRoute.openDirectory, path);
};
const openFileLocation = (filePath: any) => {
  const path = require("path");
  const folderPath = path.dirname(filePath);
  ipc.invoke(ipcApiRoute.openDirectory, folderPath);
};
const bytesToKB = (bytes: any) => {
  if (bytes === 0) return "0 KB";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};
</script>

<style lang="scss" scoped>
.dark .title {
  @apply text-white;
}

.dark {
  .custom-dialog {
    .shadow-sm {
      @apply border-2 border-[#374151] opacity-75;
    }
  }

  .file-item {
    @apply border-gray-700 bg-gray-800;
  }

  .file-icon {
    @apply bg-blue-900;
  }

  .file-status {
    @apply bg-green-900 text-green-300;
  }
}
</style>
