<template>
  <div class="px-4">
    <el-button size="small" @click="viewNotice">一键已读</el-button>
    <el-table :data="notifications" style="width: 100%" max-height="600">
      <el-table-column label="标题">
        <template #default="scope">
          {{ isDeptNotice(scope.row) ? scope.row.title : approveType[scope.row.title] }}
        </template>
      </el-table-column>
      <el-table-column prop="content" label="内容">
        <template #default="scope">
          <div v-if="isDeptNotice(scope.row)" v-html="scope.row.msg || scope.row.content"></div>
        </template>
      </el-table-column>
      <el-table-column label="发布时间">
        <template #default="scope">
          {{ dayjs(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { useNoticeStore } from "@/stores/modules/notices";
import dayjs from "dayjs";

const noticeStore = useNoticeStore();

const approveType: any = {
  "300": "创建群组",
  "301": "增加成员",
  "302": "删除成员"
};

const notifications = computed(() => noticeStore.noticeList);

const isDeptNotice = ({ noteType }: any) => noteType === "1";

const viewNotice = () => {
  noticeStore.clearNoticeCount();
};
</script>

<style scoped>
.el-button {
  @apply bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded;
}
</style>
