<template>
  <div class="contact space-y-2">
    <div v-if="user?.length == 0">
      <NoData />
    </div>
    <div
      v-for="item in user as any"
      :key="item.id"
      class="group-item relative flex items-center gap-3 rounded-lg p-2 hover:bg-gray-100 cursor-pointer"
      @click="openInfo(item)"
    >
      <div v-if="item.secret && item.secret < 40" class="h-full secret-bar-green"></div>
      <div v-else-if="item.secret && item.secret < 60" class="h-full secret-bar-yellow"></div>
      <div v-else-if="item.secret" class="h-full secret-bar-orange"></div>
      <div @click.stop>
        <DynamicAvatar
          :id="item.id"
          :data-info="item"
          :relation-name="item.groupName"
          :type-avatar="item.groupType == 0 ? 1 : 2"
        />
      </div>
      <div class="flex-1 flex flex-col group-info">
        <h4 class="text-sm font-medium text-gray-900">{{ item.groupName }}</h4>
        <p class="text-xs text-gray-500">{{ item.memberNum }} 成员</p>
        <!-- <LevelBtn :message="item.secret"></LevelBtn> -->
      </div>
      <div class="flex items-center space-x-4" @click.stop>
        <el-tooltip content="发送消息" placement="top">
          <el-button link icon="chat-dot-round" @click="openChat(item)"></el-button>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="GroupContact">
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import { useRecentStore } from "@/stores/modules/recent";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import NoData from "@/components/NoData/index.vue";

const userStore = useUserStore();
const talkStore = useTalkStore();
const recentStore = useRecentStore();

const props = defineProps({
  user: {
    type: Array
  },
  ifSearch: {
    type: Boolean
  }
});

const emit = defineEmits(["info-click", "search-click"]);
const openInfo = (item: any) => {
  if (props.ifSearch) {
    openChat(item);
  } else {
    emit("info-click", item.id);
  }
};

const openChat = async (data: any) => {
  emit("search-click");
  const params = {
    senderId: userStore.userId,
    contactId: data.id,
    chatType: data.groupType + 1,
    avatar: null,
    contactName: data.groupName,
    secret: data.secret
  };
  const res = await recentStore.addListRecents(params);
  if (res) {
    talkStore.setActiveChat(data.id);
    talkStore.ifChat = true;
    talkStore.ifContact = false;
  }
};
</script>

<style scoped lang="scss">
.el-button {
  @apply text-xl;
}
.book {
  @apply p-2;
}
.dark {
  .group-item {
    @apply hover:bg-gray-700;
  }
  h4 {
    @apply text-white;
  }
  p {
    @apply text-gray-400;
  }
}
.secret-bar-green {
  position: absolute;
  top: 0;
  width: 5px;
  left: -9px;
  background: linear-gradient(180deg, rgba(34, 197, 94, 0) 0%, rgba(34, 197, 94, 0.3) 50%, rgba(34, 197, 94, 0) 100%);
}

.secret-bar-yellow {
  position: absolute;
  top: 0;
  width: 5px;
  left: -9px;
  background: linear-gradient(180deg, rgba(234, 179, 8, 0) 0%, rgba(234, 179, 8, 0.3) 50%, rgba(234, 179, 8, 0) 100%);
}

.secret-bar-orange {
  position: absolute;
  top: 0;
  width: 5px;
  left: -9px;
  background: linear-gradient(180deg, rgba(154, 52, 18, 0) 0%, rgba(154, 52, 18, 0.3) 50%, rgba(154, 52, 18, 0) 100%);
}
</style>
