import { defineStore } from "pinia";
import { GlobalState, ThemeType } from "../interface";
import { DEFAULT_PRIMARY } from "@/config";
import piniaPersistConfig from "../helper/persist";
// import { ipc, isEE } from "@/utils/ipcRenderer";
// import { ipcApiRoute } from "@/ipc/ipcApi";

export const useGlobalStore = defineStore("lark-global", {
  state: (): GlobalState => ({
    // 布局模式 (纵向：vertical | 经典：classic | 横向：transverse | 分栏：columns)
    layout: "vertical",
    // element 组件大小
    assemblySize: "default",
    // 当前页面是否全屏
    maximize: false,
    // 主题颜色
    primary: DEFAULT_PRIMARY,
    // 标签页
    tabs: true,
    // 标签页图标
    tabsIcon: true
  }),
  getters: {},
  actions: {
    // Set GlobalState
    setGlobalState(...args: ObjToKeyValArray<GlobalState>) {
      this.$patch({ [args[0]]: args[1] });
    }
  },
  persist: piniaPersistConfig("lark-global")
});
