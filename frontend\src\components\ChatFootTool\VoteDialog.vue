<template>
  <div class="dialog-box">
    <el-dialog v-model="visible" :show-close="false" :close-on-click-modal="false" class="custom-dialog">
      <template #header>
        <button class="close-btn" @click="closeDialog">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'poll']" class="icon" />
          <span class="title">投票</span>
        </div>
      </template>
      <div>
        <!-- <h4>{{ voteData.title }}</h4> -->
        <el-form :model="formData" :rules="rules" require-asterisk-position="right" ref="ruleForm">
          <el-form-item label="投票名称" prop="title">
            <el-input v-model="formData.title" placeholder="请输入投票名称"></el-input>
          </el-form-item>
          <el-form-item label="投票描述" prop="description">
            <el-input v-model="formData.description" type="textarea" placeholder="请输入描述信息"></el-input>
          </el-form-item>
          <div class="flex">
            <el-form-item label="投票创建日期" prop="createdAt" class="mr-5">
              <el-date-picker
                v-model="formData.createdAt"
                type="datetime"
                placeholder="投票创建日期"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="投票截止日期" prop="deadline">
              <el-date-picker
                v-model="formData.deadline"
                type="datetime"
                placeholder="投票截止日期"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              ></el-date-picker>
            </el-form-item>
          </div>
          <div v-for="(option, index) in formData.text" :key="index">
            <el-form-item label="投票选项" :prop="'text.' + index">
              <el-input v-model="formData.text[index]" placeholder="请输入投票选项"></el-input>
              <el-button type="primary" v-if="index === formData.text.length - 1" @click="addOption" class="ml-2">+</el-button>
              <el-button type="danger" v-else @click="delOption(index)" class="ml-2">-</el-button>
            </el-form-item>
          </div>
        </el-form>
        <div class="bottom-0 right-0">
          <el-button type="primary" :loading="voteLoading" @click="submitForm">提交</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, watchEffect } from "vue";
import { useTalkStore } from "@/stores/modules/talk";
import { ElMessage, dayjs } from "element-plus";
import { getVoteCreate } from "@/api/modules/contact";

const talkStore = useTalkStore();
const visible = ref(false);
const voteLoading = ref(false);
const formData = ref({
  title: "",
  description: "",
  deadline: "",
  createdAt: "",
  text: <any>[""],
  businessId: ""
});

watchEffect(() => {
  formData.value.businessId = talkStore.activeChatId;
});

const checkDeadline = (_rule: any, value: any, callback: any) => {
  if (formData.value.createdAt && dayjs(value).valueOf() <= dayjs(formData.value.createdAt).valueOf()) {
    return callback(new Error("投票截止日期必须晚于创建日期"));
  }

  callback();
};

// 验证规则
const rules = {
  title: [{ required: true, message: "请输入投票名称", trigger: "blur" }],
  description: [{ required: true, message: "请输入描述信息", trigger: "blur" }],
  createdAt: [{ required: true, message: "请选择投票创建日期", trigger: "change" }],
  deadline: [
    { required: true, message: "请选择投票截止日期", trigger: "change" },
    { validator: checkDeadline, trigger: "change" }
  ],
  text: [{ required: true, message: "请输入投票选项", trigger: "blur" }]
};

const emit = defineEmits(["send-message"]);

// 表单实例
const ruleForm: any = ref(null);
// 添加选项
const addOption = () => {
  formData.value.text.push("");
};
const delOption = (index: number) => {
  formData.value.text.splice(index, 1);
};
const submitForm = async () => {
  if (formData.value.text.length < 2) {
    ElMessage.error("投票选项不能少于2条!");
    return;
  }
  ruleForm.value.validate(async (valid: any) => {
    if (valid) {
      voteLoading.value = true;
      try {
        const res: any = await getVoteCreate(formData.value);
        if (res.code == 0) {
          ElMessage.success("创建成功");
          emit("send-message", res.data, 4);
          closeDialog();
        }
      } finally {
        voteLoading.value = false;
      }
    } else {
      ElMessage.error("请检查表单项!");
      return false;
    }
  });
};

watch(visible, newVal => {
  if (!newVal && ruleForm.value) {
    ruleForm.value.resetFields();
  }
});

const openDialog = () => {
  visible.value = true;
};
const closeDialog = () => {
  visible.value = false;
};
defineExpose({ openDialog, closeDialog });
</script>

<style lang="scss" scoped>
.el-form-item {
  @apply block;

  :deep(.el-form-item__content) {
    flex-wrap: nowrap;
  }
}
</style>
