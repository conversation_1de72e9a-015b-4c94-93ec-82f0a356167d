<template>
  <div class="tree">
    <el-tree class="custom-tree" :data="list" :props="defaultProps" node-key="id" @node-click="handleNodeClick">
      <template #default="{ data }">
        <div class="flex-1 flex justify-between items-center friend-group">
          <span>{{ data.groupName }}</span>
          <span v-if="data.id == 'default'" class="flex items-center hidden actions">
            <el-button link icon="circle-plus" @click.stop="openDialog(1, {})"></el-button>
          </span>
          <span
            v-if="data.id != 'default' && data.id != 'friend' && data.id != 'newFriend'"
            class="flex items-center hidden actions"
            click.stop
          >
            <el-button link icon="edit" @click="openDialog(2, data)"></el-button>
            <el-button link icon="delete" @click="openDialog(3, data)"></el-button>
          </span>
        </div>
      </template>
    </el-tree>
    <el-dialog v-model="dialogVisible" :show-close="false" class="custom-dialog need-foot">
      <template #header>
        <button class="close-btn" @click="dialogVisible = false">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'calendar']" class="icon" />
          <span class="title">{{ dialogInfo.title }}</span>
        </div>
      </template>
      <template #default>
        <div v-if="dialogInfo.type == 1">
          <el-input v-model="dialogInfo.groupName" placeholder="请输入好友分组名称"></el-input>
        </div>
        <div v-else-if="dialogInfo.type == 2">
          <el-input v-model="dialogInfo.groupName"></el-input>
        </div>
        <div v-else>确定删除好友分组 {{ dialogInfo.data.groupName }}</div>
      </template>
      <template #footer>
        <el-button type="default" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSure">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="FriendTree">
import { ref, onMounted, computed } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { useFriendStore } from "@/stores/modules/friends";
import { addFriendGroup, updateFriendGroup, deleteFriendGroup } from "@/api/modules/contact";

const userStore = useUserStore();
const friendStore = useFriendStore();

const props = defineProps({
  ifAction: {
    type: Boolean
  }
});

onMounted(async () => {
  await friendStore.getFriendGroup({ userId: userStore.userId });
  await friendStore.getAllFriend({ userId: userStore.userId });
});

const list = computed(() => {
  if (props.ifAction) {
    return [{ id: "newFriend", groupName: "新的好友" }, ...friendStore.groupTree];
  } else {
    return friendStore.groupTree;
  }
});

const defaultProps = {
  label: "groupName",
  children: "children",
  value: "id"
};

const dialogVisible = ref(false);
const dialogInfo: any = ref({
  type: 0,
  data: {},
  groupName: "",
  title: ""
});
const openDialog = (num: number, data: any) => {
  dialogVisible.value = true;
  dialogInfo.value.type = num;
  dialogInfo.value.data = data;
  dialogInfo.value.groupName = data.groupName;
  if (num == 1) {
    dialogInfo.value.title = "增加分组";
  } else if (num == 2) {
    dialogInfo.value.title = "修改分组";
  } else {
    dialogInfo.value.title = "删除分组";
  }
};

const emit = defineEmits(["node-click"]);
const handleNodeClick = (data: any) => {
  emit("node-click", data.id);
};

const addGroup = async () => {
  const params = {
    userId: userStore.userId,
    groupName: dialogInfo.value.groupName
  };
  const res: any = await addFriendGroup(params);
  if (res.code == 0) {
    dialogVisible.value = false;
    friendStore.addFriendGroup({
      id: res.data,
      groupName: params.groupName
    });
  }
};

const updateGroup = async () => {
  const params = {
    id: dialogInfo.value.data.id,
    groupName: dialogInfo.value.data.groupName
  };
  const res: any = await updateFriendGroup(params);
  if (res.code == 0) {
    dialogVisible.value = false;
    friendStore.updateFriendGroup(dialogInfo.value.data.id, dialogInfo.value.groupName);
  }
};

const deleteGroup = async () => {
  const res: any = await deleteFriendGroup(dialogInfo.value.data.id);
  if (res.code == 0) {
    dialogVisible.value = false;
    friendStore.deleteFriendGroup(dialogInfo.value.data.id);
  }
};

const handleSure = () => {
  if (dialogInfo.value.type == 1) {
    addGroup();
  } else if (dialogInfo.value.type == 2) {
    updateGroup();
  } else {
    deleteGroup();
  }
};
</script>

<style scoped lang="scss">
.address-book .friend-group:hover {
  .actions {
    @apply block;
  }
}
</style>
