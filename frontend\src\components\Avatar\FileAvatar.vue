<template>
  <div class="file-wrapper">
    <font-awesome-icon :icon="['fas', !imgSrc?'file':imgSrc]" class="control-btn-icon" />
  </div>
</template>

<script setup lang="ts">
import { computed, PropType } from "vue";

const props = defineProps({
  typeAvatar: {
    type: String,
  },
  typeAvatarMap: {
    type: Object as PropType<Record<string, string>>,
    default: () => ({
      '.txt': 'file-lines',//文本
      '.html': 'file-code',
      '.js': 'file-code',
      '.css': 'file-code',
      '.ts': 'file-code',
      '.log': 'file-code',
      '.md': 'file-code',
      '.cpp': 'file-code',
      '.c': 'file-code',
      '.java': 'file-code',
      '.py': 'file-code',
      '.xml': 'file-code',
      '.json': 'file-code',
      '.dll': 'file-lines',//系统文件
      '.exe': 'file-lines',
      '.sys': 'file-lines',
      '.reg': 'file-lines',
      '.gz': 'file-zipper',//压缩包
      '.tar': 'file-zipper',
      '.7z': 'file-zipper',
      '.rar': 'file-zipper',
      '.zip': 'file-zipper',
      '.360': 'file-zipper',
      '.pdf': 'file-pdf',//pdf
      '.ppt': 'file-powerpoint',//ppt
      '.pptx': 'file-powerpoint',
      '.doc': 'file-word', //word
      '.docx': 'file-word', 
      '.xls': 'file-excel', //excel
      '.xlsx': 'file-excel', 
      '.csv': 'file-csv', 
      '.mp4': 'file-video', //视频
      '.avi': 'file-video',
      '.mkv': 'file-video',
      '.flv': 'file-video',
      '.wmv': 'file-video',
      '.mov': 'file-video',
      '.mpg': 'file-video',
      '.mpeg': 'file-video',
      '.hevc': 'file-video',
      '.h.265': 'file-video',
      '.wav': 'file-audio',//音频
      '.mp3': 'file-audio',
      '.flac': 'file-audio',
      '.aac': 'file-audio',
      '.wma': 'file-audio',
      '.dts': 'file-audio',
      '.ac3': 'file-audio',
      '.aiff': 'file-audio',
      '.aiff-c': 'file-audio',
      '.png': 'file-image', //图片
      '.jpg': 'file-image', 
      '.jpeg': 'file-image',
      '.gif': 'file-image',
      '.svg': 'file-image',
      '.webp': 'file-image',
      '.bmp': 'file-image',
      '.raw': 'file-image',
    })
  },
});


// 
const imgSrc = computed(() => {
  const type = props.typeAvatar;
  if (type && props.typeAvatarMap[type]) {
    return props.typeAvatarMap[type];
  }
  return "";
});
</script>

<style lang="scss" scoped>
.file-wrapper {
  @apply flex p-1;
  .control-btn-icon {
    @apply w-8 h-8 text-xl text-gray-600 hover:text-gray-600;
  }
}
.dark {
  .control-btn-icon {
    @apply text-gray-400 hover:text-gray-400;
  }
}
</style>
