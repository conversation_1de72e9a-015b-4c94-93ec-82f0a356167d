<template>
  <div class="drawer-box">
    <slot :open="openDrawer"></slot>
    <el-drawer v-model="visible" size="400px" @close="closeDrawer" class="custom-drawer">
      <template #header>
        <span>群组信息</span>
      </template>
      <template #default>
        <GroupInfo @owner="openDialog()" @close-drawer="closeDrawer"></GroupInfo>
      </template>
    </el-drawer>
    <el-dialog v-model="dialogVisible" :show-close="false" class="custom-dialog need-foot">
      <template #header>
        <button class="close-btn" @click="closeDialog">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'users']" class="icon" />
          <span class="title">变更群主</span>
        </div>
      </template>
      <template #default>
        <div class="m-auto p-4 border rounded-lg">
          <el-scrollbar height="300">
            <el-radio-group v-model="newLeader">
              <el-radio v-for="i in groupMembers" :label="i.member" :key="i.member" :disabled="i.member == userStore.userId">
                <DynamicAvatar :id="i.member" :data-info="i" :relation-name="i.memberName" :size="32" :typeAvatar="0" />
                <span class="ml-2">{{ i.memberName }}</span>
                <span class="ml-2">{{ i.pathName }}</span>
              </el-radio>
            </el-radio-group>
          </el-scrollbar>
        </div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import * as contactApi from "@/api/modules/contact";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import { useContactStore } from "@/stores/modules/friend";
import GroupInfo from "./item/GroupInfo.vue";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import { ElMessage } from "element-plus";
import WebSocketClient from "@/utils/websocket/websocketClient";
import { MessageCode, ContentType } from "@/utils/websocket/messageInfo";
import { sendMessage } from "@/utils/websocket/messageService";
const userStore = useUserStore();
const talkStore = useTalkStore();
const contactStore = useContactStore();

const groupMembers = computed(() => contactStore.groupMembers[talkStore.activeChatId] || []);
const groupInfo = computed(() => contactStore.groupInfo[talkStore.activeChatId] || {});

const visible = ref(false);
const openDrawer = () => {
  visible.value = true;
};
const closeDrawer = () => {
  visible.value = false;
};

const dialogVisible = ref(false);
const openDialog = () => {
  dialogVisible.value = true;
};
const closeDialog = () => {
  dialogVisible.value = false;
};

const webSocketManager: any = WebSocketClient.getInstance();
// 变更群主
const newLeader = ref("");
const handleConfirm = async () => {
  dialogVisible.value = false;
  let params: any = {
    groupId: groupInfo.value.id,
    groupOwnerId: newLeader.value
  };
  let [owner] = groupMembers.value.filter((item: any) => item.member == newLeader.value);
  const res: any = await contactApi.changeGroupOwner(params);
  if (res.code == 0) {
    ElMessage.success("操作成功");
    try {
      await sendMessage(webSocketManager!, {
        code: MessageCode.GROUP_MODIFY,
        receiverId: talkStore.activeChatId,
        isGroup: true,
        contentType: ContentType.GROUP_MODIFY_OWNER,
        content: {
          ...params,
          groupOwnerName: owner.memberName
        }
      });
    } catch (error: any) {
      ElMessage.error(error.message);
    }
  }
};
</script>

<style lang="scss" scoped>
.drawer-box {
  :deep(.el-drawer) {
    .el-button {
      @apply flex-auto;
    }
  }

  .el-radio-group {
    @apply flex-col items-start;

    :deep(.el-radio) {
      @apply flex items-center mb-2 h-auto;

      .el-radio__label {
        @apply flex items-center;
      }
    }
  }
}
</style>
