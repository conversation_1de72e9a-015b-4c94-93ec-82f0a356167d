import request from "@/api";
import { ApiResultData } from "@/api/interface";
import axios from "axios";

// 创建专门用于Dify API的axios实例
const difyRequest = axios.create({
  baseURL: import.meta.env.MODE === 'production' 
    ? '/api/dify/v1'  // 生产环境使用nginx代理路径
    : (import.meta.env.VITE_DIFY_URL || 'http://localhost:8000/v1'), // 开发环境直连Dify服务
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Dify API请求拦截器 - 开发环境添加API Key认证，生产环境由nginx处理
difyRequest.interceptors.request.use(
  (config) => {
    // 开发环境需要添加API Key，生产环境由nginx代理处理
    if (import.meta.env.MODE !== 'production') {
      const apiKey = import.meta.env.VITE_DIFY_API_KEY || '';
      if (apiKey) {
        config.headers.Authorization = `Bearer ${apiKey}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Dify API响应拦截器
difyRequest.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('Dify API调用失败:', error);
    return Promise.reject(error);
  }
);

// Dify API 相关接口类型定义
export interface DifyCompletionRequest {
  inputs: Record<string, any>;
  response_mode: "streaming" | "blocking";
  user: string;
  query?: string;
  conversation_id?: string;
}

export interface DifyChatRequest extends DifyCompletionRequest {
  query: string;
  conversation_id?: string;
  files?: {
    type: string;
    transfer_method: string;
    url?: string;
    upload_file_id?: string;
  }[];
}

export interface DifyResponse<T = any> {
  event: string;
  conversation_id: string;
  message_id: string;
  created_at: number;
  task_id: string;
  id: string;
  answer: string;
  data?: T;
}

export interface DifyWorkflowRequest {
  inputs: Record<string, any>;
  response_mode: "streaming" | "blocking";
  user: string;
}

// Dify 文本生成接口 - 用于completion类型应用
export const difyCompletion = async (params: DifyCompletionRequest) => {
  return await difyRequest.post("/completion-messages", params);
};

// Dify 对话接口 - 用于chat类型应用
export const difyChat = async (params: DifyChatRequest) => {
  return await difyRequest.post("/chat-messages", params);
};

// Dify 工作流接口 - 用于workflow类型应用
export const difyWorkflow = async (params: DifyWorkflowRequest) => {
  return await difyRequest.post("/workflows/run", params);
};

// 获取会话历史
export const getDifyConversations = async (params?: {
  user: string;
  last_id?: string;
  limit?: number;
}) => {
  return await difyRequest.get("/conversations", { params });
};

// 获取会话消息
export const getDifyMessages = async (conversationId: string, params?: {
  user: string;
  first_id?: string;
  limit?: number;
}) => {
  return await difyRequest.get(`/messages/${conversationId}`, { params });
};

// 删除会话
export const deleteDifyConversation = async (conversationId: string, params: { user: string }) => {
  return await difyRequest.delete(`/conversations/${conversationId}`, { params });
};

// 重命名会话
export const renameDifyConversation = async (conversationId: string, params: {
  name: string;
  user: string;
}) => {
  return await difyRequest.post(`/conversations/${conversationId}/name`, params);
};

// 获取应用参数
export const getDifyParameters = async (params: { user: string }) => {
  return await difyRequest.get("/parameters", { params });
};

// 文件上传接口
export const uploadDifyFile = async (file: File, params: { user: string }) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('user', params.user);

  return await difyRequest.post("/files/upload", formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// 语音转文字接口
export const difyAudioToText = async (file: File, params: { user: string }) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('user', params.user);

  return await difyRequest.post("/audio-to-text", formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// 文字转语音接口
export const difyTextToAudio = async (params: {
  message_id: string;
  text: string;
  user: string;
  voice?: string;
}) => {
  return await difyRequest.post("/text-to-audio", params, {
    responseType: 'blob'
  });
};

// 获取应用信息
export const getDifyAppInfo = async (params: { user: string }) => {
  return await difyRequest.get("/info", { params });
};

// 反馈消息接口
export const difyMessageFeedback = async (messageId: string, params: {
  rating: "like" | "dislike";
  user: string;
}) => {
  return await difyRequest.post(`/messages/${messageId}/feedbacks`, params);
};

// 建议问题接口
export const getDifySuggestedQuestions = async (messageId: string, params: { user: string }) => {
  return await difyRequest.get(`/messages/${messageId}/suggested`, { params });
};
