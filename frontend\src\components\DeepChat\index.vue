<template>
  <div class="app-container">
    <DeepChatContainer
      class="no-drag"
      :demo="true"
      :text-input="{ placeholder: { text: 'Welcome to the demo!' } }"
      :history="history"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import "deep-chat";
import DeepChatContainer from "./DeepChatContainer.vue";

const history = ref([
  { role: "user", text: "Hey, how are you today?" },
  { role: "ai", text: "I am doing very well!" }
]);
</script>

<style scoped>
.app-container {
  font-family: sans-serif;
  /* text-align: center;
  justify-content: center;
  display: grid; */
  width: 100%;
  height: 100%;
  padding: 20px 10px 10px;
  background-color: #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  -webkit-app-region: drag;
}
.no-drag {
  -webkit-app-region: no-drag;
}
</style>
