# 数据统计组件使用说明

## 概述

`DataStatsWidget.vue` 是一个用于显示邮件数量和计划数量的统计卡片组件。该组件集成在首页的仪表板中，为用户提供快速的数据概览。

## 功能特性

### 📧 邮件统计卡片
- **总邮件数量**: 显示所有邮件数量
- **今日邮件**: 显示当天收到的邮件数量 
- **未读提醒**: 红色徽章显示未读邮件数量
- **点击交互**: 点击卡片可跳转到邮件详情页面

### 📅 计划统计卡片
- **总计划数量**: 显示所有待办计划总数
- **完成进度**: 以进度条和百分比形式展示完成率
- **待办提醒**: 显示待完成的计划数量
- **点击交互**: 点击卡片可跳转到计划详情页面

### 🎨 界面特性
- **响应式设计**: 自适应不同屏幕尺寸
- **暗色主题支持**: 完整的暗色模式适配
- **加载状态**: 带动画的加载指示器
- **悬停效果**: 卡片悬停时的视觉反馈
- **手动刷新**: 提供刷新按钮更新数据

## 数据源

### 邮件数据
- 基于 `useRecentStore` 中的最近联系人数据
- 未读数量通过 `unreadNum` 字段统计
- 今日邮件数量为模拟数据（可根据实际API调整）

### 计划数据
- 通过 `getTodosList` API 获取待办事项数据
- 自动计算完成数量和待办数量
- 实时计算完成百分比

## 技术实现

### 核心技术栈
- **Vue 3** - 组合式API
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **Iconify** - 图标系统

### 主要方法
```typescript
// 刷新统计数据
refreshStats(): Promise<void>

// 获取邮件统计
getEmailStats(): Promise<void>

// 获取计划统计 
getPlanStats(): Promise<void>

// 邮件卡片点击处理
handleEmailClick(): void

// 计划卡片点击处理
handlePlanClick(): void
```

### 响应式数据
```typescript
interface EmailStats {
  total: number    // 总邮件数
  today: number    // 今日邮件数
  unread: number   // 未读邮件数
}

interface PlanStats {
  total: number     // 总计划数
  completed: number // 已完成数
  pending: number   // 待办数
}
```

## 使用方式

该组件已集成在首页仪表板中，无需额外配置。组件会在挂载时自动加载数据，并提供手动刷新功能。

### 自定义点击行为

如需修改卡片点击后的跳转行为，可编辑以下方法：

```typescript
const handleEmailClick = () => {
  // 自定义邮件卡片点击逻辑
  // 例如：router.push('/email')
}

const handlePlanClick = () => {
  // 自定义计划卡片点击逻辑
  // 例如：router.push('/todos')
}
```

## 样式定制

组件使用 Tailwind CSS 和 CSS 变量，支持主题定制：

```css
.stats-card {
  /* 卡片悬停效果 */
  transform: translateY(0);
  transition: all 0.2s ease-in-out;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}
```

## 扩展建议

1. **实时更新**: 集成 WebSocket 实现数据实时更新
2. **更多统计**: 添加更多数据维度（周统计、月统计等）
3. **图表展示**: 集成图表库显示趋势数据
4. **导出功能**: 添加数据导出功能
5. **筛选器**: 添加时间范围筛选器

## 注意事项

- 组件依赖于项目的数据存储和API结构
- 邮件数据目前基于聊天数据模拟，建议根据实际邮件API调整
- 确保在使用前已正确配置相关的数据存储和API
