<template>
  <div class="p-4">
    <div class="flex items-center space-x-2 mb-4">
      <el-input style="width: 400px" v-model="searchTitle" placeholder="请输入搜索内容" clearable></el-input>
    </div>

    <el-table :data="approveData" class="table-list" max-height="600">
      <el-table-column prop="groupName" label="群组名称"></el-table-column>
      <el-table-column label="类型">
        <template #default="scope">
          {{ approveType[scope.row.approveType] }}
        </template>
      </el-table-column>
      <el-table-column prop="creatorName" label="申请人"></el-table-column>
      <el-table-column label="申请时间" min-width="200">
        <template #default="scope">
          {{ dayjs(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="240">
        <template #default="scope">
          <el-button type="default" size="small" @click="showDetail(scope.row)">详情</el-button>
          <el-button type="primary" size="small" v-if="scope.row.approveStatus == '0'" @click="approveFc(scope.row.id, 1)">
            通过
          </el-button>
          <el-button type="danger" size="small" v-if="scope.row.approveStatus == '0'" @click="approveFc(scope.row.id, 2)">
            不通过
          </el-button>
          <el-tag type="success" v-if="scope.row.approveStatus == 1" class="ml-3">通过</el-tag>
          <el-tag type="danger" v-if="scope.row.approveStatus == 2" class="ml-3">不通过</el-tag>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="dialogVisible" title="审批详情">
      <div>群组名称: {{ selectedApproval.groupName }}</div>
      <div>群组密级: {{ SecretLevelConverter.getLevelInfo(selectedApproval.groupLevel, "obj").label }}</div>
      <div>审批类型: {{ approveType[selectedApproval.approveType] }}</div>
      <div>申请人: {{ selectedApproval.creatorName }}</div>
      <div>涉及人员: {{ selectedApproval.members }}</div>
      <div>申请时间: {{ dayjs(selectedApproval.createTime).format("YYYY-MM-DD HH:mm:ss") }}</div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { updateGroupApprove } from "@/api/modules/notices";
import dayjs from "dayjs";
import { useNoticeStore } from "@/stores/modules/notices";
import { SecretLevelConverter } from "@/utils/secretLevelConverter";

const noticeStore = useNoticeStore();

const approveType: any = {
  "300": "创建群组",
  "301": "增加成员",
  "302": "删除成员"
};
const searchTitle = ref("");
const dialogVisible = ref(false);
const selectedApproval: any = ref({});

const approveData = computed(() => {
  if (searchTitle) {
    return noticeStore.approveList.filter(
      (item: any) =>
        item.groupName.includes(searchTitle.value) ||
        item.creatorName.includes(searchTitle.value) ||
        approveType[item.approveType].includes(searchTitle.value)
    );
  } else {
    return noticeStore.approveList;
  }
});

const approveFc = async (id: string, flag: number) => {
  const params = {
    id: id,
    approveFlg: flag
  };
  const res: any = await updateGroupApprove(params);
  if (res.code == 0) {
    await noticeStore.getApproveList();
  }
};

const showDetail = (approval: any) => {
  approval.members = JSON.parse(approval.groupMembers)
    .map((item: any) => item.userName)
    .join("，");
  selectedApproval.value = approval;
  dialogVisible.value = true;
};
</script>

<style scoped lang="scss">
.table-list {
  :deep(.el-scrollbar) {
    height: calc(100vh - 300px);
  }

  .el-button {
    @apply w-16;
  }
}
</style>
