import { defineStore } from "pinia";
import { NoticesState } from "../interface";
import piniaPersistConfig from "../helper/persist";
import { ipc } from "@/utils/ipcRenderer";
import { useUserStore } from "./user";
import * as noticeApi from "@/api/modules/notices";

const userStore = useUserStore();

export const useNoticeStore = defineStore("lark-notices", {
  state: (): NoticesState => ({
    noticeList: [],
    noticeCount: 0,
    approveList: [],
    approveCount: 0
  }),
  getters: {},
  actions: {
    // 获取系统通知
    async getDefaultNotices(params?: any) {
      const res: any = await noticeApi.getNoticesList(params);
      if (res.code == 0) {
        this.noticeList = res.data.list;
        this.noticeList.forEach((item: any) => {
          if (item.status) {
            this.noticeCount++;
          }
        });
      }
    },
    // 获取审批通知
    async getApproveList() {
      const params = {
        pageNo: 1,
        pageSize: -1,
        userId: userStore.userId
      };
      const res: any = await noticeApi.getGroupApproveList(params);
      if (res.code == 0) {
        this.approveList = res.data.list;
        this.approveCount = 0;
        this.approveList.forEach((item: any) => {
          if (item.approveStatus == "0") {
            this.approveCount++;
          }
        });
      }
    },
    // 新增通知
    addNotices(notice: any) {
      const index = this.noticeList.findIndex((item: any) => item.id == notice.id);
      if (index == -1) {
        notice.status = 1;
        this.noticeList.unshift(notice);
        this.noticeCount++;
        if (notice.noteType == 0) {
          this.getApproveList();
        }
      }
    },
    // 通知数归零
    async clearNoticeCount() {
      const res: any = await noticeApi.getNoticesList({ isView: "1" });
      if (res.code == 0) {
        this.noticeList = res.data.list;
        this.noticeCount = 0;
        ipc?.send("message-stop-blinking");
      }
    }
  },
  persist: piniaPersistConfig("lark-notices-session")
});
