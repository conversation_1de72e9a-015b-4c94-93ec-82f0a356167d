<template>
  <div class="flex relative overflow-hidden items-start rounded-lg bg-gray-50" style="width: 150px;" @mouseover="setShow" @mouseleave="setHide">
    <div class="flex items-center gap-2">
      <el-icon v-if="!imgUrl" :size="150" color="#3a3a3a" class="bg-gray-100">
        <Picture />
      </el-icon>
      <img v-else :src="imgUrl" class="imgsmall"  />
    </div>
    <MessageLoad :data="props.dataItem" v-show="showBtn" />
    <div class="proress" v-if="props.dataItem.progress">
      <span v-if="props.dataItem.proStatus==1" class="tipsbox bg-red-500 text-white absolute z-50 left-1/2 top-1/2 text-xs py-0.5 px-1.5 flex items-center">
        上传失败
      </span>
      <el-progress type="circle" :width="50" :stroke-width="3" :percentage="props.dataItem.progress"></el-progress>
    </div>
  </div>
</template>

<script setup>
import { ref,onMounted } from "vue";
import MessageLoad from "./MessageLoad.vue";
import * as FileApi from "@/api/infra/file";
const showBtn = ref(false)
const props = defineProps({
  dataItem: {
    type: Object,
    required: true
  }
});
const imgUrl = ref('');
const setShow = () => {
  showBtn.value = true
};
const setHide = () => {
  setTimeout(()=>{
    showBtn.value = false
  },200)
  
};
const init = async() => {
  if(props.dataItem.contentId && props.dataItem.id){
    const resMsg = await FileApi.getFileUrl({ id: props.dataItem.contentId });
    if (resMsg.code == 0) {
      imgUrl.value = resMsg.data?.url
    }
  }
};
onMounted(()=>{
  init()
})
</script>
<style lang="scss" scoped>
  .proress {
    @apply w-full h-full bg-gray-100 bg-opacity-80 flex items-center justify-center rounded-lg absolute left-0 top-0;
  }
  .tipsbox{
    border-radius: 4px;
    margin-left: -30px; 
    margin-top: -10px;
  }
  .imgsmall{
    width: 150px;
  }
</style>
