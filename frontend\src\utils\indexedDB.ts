import { pinyin } from "pinyin-pro";
import { ElMessage } from "element-plus";

type Message = {
  contact: object;
  msgId: string;
  msg: string;
};

const DB_NAME = "myDatabase";
const STORE_NAME = "message";
const VERSION = 1;

let dbInstance: IDBDatabase | null = null;

function getDb(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    if (dbInstance) {
      resolve(dbInstance);
      return;
    }

    const request = indexedDB.open(DB_NAME, VERSION);

    request.onupgradeneeded = function (event: IDBVersionChangeEvent) {
      dbInstance = event.target?.result as IDBDatabase;
      if (!dbInstance.objectStoreNames.contains(STORE_NAME)) {
        dbInstance.createObjectStore(STORE_NAME, { keyPath: "msgId" });
      }
    };

    request.onsuccess = function (event: Event) {
      dbInstance = (event.target as IDBOpenDBRequest).result;
      resolve(dbInstance);
    };

    request.onerror = function (event: Event) {
      reject((event.target as IDBOpenDBRequest).error);
    };
  });
}

async function addData(data: Message): Promise<number> {
  const db = await getDb();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], "readwrite");
    const objectStore = transaction.objectStore(STORE_NAME);
    const request = objectStore.put(data);

    request.onsuccess = function (_event: Event) {
      resolve(request.result as number);
    };

    request.onerror = function (event: Event) {
      reject((event.target as IDBRequest).error);
    };
  });
}

async function getAllData(): Promise<Message[]> {
  const db = await getDb();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], "readonly");
    const objectStore = transaction.objectStore(STORE_NAME);
    const request = objectStore.getAll();

    request.onsuccess = function (_event: Event) {
      resolve(request.result as Message[]);
    };

    request.onerror = function (event: Event) {
      reject((event.target as IDBRequest).error);
    };
  });
}

async function getDatabyMsg(searchString: string): Promise<Message[]> {
  const db = await getDb();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], "readonly");
    const objectStore = transaction.objectStore(STORE_NAME);
    const request = objectStore.openCursor();
    const items: Message[] = [];

    request.onsuccess = event => {
      const cursor = (event.target as IDBRequest<IDBCursorWithValue | null>).result;
      if (cursor) {
        const item = cursor.value;
        if (item.msg) {
          const lowerCaseItem = item.msg.toLowerCase();
          const pinyinWithoutTone = pinyin(item.msg, { toneType: "none" }).toLowerCase();
          if (lowerCaseItem.includes(searchString.toLowerCase()) || pinyinWithoutTone.includes(searchString.toLowerCase())) {
            items.push(item);
          }
        }
        cursor.continue();
      } else {
        resolve(items as Message[]);
      }
    };

    request.onerror = function (event: Event) {
      reject((event.target as IDBRequest).error);
    };
  });
}

async function deleteData(id: number): Promise<void> {
  const db = await getDb();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], "readwrite");
    const objectStore = transaction.objectStore(STORE_NAME);
    const request = objectStore.delete(id);

    request.onsuccess = function (_event: Event) {
      resolve();
    };

    request.onerror = function (event: Event) {
      reject((event.target as IDBRequest).error);
    };
  });
}

async function deleteAllData() {
  try {
    const db = await getDb();
    const tx = db.transaction([STORE_NAME], "readwrite");
    const store = tx.objectStore(STORE_NAME);
    store.clear();
  } catch (error: any) {
    ElMessage.error(error.message);
  }
}

export { addData, getAllData, deleteData, getDatabyMsg, deleteAllData };
