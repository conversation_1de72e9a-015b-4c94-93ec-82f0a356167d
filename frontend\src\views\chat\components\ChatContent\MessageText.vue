<template>
  <div>
    <aside
      class="text-sm font-normal break-all max-w-sm conimg"
      style="display: inline"
      :class="{ 'drop-shadow-sm': props.dataItem.sender == userStore.userId }"
      v-html="transform(props.dataItem.msg)"
    ></aside>
    <!-- 引用文本 -->
    <span v-if="props.dataItem.quoteContent != null" class="bg-gray-100 flex text-gray-400 mt-2 py-2 px-3 rounded-md text-sm">
      <span>{{ props.dataItem.quoteSenderName + ":" }}</span>
      <span v-html="props.dataItem.quoteContent" style="word-break: break-all" class="flex-1"></span>
    </span>
    <!-- 引用文件 -->
    <span
      v-if="props.dataItem.quoteMessageFile != null && props.dataItem.quoteMessageFile.contentId"
      class="bg-white flex flex-col text-gray-400 mt-2 px-3 py-2 rounded-md text-sm"
    >
      <p v-if="props.dataItem.quoteSenderName" class="mr-2 mb-1">{{ props.dataItem.quoteSenderName + ": " }}</p>
      <!-- 引用图片 -->
      <MessageImg :data-item="props.dataItem.quoteMessageFile" v-if="props.dataItem.quoteMessageFile.msgType == 2" />
      <!-- 引用文本文件 -->
      <MessageFile :data-item="props.dataItem.quoteMessageFile" v-if="props.dataItem.quoteMessageFile.msgType == 1" />
      <!-- 引用音频文件 -->
      <MessageAudio :data-item="props.dataItem.quoteMessageFile" v-if="props.dataItem.quoteMessageFile.msgType == 3" />
    </span>
  </div>
</template>

<script setup>
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import MessageImg from "./MessageImg.vue";
import MessageFile from "./MessageFile.vue";
import MessageAudio from "./MessageAudio.vue";

const userStore = useUserStore();
const talkStore = useTalkStore();

const transform = content => {
  const fa = talkStore.commonEmoji;
  if (content) {
    content = content
      // eslint-disable-next-line
      .replace(/face\[([^\s\[\]]+?)]/g, function (face) {
        // 转义表情
        const altStr = face.replace(/^face/g, "");
        const isFace = talkStore.commonEmojiList.includes(altStr);
        if (isFace) {
          return '<img width="24px" height="24px" src="' + fa[altStr] + '">';
        }
      })
      .replace(/\n/g, "<br/>");
  }
  return content;
};

const props = defineProps({
  dataItem: {
    type: Object,
    required: true
  }
});
</script>
<style scoped lang="scss">
.conimg {
  :deep(img) {
    display: inline !important;
  }
}
</style>
