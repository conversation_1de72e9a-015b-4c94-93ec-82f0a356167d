<template>
  <div class="widget-container h-full overflow-visible">
    <!-- 悬浮标题和按钮容器 -->
    <div class="absolute top-0 left-4 -translate-y-1/2 flex items-center gap-2 z-10">
      <!-- 小组件标题 -->
      <div
        class="widget-title bg-white dark:bg-gray-800 px-3 py-1.5 rounded-full shadow-md border border-gray-200 dark:border-gray-700 flex items-center gap-2"
      >
        <font-awesome-icon :icon="['fas', 'cube']" class="text-lg text-orange-500" />
        <span class="text-xs font-bold text-gray-700 dark:text-gray-200">AI 微应用</span>
      </div>
      <!-- 添加按钮 -->
      <button
        @click="maximizeWidget"
        class="w-7 h-7 rounded-full bg-orange-500 text-white flex items-center justify-center shadow-md"
        title="展开应用商店"
      >
        <font-awesome-icon :icon="['fas', 'plus']" class="w-5 h-5" />
      </button>
    </div>

    <div class="flex flex-col h-full p-3 pt-6">
      <!-- AI工具网格 - 固定2行4列布局，支持滚动 -->
      <div class="flex-1 overflow-y-auto ai-apps-container">
        <!-- 空状态显示：居中显示图标和文字 -->
        <div v-if="aiApps.length === 0" class="flex flex-col items-center justify-center h-full text-gray-400 dark:text-gray-500">
          <font-awesome-icon :icon="['fas', 'cube']" class="text-4xl text-orange-500 mb-2" />
          <span class="text-sm">暂无AI微应用</span>
        </div>
        <div v-else class="grid grid-cols-4 gap-1.5 p-1 ai-apps-grid">
          <div
            v-for="app in aiApps"
            :key="app.id"
            class="ai-app-item bg-white dark:bg-gray-800 rounded-lg p-1.5 border border-gray-200 dark:border-gray-700 shadow relative group flex flex-col items-center justify-center text-center cursor-pointer text-gray-800 dark:text-gray-200"
            @click="openApp(app)"
          >
            <!-- 删除按钮 -->
            <button
              v-if="aiApps.length > 1"
              @click.stop="removeApp(app.id)"
              class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center text-xs z-[60] cursor-pointer"
              title="删除应用"
            >
              <font-awesome-icon :icon="['fas', 'times']" class="w-3 h-3" />
            </button>

            <!-- 统一的应用布局 -->
            <div class="flex flex-col h-full justify-center items-center text-center relative z-[50] space-y-1">
              <AiAppIcon :name="app.name" :icon="app.icon" size="large" />
              <span class="text-xs md:text-sm font-black truncate w-full">{{ app.name }}</span>
            </div>

            <!-- 玻璃效果装饰 -->
            <div
              class="absolute inset-0 rounded-lg bg-gradient-to-br from-white/10 to-transparent pointer-events-none z-10"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认关闭对话框 -->
    <div
      v-if="showConfirmDialog"
      class="fixed inset-0 bg-black/50 flex items-center justify-center z-[9999]"
      @click.self="cancelConfirm"
    >
      <div
        class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4 shadow-2xl transform transition-all duration-300 scale-100"
      >
        <div class="flex items-center mb-4">
          <font-awesome-icon :icon="['fas', 'sparkles']" class="text-orange-500 mr-3 text-2xl" />
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">确认关闭应用</h3>
        </div>

        <div class="mb-6">
          <p class="text-gray-600 dark:text-gray-300 mb-3">检测到已有AI微应用正在运行，是否关闭以下应用？</p>

          <div class="bg-orange-50 dark:bg-orange-900/30 rounded-lg p-3 mb-3 border border-orange-200 dark:border-orange-700">
            <div class="flex items-center">
              <Icon :icon="currentActiveApp?.icon || sparklesIcon" width="20" height="20" class="mr-2 text-orange-600" />
              <span class="font-medium text-orange-900 dark:text-orange-100">{{ currentActiveApp?.name }}</span>
            </div>
            <p class="text-sm text-orange-700 dark:text-orange-300 mt-1">{{ currentActiveApp?.description }}</p>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <button
            @click="cancelConfirm"
            class="px-4 py-2 text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg"
          >
            取消
          </button>
          <button
            @click="confirmCloseAndOpen"
            class="px-4 py-2 bg-orange-500 text-white rounded-lg"
          >
            确认关闭
          </button>
        </div>
      </div>
    </div>


  </div>

  <!-- 全屏对话框 -->
  <el-dialog
    v-model="isMaximized"
    width="95%"
    top="5vh"
    class="custom-dialog fullscreen-app-store-dialog"
    :show-close="false"
    :close-on-click-modal="false"
    append-to-body
  >
    <!-- Header -->
    <template #header>
            <button class="close-btn" @click="closeMaximize">
        <font-awesome-icon :icon="['fas', 'compress']" />
      </button>
      <div class="flex items-center gap-2">
        <font-awesome-icon :icon="['fas', 'store']" class="text-lg text-orange-500" />
        <span class="title">AI微应用商店</span>
      </div>
    </template>

    <!-- Body -->
    <div class="p-2">
      <div class="h-full flex flex-col">

            <!-- 全屏搜索栏和分类筛选 -->
            <div class="mb-6 space-y-4">
              <!-- 搜索框 -->
              <div class="relative max-w-md">
                <font-awesome-icon :icon="['fas', 'search']" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  v-model="searchKeyword"
                  type="text"
                  placeholder="搜索AI微应用..."
                  class="search-input w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>

              <!-- 分类筛选 -->
              <div class="flex flex-wrap gap-2">
                <button
                  v-for="category in availableCategories"
                  :key="category"
                  @click="selectedCategory = category"
                  class="px-4 py-2 text-sm rounded-full"
                  :class="selectedCategory === category 
                    ? 'bg-orange-500 text-white' 
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'"
                >
                  {{ category }}
                </button>
              </div>
            </div>

            <!-- 全屏应用列表 -->
            <div class="flex-1 overflow-hidden min-h-0">
              <div class="h-full overflow-y-auto space-y-4 p-2 custom-scrollbar">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  <div
                    v-for="app in filteredAvailableApps"
                    :key="app.id"
                    class="fullscreen-app-card bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-600 shadow cursor-pointer"
                    @click="addAppToList(app)"
                  >
                    <!-- 应用信息 -->
                    <div class="flex flex-col h-full">
                      <!-- 固定高度的内容区域 -->
                      <div class="flex-1 flex flex-col items-center text-center px-2 py-4">
                        <!-- 图标区域 - 固定高度 -->
                        <div class="w-16 h-16 flex items-center justify-center mb-3">
                          <AiAppIcon :name="app.name" :icon="app.icon" size="large" />
                        </div>

                        <!-- 应用名称 - 固定高度 -->
                        <div class="h-8 flex items-center justify-center mb-2">
                          <h4 class="text-lg font-semibold text-gray-900 dark:text-white line-clamp-1">{{ app.name }}</h4>
                        </div>

                        <!-- 描述区域 - 固定高度，最多两行 -->
                        <div class="h-10 mb-3 flex items-start">
                          <p class="text-sm text-gray-600 dark:text-gray-300 leading-5 line-clamp-2">{{ app.description }}</p>
                        </div>

                        <!-- 标签区域 - 固定高度 -->
                        <div class="h-8 flex items-center justify-center w-full">
                          <div class="flex items-center justify-center gap-1 overflow-hidden">
                            <span
                              v-for="tag in app.tags.slice(0, 3)"
                              :key="tag"
                              class="app-tag px-2 py-1 text-xs bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 rounded-full whitespace-nowrap"
                            >
                              {{ tag }}
                            </span>
                          </div>
                        </div>
                      </div>

                      <!-- 底部按钮区域 - 固定在底部 -->
                      <div class="flex justify-end p-4 pt-2">
                        <button
                          :disabled="isAppAdded(app.id)"
                          class="add-button px-4 py-2 text-sm rounded-lg"
                          :class="isAppAdded(app.id) 
                            ? 'bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed' 
                            : 'bg-orange-500 text-white'"
                        >
                          {{ isAppAdded(app.id) ? "已添加" : "添加" }}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 空状态 -->
                <div v-if="filteredAvailableApps.length === 0" class="flex items-center justify-center h-full">
                  <div class="text-center text-gray-500">
                    <div class="mb-4">
                      <Icon icon="ion:applications-outline" class="w-16 h-16 text-gray-400 mx-auto" />
                    </div>
                    <div class="text-lg">没有找到匹配的应用</div>
                  </div>
                </div>
              </div>
            </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { shallowRef, ref, onMounted, onUnmounted, computed } from 'vue'
import { Icon } from '@iconify/vue'
import { useAiAppStore } from '@/stores/modules/aiapp'
import { ipc } from '@/utils/ipcRenderer'
import { ipcApiRoute } from '@/ipc/ipcApi'
import { getAvailableApps, getAppPage, addUserApp, removeUserApp, type AiAppData, type AppPageReqVO, type PageResult } from '@/api/modules/aiApps'
import AiAppIcon from '@/components/AiAppIcon.vue'

// 已使用全局注册的 Font Awesome 图标，无需单独导入
import widget5BoldDuotone from '@iconify-icons/solar/widget-5-bold-duotone';
import sparklesIcon from '@iconify-icons/solar/magic-stick-3-bold-duotone';

interface FormField {
  /** 字段显示标签 */
  label: string;
  /** 字段名称 */
  name: string;
  /** CSS样式类名 */
  class: string;
  /** 默认值 */
  value: string | number;
  /** 输入类型 */
  type?: string;
  /** 选项列表（用于select类型） */
  options?: Array<{ label: string; value: string }>;
  /** 文件接受类型（用于file类型） */
  accept?: string;
  /** 是否支持多文件（用于file类型） */
  multiple?: boolean;
  /** 最小值（用于number/range类型） */
  min?: number;
  /** 最大值（用于number/range类型） */
  max?: number;
  /** 步长（用于range类型） */
  step?: number;
  /** 占位符文本 */
  placeholder?: string;
}

interface AppInputs {
  /** 表单字段定义 */
  fields: FormField[];
}

interface AiApp {
  /** 应用编号 */
  id: number;
  /** 应用标识符 */
  appId: string;
  /** 应用名称 */
  name: string;
  /** 应用描述 */
  description?: string;
  /** 应用类型 */
  type?: number;
  /** 应用状态 */
  status?: number;
  /** 应用输入配置 */
  inputs?: AppInputs;
  /** 应用图标 - 可以是图标名称或URL */
  icon: string | any;
  /** 主题色 */
  themeColor?: string;
  /** 排序 */
  sort?: number;
  /** 创建时间 */
  createTime?: string;
  /** 应用分类 */
  category?: string;
  /** 应用标签 */
  tags?: string[];
  /** 是否为热门应用 */
  isHot?: boolean;
  /** 应用评分 */
  rating?: number;
}

interface AvailableApp extends AiApp {
  /** 应用标签 */
  tags: string[];
  /** 应用分类 */
  category: string;
  /** 是否为热门应用 */
  isHot?: boolean;
  /** 应用评分 */
  rating?: number;
}

// ==================== 窗口隔离机制 ====================

// 全局窗口状态管理
interface WindowInstance {
  id: string;
  appId: string;
  appName: string;
  appData: AiApp;
  timestamp: number;
}

// 全局窗口注册表（使用localStorage实现跨窗口共享）
const WINDOW_REGISTRY_KEY = "ai_app_windows_registry";
const CURRENT_WINDOW_KEY = "ai_app_current_window";

// 生成唯一窗口ID
const generateWindowId = (): string => {
  return `window_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// 获取当前活跃的窗口列表
const getActiveWindows = (): WindowInstance[] => {
  try {
    const registry = localStorage.getItem(WINDOW_REGISTRY_KEY);
    return registry ? JSON.parse(registry) : [];
  } catch (error) {
    console.error("获取窗口注册表失败:", error);
    return [];
  }
};

// 注册新窗口
const registerWindow = (windowInstance: WindowInstance): void => {
  try {
    const windows = getActiveWindows();
    // 移除过期窗口（超过5分钟的窗口认为已关闭）
    const now = Date.now();
    const validWindows = windows.filter(w => now - w.timestamp < 5 * 60 * 1000);

    // 添加新窗口
    validWindows.push(windowInstance);
    localStorage.setItem(WINDOW_REGISTRY_KEY, JSON.stringify(validWindows));
    localStorage.setItem(CURRENT_WINDOW_KEY, windowInstance.id);
  } catch (error) {
    console.error("注册窗口失败:", error);
  }
};

// 注销窗口
const unregisterWindow = (windowId: string): void => {
  try {
    const windows = getActiveWindows();
    const filteredWindows = windows.filter(w => w.id !== windowId);
    localStorage.setItem(WINDOW_REGISTRY_KEY, JSON.stringify(filteredWindows));

    // 如果注销的是当前窗口，清除当前窗口标记
    const currentWindowId = localStorage.getItem(CURRENT_WINDOW_KEY);
    if (currentWindowId === windowId) {
      localStorage.removeItem(CURRENT_WINDOW_KEY);
    }
  } catch (error) {
    console.error("注销窗口失败:", error);
  }
};

// 关闭所有活跃的AI聊天窗口
const closeOtherWindows = async (): Promise<void> => {
  try {
    const windows = getActiveWindows();

    if (windows.length > 0) {
      // 注意：当前Electron实现只支持单个AI聊天窗口
      // 所以我们直接调用关闭方法，这会关闭当前存在的窗口
      await ipc?.invoke(ipcApiRoute.closeAiChatWindow);

      // 清理所有窗口的注册信息
      windows.forEach(window => {
        unregisterWindow(window.id);
      });
    }
  } catch (error) {
    console.error("[窗口管理] 关闭AI聊天窗口失败:", error);
  }
};

// ==================== Store 初始化 ====================

// 初始化 AI 微应用 store
const aiAppStore = useAiAppStore();

// ==================== 组件状态 ====================

// 确认对话框状态
const showConfirmDialog = ref(false);
const pendingApp = ref<AiApp | null>(null);
const currentActiveApp = ref<AiApp | null>(null);

// Toast状态
const showToast = ref(false);
const toastMessage = ref('');
const toastType = ref<'success' | 'error' | 'info'>('success');

// 全屏状态
const isMaximized = ref(false);
const searchQuery = ref('');
const selectedCategory = ref('全部');
const searchKeyword = ref('');
const isAnimating = ref(false);

// 用户的AI微应用列表（从 store 获取）
const aiApps = computed(() => aiAppStore.myAppList);

// 可用的AI微应用列表（应用商店中的应用）
const availableApps = shallowRef<AvailableApp[]>([])
// 加载状态
const isLoadingApps = ref(false)
// 错误状态
const loadAppsError = ref<string | null>(null)

// 从后端获取可用应用列表
const fetchAvailableApps = async () => {
  isLoadingApps.value = true
  loadAppsError.value = ''
  
  try {
    // 构建分页请求参数
    const pageParams: AppPageReqVO = {
      pageNo: 1,
      pageSize: 100, // 获取前100个应用，可根据需要调整
      status: 1 // 只获取启用状态的应用
    }
    
    // 调用分页接口获取全部微应用列表
    const response = await getAppPage(pageParams)
    const pageResult: PageResult<AiAppData> = response.data
    const apps = pageResult.list || []
    
    console.log('获取到的微应用数据:', apps)
    console.log('总数量:', pageResult.total)
    
    // 将后端数据映射为组件需要的格式
    availableApps.value = apps.map(app => ({
      ...app,
      appId: app.appId || '', // 确保 appId 不为 undefined
      description: app.description || '', // 确保 description 不为 undefined
      tags: getAppTags(app.appId || ''),
      category: getAppCategory(app.appId || ''),
      type: app.type || 1, // 默认类型
      status: app.status || 1, // 默认状态
      createTime: app.createTime || new Date().toISOString(),
      isHot: app.isHot || false,
      rating: app.rating || 4.0
    }))
    
    console.log('处理后的应用列表:', availableApps.value)
    
  } catch (error) {
    console.error('获取可用应用列表失败:', error)
    loadAppsError.value = '获取应用列表失败，请稍后重试'
    // 出错时保持空数组，完全依赖后端数据
    availableApps.value = []
  } finally {
    isLoadingApps.value = false
  }
}

// 获取应用标签的辅助函数
function getAppTags(appId: string): string[] {
  const tagMap: Record<string, string[]> = {
    jiaoyou: ["社交", "交友", "青年"],
    dushu: ["阅读", "学习", "文档"],
    moxing: ["AI", "生成", "模型"],
    wenjian: ["审查", "文件", "检测"],
    calculator: ["数学", "计算", "公式"],
    painter: ["绘画", "创作", "艺术"],
    analyzer: ["数据", "分析", "图表"],
    photographer: ["摄影", "修图", "滤镜"],
    composer: ["音乐", "作曲", "创作"],
    writer: ["写作", "文章", "创作"],
    translator: ["翻译", "语言", "国际化"],
    coder: ["编程", "代码", "开发"],
    voice: ["语音", "转录", "合成"],
    marketing: ["营销", "推广", "文案"],
    scheduler: ["日程", "规划", "时间"]
  };
  return tagMap[appId] || ["AI", "工具"];
}

// 获取应用分类的辅助函数
function getAppCategory(appId: string): string {
  const categoryMap: Record<string, string> = {
    jiaoyou: "社交",
    dushu: "学习",
    moxing: "生成",
    wenjian: "工具",
    calculator: "工具",
    painter: "创意",
    analyzer: "分析",
    photographer: "创意",
    composer: "创意",
    writer: "文本",
    translator: "语言",
    coder: "开发",
    voice: "语音",
    marketing: "商业",
    scheduler: "效率"
  };
  return categoryMap[appId] || "其他";
}

// ==================== 确认对话框处理函数 ====================

// 取消确认对话框
const cancelConfirm = () => {
  showConfirmDialog.value = false;
  currentActiveApp.value = null;
  pendingApp.value = null;
};

// 确认关闭当前窗口并打开新窗口
const confirmCloseAndOpen = async () => {
  showConfirmDialog.value = false;

  if (pendingApp.value) {
    // 关闭所有其他窗口
    await closeOtherWindows();

    // 打开新窗口
    await openAppWindow(pendingApp.value);

    // 清理状态
    currentActiveApp.value = null;
    pendingApp.value = null;
  }
};

// ==================== 核心窗口管理函数 ====================

const openApp = async (app: AiApp) => {

  // 检查是否有活跃窗口（主窗口不算在内，只检查AI聊天窗口）
  const activeWindows = getActiveWindows();

  if (activeWindows.length > 0) {

    // 显示第一个活跃窗口的信息（通常只有一个）
    currentActiveApp.value = activeWindows[0].appData;
    pendingApp.value = app;
    showConfirmDialog.value = true;
    return;
  }

  // 没有冲突，直接打开窗口
  await openAppWindow(app);
};

// 实际打开窗口的函数
const openAppWindow = async (app: AiApp) => {

  // 解析inputs数据 - 如果是字符串则需要JSON.parse
  let parsedInputs: any = null;
  try {
    if (typeof app.inputs === 'string') {
      parsedInputs = JSON.parse(app.inputs);
    } else if (app.inputs && typeof app.inputs === 'object') {
      parsedInputs = app.inputs;
    }
  } catch (error) {
    console.error('❌ AI微应用inputs解析失败:', error);
    parsedInputs = null;
  }

  // 将配置字段转换为键值对对象
  const inputValues: Record<string, any> = {};
  if (parsedInputs?.fields && Array.isArray(parsedInputs.fields)) {
    parsedInputs.fields.forEach((field: any) => {
      inputValues[field.name] = field.value;
    });
  }

  // 转换字段格式以匹配AI聊天组件期望的FormField接口
  const configFields =
    parsedInputs?.fields?.map((field: any) => {
      const baseField: any = {
        label: field.label,
        name: field.name,
        class: field.class,
        value: field.value,
        type: field.type || "text"
      };

      // 为select类型添加选项
      if (field.options) {
        baseField.options = field.options;
      }

      // 为range类型设置合适的min、max、step值
      if (field.type === "range") {
        // 根据字段名称和当前值智能设置范围
        if (field.name.includes("creativity") || field.name.includes("temperature")) {
          baseField.min = 0;
          baseField.max = 1;
          baseField.step = 0.1;
        } else if (field.name.includes("token") || field.name.includes("length")) {
          baseField.min = 100;
          baseField.max = 4000;
          baseField.step = 100;
        } else {
          // 默认范围
          baseField.min = 0;
          baseField.max = 100;
          baseField.step = 1;
        }
      }

      // 为number类型添加min、max、placeholder等属性
      if (field.type === "number") {
        if (field.min !== undefined) baseField.min = field.min;
        if (field.max !== undefined) baseField.max = field.max;
        if (field.placeholder) baseField.placeholder = field.placeholder;
      }

      // 为file类型添加accept和multiple属性
      if (field.type === "file") {
        if (field.accept) baseField.accept = field.accept;
        if (field.multiple !== undefined) baseField.multiple = field.multiple;
      }

      return baseField;
    }) || [];

  // 准备完整的配置数据，符合AppConfigData接口
  // 确保所有数据都是可序列化的
  const appConfigData = {
    appInfo: {
      id: app.id,
      appId: app.appId || `app_${app.id}`,
      name: app.name,
      description: app.description,
      type: app.type,
      status: app.status,
      // 确保icon是字符串，避免传递Vue组件对象
      icon: typeof app.icon === "string" ? app.icon : "mdi:robot",
      themeColor: app.themeColor,
      sort: app.sort || 0,
      createTime: app.createTime
    },
    inputs: JSON.parse(JSON.stringify(inputValues)), // 深拷贝确保可序列化
    configFields: JSON.parse(JSON.stringify(configFields)) // 深拷贝确保可序列化
  };

  try {
    // 生成新的窗口实例
    const windowId = generateWindowId();
    // 创建可序列化的应用数据副本
    const serializableAppData = {
      id: app.id,
      appId: app.appId,
      name: app.name,
      description: app.description,
      type: app.type,
      status: app.status,
      icon: typeof app.icon === "string" ? app.icon : "mdi:robot",
      themeColor: app.themeColor,
      sort: app.sort,
      createTime: app.createTime
      // 不包含inputs等可能包含复杂对象的字段
    };

    const windowInstance: WindowInstance = {
      id: windowId,
      appId: app.appId || `app_${app.id}`,
      appName: app.name,
      appData: serializableAppData, // 使用可序列化的数据
      timestamp: Date.now()
    };

    // 在配置数据中添加窗口ID，用于窗口隔离
    // 注意：不要传递整个windowInstance对象，因为它包含无法序列化的数据
    const configWithWindowId = {
      ...appConfigData,
      windowId: windowId,
      // 只传递必要的窗口信息，避免序列化问题
      windowInfo: {
        id: windowInstance.id,
        appId: windowInstance.appId,
        appName: windowInstance.appName,
        timestamp: windowInstance.timestamp
      }
    };

    // 在发送前验证数据是否可序列化
    try {
      JSON.stringify(configWithWindowId);
    } catch (serializeError) {
      console.error("[窗口管理] 配置数据序列化失败:", serializeError);
      throw new Error("配置数据包含无法序列化的对象");
    }

    // 调用IPC打开AI聊天窗口，传递包含窗口ID的配置数据
    await ipc?.invoke(ipcApiRoute.openAiChatWindow, configWithWindowId);

    // 注册新窗口到全局注册表
    registerWindow(windowInstance);

  } catch (error: any) {
    console.error("[窗口管理] 打开AI聊天窗口失败:", error);
    // 如果打开失败，显示错误提示
    // 这里可以添加错误提示UI
  }
};

// 删除应用
const removeApp = async (appId: number) => {
  // 确保至少保留一个应用
  if (aiApps.value.length <= 1) {
    console.log("至少需要保留一个微应用");
    return;
  }

  const appIndex = aiApps.value.findIndex(app => app.id === appId);
  if (appIndex === -1) {
    console.error("未找到要删除的应用");
    return;
  }

  const appName = aiApps.value[appIndex].name;
  
  try {
    // 调用后端API移除用户与微应用的关联
    await removeUserApp(appId);
    
    // 从本地列表中移除
    aiApps.value.splice(appIndex, 1);
    
    // 同时更新store中的数据
    aiAppStore.removeAppFromMyList(appId);
    
    // 不显示移除成功的toast提示，保持界面简洁
  } catch (error) {
    console.error('移除应用失败:', error);
    console.error(`移除 ${appName} 失败，请稍后重试`);
  }
};

// ==================== 计算属性 ====================

// 过滤后的可用应用列表
const filteredAvailableApps = computed(() => {
  let filtered = availableApps.value;

  // 按分类筛选
  if (selectedCategory.value !== "全部") {
    filtered = filtered.filter(app => app.category === selectedCategory.value);
  }

  // 按关键词搜索
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase().trim();
    filtered = filtered.filter(
      app =>
        app.name.toLowerCase().includes(keyword) ||
        (app.description && app.description.toLowerCase().includes(keyword)) ||
        app.tags.some(tag => tag.toLowerCase().includes(keyword)) ||
        app.category.toLowerCase().includes(keyword)
    );
  }

  return filtered;
});

// 获取所有可用分类
const availableCategories = computed(() => {
  const categories = ["全部", ...new Set(availableApps.value.map(app => app.category))];
  return categories;
});

// ==================== 应用商店方法 ====================

// 检查应用是否已添加
const isAppAdded = (appId: number): boolean => {
  return aiApps.value.some(app => app.id === appId);
};

// 添加应用到列表
const addAppToList = async (app: AvailableApp) => {
  if (isAppAdded(app.id)) {
    console.log(`${app.name} 已经在您的应用列表中`);
    return;
  }

  try {
    // 调用后端API添加用户到微应用
    await addUserApp(app.id);
    
    // 创建新的应用对象（去除额外的字段）
    const newApp: AiApp = {
      id: app.id,
      appId: app.appId,
      name: app.name,
      description: app.description,
      type: app.type,
      status: app.status,
      inputs: app.inputs,
      icon: app.icon,
      themeColor: app.themeColor,
      sort: app.sort,
      createTime: app.createTime
    };

    // 添加到本地aiApps列表
    aiApps.value.push(newApp);
    
    // 同时更新store中的数据
    aiAppStore.addAppToMyList(newApp);

    // 添加成功，不显示任何提示
    console.log(`${app.name} 已成功添加到您的应用列表`);
    
    // 不再自动关闭对话框，让用户继续浏览和添加其他应用
  } catch (error) {
    console.error('添加应用失败:', error);
    // 错误信息仅在控制台输出
    console.error(`添加 ${app.name} 失败，请稍后重试`);
  }
};



// ==================== 生命周期管理 ====================

// 当前组件的窗口ID（如果有的话）
let currentComponentWindowId: string | null = null;

// 组件挂载时的初始化
onMounted(async () => {

  // 清理过期的窗口注册
  cleanupExpiredWindows();

  // 监听窗口关闭事件（如果需要的话）
  window.addEventListener("beforeunload", handleWindowClose);

  // 获取我的 AI 微应用列表
  try {
    await aiAppStore.fetchMyAppList();
  } catch (error) {
    console.error('❌ AI 微应用列表初始化失败:', error);
  }
});

// 组件卸载时的清理
onUnmounted(() => {
  // 清理当前组件相关的窗口注册
  if (currentComponentWindowId) {
    unregisterWindow(currentComponentWindowId);
  }

  // 移除事件监听器
  window.removeEventListener("beforeunload", handleWindowClose);
});

// 处理窗口关闭事件
const handleWindowClose = () => {
  if (currentComponentWindowId) {
    unregisterWindow(currentComponentWindowId);
  }
};

// 清理过期窗口的工具函数
const cleanupExpiredWindows = () => {
  try {
    const windows = getActiveWindows();
    const now = Date.now();
    const validWindows = windows.filter(w => now - w.timestamp < 5 * 60 * 1000); // 5分钟过期

    if (validWindows.length !== windows.length) {
      localStorage.setItem(WINDOW_REGISTRY_KEY, JSON.stringify(validWindows));
    }
  } catch (error) {
    console.error("[窗口管理] 清理过期窗口失败:", error);
  }
};

// ==================== 全屏展开功能 ====================

// 最大化组件
const maximizeWidget = async () => {
  if (isAnimating.value) return; // 防抖处理

  isAnimating.value = true;
  // 使用requestAnimationFrame优化动画性能
  requestAnimationFrame(async () => {
    isMaximized.value = true
    // 重置搜索和分类状态
    searchKeyword.value = "";
    selectedCategory.value = "全部";

    // 获取全部微应用列表
    await fetchAvailableApps();

    // 动画完成后重置状态
    setTimeout(() => {
      isAnimating.value = false;
    }, 200);
  });
};

// 关闭全屏
const closeMaximize = () => {
  if (isAnimating.value) return; // 防抖处理

  isAnimating.value = true;
  // 使用requestAnimationFrame优化动画性能
  requestAnimationFrame(() => {
    isMaximized.value = false
    // 重置搜索和分类状态
    searchKeyword.value = "";
    selectedCategory.value = "全部";

    // 动画完成后重置状态
    setTimeout(() => {
      isAnimating.value = false;
    }, 200);
  });
};
</script>

<style scoped lang="scss">
.widget-container {
  overflow: visible;
}

// 全屏对话框样式
.fullscreen-app-store-dialog {
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
  }
}

// 自定义滚动条样式
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;

  &::-webkit-scrollbar {
    width: 4px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }
}

.dark .custom-scrollbar {
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 移除所有样式，使用父级样式

// 移除暗色模式样式

// 移除图标hover效果，只保留可点击状态

// 移除了不再使用的动画样式

// AI应用容器样式
.ai-apps-container {
  // 限制容器高度为2行的高度
  max-height: calc(2 * 120px + 1.5rem + 1rem); // 2行高度 + 间隙 + 内边距
  min-height: calc(2 * 120px + 1.5rem + 1rem); // 确保最小高度一致
}

// AI应用网格布局样式
.ai-apps-grid {
  // 确保网格能够显示完整的行
  grid-auto-rows: 120px; // 固定行高
}

.ai-app-item {
  position: relative;
  z-index: 30;
  aspect-ratio: 1 / 1;
  height: 120px; // 固定高度与网格行高一致
  width: 100%; // 确保宽度填满网格单元
}

// 自定义滚动条样式
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }
}

// 搜索框样式
.search-input {
  &:focus {
    box-shadow: 0 0 0 3px rgba(255, 149, 0, 0.1);
  }
}

// 标签样式
.app-tag {
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 12px;
}

// 添加按钮样式
.add-button {
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 文本截断样式
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 移除了不再使用的脉冲动画

// Toast通知样式
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.toast-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 应用商店对话框动画
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
</style>
