<template>
  <el-dialog v-model="visible" :show-close="false" :close-on-click-modal="false" class="custom-dialog need-foot">
    <template #header>
      <button class="close-btn" @click="closeDialog">
        <font-awesome-icon :icon="['fas', 'times']" />
      </button>
      <div class="flex items-center gap-2">
        <font-awesome-icon :icon="['fas', 'users']" class="icon" />
        <span class="title">删除成员</span>
      </div>
    </template>
    <template #default>
      <el-scrollbar height="300">
        <el-checkbox-group v-model="delData">
          <el-checkbox v-for="i in data" :value="i.id" :key="i.id" :disabled="i.member == userStore.userId">
            <DynamicAvatar :id="i.member" :data-info="i" :relation-name="i.memberName" :type-avatar="0" :size="32" />
            <span class="ml-2">{{ i.memberName }}</span>
            <span class="ml-2">{{ i.pathName }}</span>
          </el-checkbox>
        </el-checkbox-group>
      </el-scrollbar>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="DelGroupMember">
import { computed, ref } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import { useContactStore } from "@/stores/modules/friend";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";

const userStore = useUserStore();
const talkStore = useTalkStore();
const contactStore = useContactStore();

const visible = ref(false);
const closeDialog = () => {
  visible.value = false;
};
const openDialog = () => {
  visible.value = true;
  delData.value = []
};
defineExpose({ openDialog, closeDialog });

const data = computed(() => contactStore.groupMembers[talkStore.activeChatId] || []);
const delData = ref([]);
const emit = defineEmits(["del-member"]);
const handleConfirm = async () => {
  emit("del-member", delData.value);
};
</script>

<style lang="scss" scoped>
.el-checkbox-group {
  @apply flex flex-col;

  :deep(.el-checkbox) {
    @apply flex items-center mb-4 h-auto;

    .el-checkbox__label {
      @apply flex items-center;
    }
  }
}
</style>
