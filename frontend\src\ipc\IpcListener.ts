// src/utils/IpcListenerManager.ts
import { ipc } from "@/utils/ipcRenderer";
import { ElMessage } from "element-plus";

type IpcCallback = (event: any, ...args: any[]) => void;
type Middleware = (args: any[]) => any[];

interface ListenerInfo {
  channel: string;
  callback: IpcCallback;
  rawCallback: IpcCallback;
  tag: string;
  once: boolean;
  timeoutId?: NodeJS.Timeout;
}

class IpcListenerManager {
  private listeners: ListenerInfo[] = [];
  private static instance: IpcListenerManager;

  private maxListenersPerTag = 10; // 默认每个tag最大监听数量
  private debugMode = true; // 是否开启调试日志
  private middlewares: Middleware[] = [];

  private constructor() {}

  public static getInstance(): IpcListenerManager {
    if (!IpcListenerManager.instance) {
      IpcListenerManager.instance = new IpcListenerManager();
    }
    return IpcListenerManager.instance;
  }

  /**
   * 开启/关闭调试
   */
  public setDebugMode(enable: boolean): void {
    this.debugMode = enable;
  }

  /**
   * 设置每个 tag 最多监听数
   */
  public setMaxListenersPerTag(limit: number): void {
    this.maxListenersPerTag = limit;
  }

  /**
   * 添加中间件
   */
  public use(middleware: Middleware): void {
    this.middlewares.push(middleware);
  }

  /**
   * 添加监听器
   */
  public add(tag: string, channel: string, callback: IpcCallback, options?: { once?: boolean; timeoutMs?: number }): void {
    const exists = this.listeners.find(l => l.tag === tag && l.channel === channel);

    if (!ipc) {
      return;
    }

    if (exists) {
      // 移除旧的监听器
      this.remove(tag, channel, callback);
      return;
    }

    const safeCallback: IpcCallback = (event, ...args) => {
      if (this.debugMode) {
      }
      try {
        let processedArgs = args;
        for (const mw of this.middlewares) {
          processedArgs = mw(processedArgs);
        }
        callback(event, ...processedArgs);
      } catch (error:any) {
        ElMessage.error(error.message);
      } finally {
        if (options?.once) {
          this.remove(tag, channel, callback);
        }
      }
    };

    if (options?.once) {
      ipc.once(channel, safeCallback);
    } else {
      ipc.on(channel, safeCallback);
    }

    const listenerInfo: ListenerInfo = {
      channel,
      callback: safeCallback,
      rawCallback: callback,
      tag,
      once: options?.once ?? false
    };

    if (options?.timeoutMs && !options.once) {
      listenerInfo.timeoutId = setTimeout(() => {
        this.remove(tag, channel, callback);
      }, options.timeoutMs);
    }

    this.listeners.push(listenerInfo);

    const tagCount = this.listeners.filter(l => l.tag === tag).length;
    if (tagCount > this.maxListenersPerTag) {

    }
  }

  /**
   * 移除一个监听器
   */
  public remove(tag: string, channel: string, callback: IpcCallback): void {
    const index = this.listeners.findIndex(l => l.tag === tag && l.channel === channel && l.rawCallback === callback);

    if (index !== -1) {
      const listener = this.listeners[index];
      if (listener.timeoutId) {
        clearTimeout(listener.timeoutId);
      }
      ipc.removeListener(listener.channel, listener.callback);
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 按页面标签清除所有
   */
  public removeByTag(tag: string): void {
    const tagListeners = this.listeners.filter(l => l.tag === tag);
    for (const listener of tagListeners) {
      if (listener.timeoutId) {
        clearTimeout(listener.timeoutId);
      }
      ipc.removeListener(listener.channel, listener.callback);
    }
    this.listeners = this.listeners.filter(l => l.tag !== tag);
  }

  /**
   * 清除全部
   */
  public clearAll(): void {
    for (const listener of this.listeners) {
      if (listener.timeoutId) {
        clearTimeout(listener.timeoutId);
      }
      ipc.removeListener(listener.channel, listener.callback);
    }
    this.listeners = [];
  }

  /**
   * 生命周期辅助
   */
  public bindLifecycle(tag: string, unmountFn: () => void): () => void {
    const originalUnmount = unmountFn;

    const wrappedUnmount = () => {
      if (this.debugMode) {

      }
      this.removeByTag(tag);
      originalUnmount();
    };

    return wrappedUnmount;
  }
}

export default IpcListenerManager;
