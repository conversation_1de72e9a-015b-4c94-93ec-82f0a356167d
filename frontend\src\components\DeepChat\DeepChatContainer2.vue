<template>
  <div class="deep-chat-container">
    <div :class="{ 'conversation-section-container': true, hidden: !isConversationListVisible }">
      <el-card class="conversation-section" :body-style="{ padding: '10px' }" shadow="never">
        <div class="header">
          <el-button style="width: 100px; border-radius: 8px" type="primary" size="small"
            @click="createNewConversation">
            <el-icon>
              <Plus />
            </el-icon>
            新对话
          </el-button>
        </div>
        <el-scrollbar>
          <div class="conversation-list" :style="{ height: 'calc(100vh - 55px)' }">
            <el-tag v-for="conv in conversations" :key="conv.chatId"
              :effect="currentChatId === conv.chatId ? 'dark' : 'plain'" class="conversation-tag" closable
              @click="switchConversation(conv)" @close.stop="removeConversation(conv)">
              <el-input v-if="editingId === conv.chatId" v-model="conv.name" size="small" class="editable-input"
                @blur="finishEditing" @keyup.enter="finishEditing" />
              <template v-else>
                <span @dblclick="editConversation(conv)">{{
      conv.name.substring(0, 3) + (conv.name.length > 3 ? "..." : "")
    }}</span>
              </template>
            </el-tag>
          </div>
        </el-scrollbar>
      </el-card>
    </div>

    <div class="toggle-icon-container" @click="toggleConversationList">
      <font-awesome-icon :icon="['fas', 'list']" style="color: #efeff1" />
    </div>
    <div class="close-icon-container" @click="closeDialog">
      <font-awesome-icon :icon="['fas', 'close']" style="color: #efeff1" />
    </div>

    <div class="chat-wrapper">
      <deep-chat ref="deepChatRef" :key="currentChatId" :messageStyles="{
      default: {
        user: { bubble: { marginLeft: 0, maxWidth: '100%', borderRadius: '8px' } },
        ai: { bubble: { maxWidth: '100%', borderRadius: '8px' } }
      }
    }" :submitButtonStyles="{}" :avatars="{
      default: { styles: { position: 'left' } },
      ai: { src: aiAvatar, styles: { position: 'left' } }
    }" :inputAreaStyle="{
      position: 'fixed',
      top: '0',
      width: '440px',
      backgroundColor: 'rgba(0, 0, 0, 0.652)',
      borderRadius: '8px'
    }" :textInput="{
      styles: {
        text: { color: 'white' },
        container: { width: '85%', marginLeft: '4px', backgroundColor: '#717177', borderRadius: '8px' },
        focus: { border: '2px solid #a2a2ff' }
      },
      placeholder: { text: '请输入搜索内容', style: { color: 'white' } }
    }" :style="{
      top: '60px',
      border: 'none',
      position: 'fixed',
      width: '440px',
      left: '110px',
      borderRadius: '8px',
      height: 'calc(100vh - 60px)',
      backgroundColor: 'rgba(0, 0, 0, 0.652)'
    }" :history="currentMessages" :images="false" @new-message="onMessageAdded" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { Plus } from "@element-plus/icons-vue";
import "deep-chat";
import type { Message, Chat } from "@/stores/interface/chat";
import aiAvatar from "@/assets/images/logo.png";
import { useProcessChatStore } from "@/stores/modules/processChats";
import { useUserStore } from "@/stores/modules/user";
import { ipc } from "@/utils/ipcRenderer";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import { ipcApiRoute } from "@/ipc/ipcApi";
// import { fetchEventSource } from "@microsoft/fetch-event-source";
// Deep Chat API configuration
const userStore = useUserStore();
const currentUserId = computed(() => userStore.userInfo.pid);

const chatStore = useProcessChatStore();
const conversations = computed(() => chatStore.chats);
const editingId = ref<string | null>(null); // 当前正在编辑的会话索引
const deepChatRef = ref<HTMLElement | null>(null);
const currentChatId = computed(() => chatStore.currentChatId);
const currentMessages = computed(() => chatStore.currentChat);
const currentInfo = computed(() => chatStore.currentChatInfo);
const isConversationListVisible = ref(true);
function createNewConversation() {
  chatStore.createNewChat();
}
function switchConversation(chat: Chat) {
  chatStore.switchChat(chat.chatId);
}
function toggleConversationList() {
  isConversationListVisible.value = !isConversationListVisible.value;
}
// Removes a conversation
function removeConversation(chat: Chat) {
  chatStore.deleteChat(chat.chatId);
}
// Handles new messages from the chat
const onMessageAdded = (event: CustomEvent) => {
  chatStore.addMessage(event);
};

function requestInterceptor(requestDetails: any) {
  console.log("🚀 ~ requestInterceptor ~ requestDetails:", requestDetails.body.messages[0].text);
  return requestDetails;
}

async function responseInterceptor(response: any) {
  console.log("🚀 ~ responseInterceptor ~ response:", response);

  const reader = response.body?.getReader();
  const decoder = new TextDecoder("utf-8");

  if (!reader) return;

  let buffer = "";
  let allMessages = [];

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      // 解码数据块
      const chunk = decoder.decode(value, { stream: true });
      buffer += chunk;

      console.log("接收到数据块:", chunk);
      console.log("当前缓冲区:", buffer);

      // 尝试解析JSON对象
      let startIndex = 0;
      while (startIndex < buffer.length) {
        // 找到JSON对象的开始
        const openBrace = buffer.indexOf("{", startIndex);
        if (openBrace === -1) break;

        // 找到匹配的结束大括号
        let braceCount = 0;
        let endIndex = openBrace;

        for (let i = openBrace; i < buffer.length; i++) {
          if (buffer[i] === "{") braceCount++;
          if (buffer[i] === "}") braceCount--;
          if (braceCount === 0) {
            endIndex = i;
            break;
          }
        }

        // 如果找到完整的JSON对象
        if (braceCount === 0) {
          const jsonStr = buffer.substring(openBrace, endIndex + 1);
          console.log("解析JSON:", jsonStr);

          try {
            const jsonObj = JSON.parse(jsonStr);
            allMessages.push(jsonObj);
            // return jsonObj;
            console.log("成功解析:", jsonObj);
          } catch (e) {
            console.error("JSON解析错误:", e, "原始数据:", jsonStr);
          }

          startIndex = endIndex + 1;
        } else {
          // 没有找到完整的JSON对象，等待更多数据
          break;
        }
      }

      // 清理已处理的数据
      if (startIndex > 0) {
        buffer = buffer.substring(startIndex);
      }
    }

    // 处理最后剩余的数据
    if (buffer.trim()) {
      console.log("剩余缓冲区数据:", buffer);
      try {
        const jsonObj = JSON.parse(buffer.trim());
        allMessages.push(jsonObj);
        console.log("最后解析:", jsonObj);
      } catch (e) {
        console.error("最后的JSON解析错误:", e);
      }
    }
  } catch (error) {
    console.error("读取流时出错:", error);
  }

  console.log("所有消息:", allMessages);

  // 合并所有文本内容
  let combinedText = "";
  let htmlContent = "";
  let hasError = false;
  let errorMessage = "";

  for (const msg of allMessages) {
    if (msg.text) {
      combinedText += msg.text;
    }
    if (msg.html) {
      htmlContent += msg.html;
    }
    if (msg.error) {
      hasError = true;
      errorMessage = msg.error;
    }
  }

  // 构造最终响应
  const finalResponse = {};

  if (hasError) {
    finalResponse.error = errorMessage;
  } else {
    if (combinedText) {
      finalResponse.text = combinedText;
    }
    if (htmlContent) {
      finalResponse.html = htmlContent;
    }
  }

  console.log("最终响应:", finalResponse);
  return finalResponse;
}

// 开始编辑会话名称
function editConversation(chat: Chat) {
  editingId.value = chat.chatId;
}

// 完成编辑会话名称
function finishEditing() {
  editingId.value = null;
}

function closeDialog() {
  ipc.send(ipcApiRoute.closeFloatingDialog, 2);
}

onMounted(async () => {
  let { hongyan } = await ipc.invoke(ipcApiRoute.getConfig);
  if (!hongyan) {
    hongyan = {
      url: "http://*************:19653/api/eai/dz/chatMessage.do",
      agentName: "fina_assistant",
      esensign: "N8J9LGr62hQvW_nZZlq2jH4iEFURQML3yS2uUbSDcjqJmk9aXY6lFlZuU-M6aYoMj5tgnz2m0GsRPXs59MIErGTI-VdaAxYSzG9zNE1TskA=",
      esentoken: "06113e759989435f97db0d2833106a80"
    };
  }

  // Initialize with an empty conversation if none exists
  if (chatStore.chats.length === 0) {
    chatStore.createNewChat();
  }
  deepChatRef.value.stream = true;
  deepChatRef.value.connect = {
    stream: { streaming: true },
    handler: (body, signals) => {
      try {
        const queryString = params => new URLSearchParams(params).toString();

        const params = { conversation_id: currentInfo.value?.conversation_id || "", sfzId: currentUserId.value };
        const fetchParams = {
          agentName: hongyan.agentName,
          esensign: hongyan.esensign,
          esentoken: hongyan.esentoken,
          question: body.messages[0].text,
          params: JSON.stringify(params)
        };
        console.log("🚀 ~ onMounted ~ params:", params);

        const url = hongyan.url + `?${queryString(fetchParams)}`;
        console.log("🚀 ~ onMounted ~ url:", url);

        fetchEventSource(url, {
          async onopen(response) {
            console.log("🚀 ~ onMounted ~ response:", response);
            if (response.ok) {
              signals.onOpen(); // stops the loading bubble
            } else {
              signals.onResponse({ error: "error" }); // displays an error message
            }
          },
          onmessage(message) {
            const a = JSON.parse(message.data);
            chatStore.setCurrentInfo(a);
            console.log("🚀 ~ onmessage ~ a:", a);
            console.log("🚀 ~ onMounted ~ conversation_id:", a.conversation_id);
            signals.onResponse({ text: a.text }); // adds text into the message bubble
          },
          onerror(message) {
            console.log("🚀 ~ onerror ~ message:", message);
            signals.onResponse({ error: message }); // displays an error message
          },
          onclose() {
            console.log("🚀 ~ onerror ~ onclose:", "onclose");
            signals.onClose(); // The stop button will be changed back to submit button
          }
        });

        signals.stopClicked.listener = () => {
          // logic to stop your stream, such as creating an abortController
        };
      } catch (e) {
        signals.onResponse({ error: "error" + e }); // displays an error message
      }
    },
    responseInterceptor: response => { }
  };
  deepChatRef.value.responseInterceptor = response => {
    try {
      const raw = response.text?.data?.trim();
      if (!raw) return;

      // ✅ 检查是否为流结束标志
      if (raw === "[DONE]") {
        return { text: "" }; // 明确跳过，不返回内容
      }

      // ✅ 解析 JSON 数据
      const json = JSON.parse(raw);
      const delta = json?.choices?.[0]?.delta;

      if (delta?.content) {
        return { text: delta.content };
      }

      if (delta?.role === "assistant") {
        return { text: "" }; // 避免 Deep Chat 报错
      }

      return;
    } catch (err) {
      console.error("responseInterceptor 解析失败:", err);
    }
  };
});
</script>

<style lang="scss" scoped>
.deep-chat-container {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: 100%;
}

.conversation-section {
  border: none;
  background-color: transparent;
}

.conversation-section-container {
  // width: 26%;
  width: 100px;
  background-color: #000000a6;
  border-radius: 8px;
  box-sizing: border-box;
  height: 100%;
  transition: transform 0.3s ease-in-out;
}

.conversation-section-container.hidden {
  visibility: hidden;
  opacity: 0;
}

.toggle-icon-container {
  position: fixed;
  top: 15px;
  left: 118px;
  cursor: pointer;
  z-index: 1000;
}

.close-icon-container {
  position: fixed;
  top: 15px;
  right: 8px;
  cursor: pointer;
  z-index: 1000;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.conversation-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
  flex-wrap: nowrap;
}

.conversation-tag {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  border-radius: 8px;
  min-height: 24px;
  flex-shrink: 0;
}

.editable-input {
  width: 100px;
}

.chat-wrapper {
  flex: 1;
  box-sizing: border-box;
  background-color: "rgba(0, 0, 0, 0.652)";
}
</style>
@/stores/modules/knowledgeChats