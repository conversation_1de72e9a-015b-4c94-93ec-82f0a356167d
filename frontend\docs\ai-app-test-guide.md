# AI应用集成测试指南

## 测试目标

验证AI应用卡片与AI聊天组件的完整集成流程，确保：
1. 点击应用卡片能正确传递配置数据
2. AI聊天窗口能正确接收和渲染配置表单
3. 配置参数能正确传递给AI接口

## 测试环境准备

### 1. 启动开发环境
```bash
# 启动前端开发服务器
npm run dev

# 启动Electron应用
npm run electron:dev
```

### 2. 检查依赖
确保以下组件正常工作：
- IPC通信机制
- AI聊天窗口创建
- 配置数据传递

## 测试步骤

### 步骤1：验证AI应用卡片显示

1. 打开主界面
2. 找到"AI 微应用"组件
3. 确认显示以下应用卡片：
   - 青年交友
   - 智能伴读
   - 模型生成
   - 文件审查

**预期结果**：所有应用卡片正常显示，图标和名称正确

### 步骤2：测试配置数据传递

1. 打开浏览器开发者工具（F12）
2. 切换到Console标签页
3. 点击"青年交友"应用卡片

**预期控制台输出**：
```
打开 青年交友 应用: 青年社交交友平台
应用配置: {fields: Array(4)}
配置参数: {age: "90后", chatStyle: "友好", ageRange: "18-30", interests: "音乐,电影,旅行"}
准备传递给AI聊天组件的配置数据: {appInfo: {...}, config: {...}, configFields: [...]}
AI聊天窗口已打开，配置数据已传递
配置数据已通过IPC发送
```

### 步骤3：验证AI聊天窗口打开

1. 点击应用卡片后，应该自动打开AI聊天窗口
2. 窗口尺寸：480x640px
3. 窗口位置：屏幕居中
4. 自动打开开发者工具

**预期结果**：AI聊天窗口正常打开，开发者工具同时打开

### 步骤4：验证配置表单渲染

在AI聊天窗口中检查：

1. **应用信息显示**：
   - 标题显示"青年交友"
   - 欢迎消息显示应用名称

2. **配置模式激活**：
   - 自动进入配置模式
   - 显示配置表单区域

3. **表单字段渲染**：
   - "你的年龄"：选择框，默认选中"90后"
   - "聊天风格"：选择框，默认选中"友好"
   - "年龄范围"：选择框，默认选中"18-30"
   - "兴趣标签"：文本输入框，默认值"音乐,电影,旅行"

**预期结果**：所有字段正确渲染，默认值正确显示

### 步骤5：测试配置交互

1. **选择框测试**：
   - 点击"你的年龄"的不同选项
   - 确认选中状态正确切换

2. **文本框测试**：
   - 修改"兴趣标签"的内容
   - 确认输入正常

3. **模式切换测试**：
   - 点击模式切换按钮
   - 在配置模式和聊天模式间切换

**预期结果**：所有交互正常，状态正确更新

### 步骤6：测试配置发送

1. 在配置模式下，输入一些消息内容
2. 点击发送按钮（显示为勾号图标）
3. 观察聊天记录

**预期结果**：
- 显示配置信息的结构化消息
- 如果有输入内容，一起显示
- 自动切换回聊天模式

### 步骤7：测试其他应用

重复步骤2-6，测试其他应用：

#### 智能伴读应用
**配置字段**：
- 模型选择（select）
- 阅读类型（select）
- 阅读速度（select）
- 语言偏好（select）

#### 模型生成应用
**配置字段**：
- 模型选择（select）
- 生成类型（select）
- 创意程度（range，0-1，步长0.1）
- 输出长度（number）
- 生成风格（select）

#### 文件审查应用
**配置字段**：
- 模型选择（select）
- 审查类型（select）
- 审查严格度（select）
- 支持文件类型（text）
- 输出格式（select）

## 常见问题排查

### 问题1：AI聊天窗口不打开
**可能原因**：
- IPC通信失败
- Electron环境问题

**排查步骤**：
1. 检查控制台是否有IPC相关错误
2. 确认`ipcApiRoute.openAiChatWindow`是否正确定义
3. 检查Electron主进程是否正常运行

### 问题2：配置数据未传递
**可能原因**：
- window对象设置失败
- IPC消息发送失败

**排查步骤**：
1. 在控制台检查`window.aiAppConfig`是否存在
2. 检查IPC消息是否正确发送
3. 确认AI聊天组件是否正确监听消息

### 问题3：配置表单不显示
**可能原因**：
- 配置数据格式错误
- 组件状态异常

**排查步骤**：
1. 检查`hasConfigData`状态
2. 验证`configFields`数据结构
3. 确认组件是否正确接收配置数据

### 问题4：字段类型渲染错误
**可能原因**：
- 字段类型定义错误
- 渲染逻辑问题

**排查步骤**：
1. 检查字段的`type`属性
2. 验证range类型的min/max/step设置
3. 确认select类型的options数组

## 性能测试

### 测试指标
1. **窗口打开速度**：< 1秒
2. **配置数据传递延迟**：< 500ms
3. **表单渲染时间**：< 200ms
4. **交互响应时间**：< 100ms

### 测试方法
1. 使用浏览器Performance工具
2. 记录关键时间点
3. 多次测试取平均值

## 自动化测试

### 单元测试
```javascript
// 测试配置数据转换
describe('openApp function', () => {
  it('should convert app config correctly', () => {
    const mockApp = { /* 模拟应用数据 */ }
    const result = openApp(mockApp)
    expect(result.configFields).toBeDefined()
    expect(result.config).toBeDefined()
  })
})
```

### 集成测试
```javascript
// 测试完整流程
describe('AI App Integration', () => {
  it('should open chat window with config', async () => {
    // 模拟点击应用卡片
    // 验证窗口打开
    // 验证配置传递
  })
})
```

## 测试报告模板

### 测试结果记录
```
测试日期：____
测试环境：____
测试人员：____

功能测试结果：
□ 应用卡片显示正常
□ 配置数据传递正常
□ AI聊天窗口打开正常
□ 配置表单渲染正常
□ 配置交互正常
□ 配置发送正常

性能测试结果：
- 窗口打开速度：____ms
- 配置传递延迟：____ms
- 表单渲染时间：____ms

问题记录：
1. ____
2. ____

建议改进：
1. ____
2. ____
```

通过以上测试流程，可以全面验证AI应用卡片与AI聊天组件的集成功能是否正常工作。
