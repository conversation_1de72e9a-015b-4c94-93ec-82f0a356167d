<template>
  <div class="shortcut-applications">
    <h2>快捷应用</h2>
    <div class="content">
      <!-- 示例快捷应用图标 -->
      <div class="app-grid">
        <div v-for="(app, index) in apps" :key="index" class="app-item" @click="openApp(app)">
          <el-icon class="app-icon" :class="app.icon" />
          <span class="app-name">{{ app.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { ElIcon } from "element-plus";

interface App {
  name: string;
  icon: string;
  path: string;
}

const apps = ref<App[]>([
  { name: "文件管理器", icon: "folder", path: "/file-manager" },
  { name: "图片查看器", icon: "picture", path: "/image-viewer" },
  { name: "文本编辑器", icon: "edit", path: "/text-editor" },
  { name: "设置", icon: "setting", path: "/settings" }
]);

const openApp = (_app: App) => {
  // 打开应用逻辑
  // router.push(app.path);
};
</script>

<style scoped>
.shortcut-applications {
  padding: 20px;
  background-color: #fdfdfd;
  min-height: 100vh;
}

.content {
  margin-top: 20px;
}

.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 20px;
  padding: 20px;
}

.app-item {
  text-align: center;
  padding: 15px;
  border-radius: 10px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s;
}

.app-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.app-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

.app-name {
  font-size: 14px;
  color: #333;
}
</style>
