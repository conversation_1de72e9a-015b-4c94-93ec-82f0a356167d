/**
 * 主进程与渲染进程通信频道定义
 * Definition of communication channels between main process and rendering process
 */
export const ipcApiRoute = {
  // 悬浮球
  createFloatingBall: "controller.floating.createFloatingBall",
  createFloatingDialog: "controller.floating.createFloatingDialog",
  creatViewDialog: "controller.floating.createViewDialog",
  moveFloatingWin: "controller.floating.winMove",
  // movedFloatingWin: "controller.floating.ballMoved",
  clickFloatingDialog: "controller.floating.ballClick",
  leaveFloatingDialog: "controller.floating.ballLeave",
  closeFloatingDialog: "controller.floating.ballClose",
  getFloatingBall: "controller.floating.getFloatingBall",
  closeViewDialog: "controller.floating.closeViewDialog",

  // 本地工具
  dragToolInto: "controller.os.dragToolInto",
  openTool: "controller.os.openTool",
  openDirectory: "controller.os.openDirectory",
  selectFolder: "controller.os.selectFolder",
  setSoftwareList: "controller.os.setSoftwareList",
  getSoftwareList: "controller.os.getSoftwareList",

  // 窗口工具
  minimize: "controller.os.minimize",
  toggleMaximize: "controller.os.toggleMaximize",
  getMaximizeState: "controller.os.getMaximizeState",
  close: "controller.os.close",
  getWinId: "controller.os.getWCid",
  closeCfgWin: "controller.os.closeConfigWindow",

  trayNotification: "controller.os.trayNotification",

  // 搜索器
  search: "controller.everything.search",

  // 文件下载器相关
  addDownload: "controller.download.addDownload",
  downloadFileByStream: "controller.filemanager.downloadFile",

  downloadProgress: "controller.download.downloadProgress",
  downloadCompleted: "controller.download.downloadCompleted",
  downloadError: "controller.download.downloadError",
  downloadCancel: "controller.download.downloadCancel",

  getDownloadList: "controller.filemanager.getDownloadList",
  delDownloadFileById: "controller.filemanager.delDownloadFileById",

  // 使用本地浏览器打开链接
  openUrlByLocalBrowser: "controller.os.openUrlByLocalBrowser",
  // 应用重启
  relaunch: "controller.os.relaunch",

  // 自定义缓存
  setCache: "controller.db.setItem",
  getCache: "controller.db.getItem",
  removeCache: "controller.db.removeItem",
  // 系统配置
  getConfig: "controller.db.getAllConfigField",
  setConfig: "controller.db.saveConfigField",
  // 最近联系人
  getContactList: "controller.db.getContactList",
  addContact: "controller.db.addContact",
  removeContact: "controller.db.removeContact",
  removeAllContact: "controller.db.removeAllContact",
  updateContact: "controller.db.updateContact",
  syncContactData: "controller.db.syncContactData",
  // 个人消息
  getUserMessageList: "controller.db.getUserMessageList",
  addUserMessage: "controller.db.addUserMessage",
  updateUserMessage: "controller.db.updateUserMessage",
  removeUserMessage: "controller.db.removeUserMessage",
  // 群组消息
  getGroupMessageList: "controller.db.getGroupMessageList",
  addGroupMessage: "controller.db.addGroupMessage",
  updateGroupMessage: "controller.db.updateGroupMessage",
  
  // 闪记功能
  openQuickNoteWindow: "controller.note.openQuickNoteWindow",
  closeQuickNoteWindow: "controller.note.closeQuickNoteWindow",
  minimizeQuickNoteWindow: "controller.note.minimizeQuickNoteWindow",
  saveNote: "controller.note.saveNote",
  getAllNotes: "controller.note.getAllNotes",
  getNoteById: "controller.note.getNoteById",
  deleteNote: "controller.note.deleteNote",
  searchNotes: "controller.note.searchNotes",
  getNoteStats: "controller.note.getNoteStats",
  moveWindow: "controller.note.moveWindow",
  
  // 笔记管理窗口功能
  openNoteManagerWindow: "controller.note.openNoteManagerWindow",
  closeNoteManagerWindow: "controller.note.closeNoteManagerWindow",
  minimizeNoteManagerWindow: "controller.note.minimizeNoteManagerWindow",
  toggleMaximizeNoteManagerWindow: "controller.note.toggleMaximizeNoteManagerWindow",

  // AI聊天窗口功能
  openAiChatWindow: "controller.ai.openAiChatWindow",
  closeAiChatWindow: "controller.ai.closeAiChatWindow",
  removeGroupMessage: "controller.db.removeGroupMessage",
  // 清空个人数据
  clearLocalCache: "controller.db.clear",
  getPiniaState: "controller.db.getPiniaState",
  setPiniaState: "controller.db.setPiniaState"
};
