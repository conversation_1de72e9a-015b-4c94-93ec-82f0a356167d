<template>
  <div class="tab-content">
    <div class="tab-content-menu">
      <div class="flex flex-col w-full space-y-1">
        <button :class="{ active: chatType == 'organization' }" @click="changeType('organization')">
          <font-awesome-icon :icon="['fas', 'building']" class="h-4 w-4" />
          <span>组织架构</span>
        </button>
        <button :class="{ active: chatType == 'official' }" @click="changeType('official')">
          <font-awesome-icon :icon="['fas', 'landmark']" class="h-4 w-4" />
          <span>官方群组</span>
        </button>
        <button :class="{ active: chatType == 'friends' }" @click="changeType('friends')">
          <font-awesome-icon :icon="['fas', 'user']" class="h-4 w-4" />
          <span class="flex-1 text-left">我的好友</span>
          <font-awesome-icon
            :icon="['fas', 'plus-circle']"
            class="h-4 w-4 cursor-pointer text-gray-500"
            @click.stop="openSearch"
          />
        </button>
        <button :class="{ active: chatType == 'team' }" @click="changeType('team')">
          <font-awesome-icon :icon="['fas', 'user-friends']" class="h-4 w-4" />
          <span class="flex-1 text-left">我的团队</span>
          <font-awesome-icon
            :icon="['fas', 'plus-circle']"
            class="h-4 w-4 cursor-pointer text-gray-500"
            @click.stop="openTeamDialog"
          />
        </button>
        <button :class="{ active: chatType == 'group' }" @click="changeType('group')">
          <font-awesome-icon :icon="['fas', 'users']" class="h-4 w-4" />
          <span class="flex-1 text-left">我的群组</span>
          <font-awesome-icon
            :icon="['fas', 'plus-circle']"
            class="h-4 w-4 cursor-pointer text-gray-500"
            @click.stop="openGroupDialog"
          />
        </button>
      </div>
    </div>
    <div class="tab-content-br border-t border-gray-200 w-full"></div>
    <el-scrollbar>
      <div class="py-2">
        <OrgTree v-if="chatType == 'organization'" @node-click="handleNodeClick" />
        <FriendTree v-if="chatType == 'friends'" @node-click="handleNodeClick" :if-action="true" class="address-book" />
        <TeamTree v-if="chatType == 'team'" @node-click="handleNodeClick" class="address-book" />
        <GroupContact
          v-if="chatType == 'group' || chatType == 'official'"
          :user="groupList"
          class="book"
          @info-click="handleGroupClick"
        />
      </div>
    </el-scrollbar>
    <CreateGroup ref="dialog" />
    <CreateTeam ref="teamDialog" />
    <SearchFriend ref="searchlog" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { getChatGroup } from "@/api/modules/contact";
import OrgTree from "@/components/tree/org-tree.vue";
import FriendTree from "@/components/tree/friend-tree.vue";
import TeamTree from "@/components/tree/team-tree.vue";
import GroupContact from "@/components/ContactComp/GroupContact.vue";
import CreateGroup from "@/components/CreateGroup/index.vue";
import CreateTeam from "@/components/tree/CreateTeam.vue";
import SearchFriend from "@/components/SearchComp/SearchFriend.vue";

const dialog: any = ref(null);
const openGroupDialog = () => {
  if (dialog.value) dialog.value.openDialog();
};
const searchlog = ref<any>(null);
const openSearch = () => {
  if (searchlog.value) searchlog.value.openDialog();
};
const teamDialog: any = ref(null);
const openTeamDialog = () => {
  if (teamDialog.value) teamDialog.value.openDialog();
};

const chatType = ref("organization");
const group = ref([]);
const changeType = async (type: string) => {
  chatType.value = type;
  if (type == "group" || type == "official") {
    if (group.value.length == 0) {
      await getGroupList();
    }
  }
};
// 获取群组
const getGroupList = async () => {
  const params = {
    pageNo: 1,
    pageSize: -1
  };
  const res = await getChatGroup(params);
  group.value = res.data.list;
};
const groupList = computed(() => {
  if (chatType.value == "group") {
    return group.value;
  } else {
    return group.value.filter((item: any) => item.groupType == 1);
  }
});

const emit = defineEmits(["node-click", "info-click"]);

const handleGroupClick = (groupId: string) => {
  emit("info-click", groupId);
};
const handleNodeClick = (code: string) => {
  if (chatType.value == "organization") {
    emit("node-click", code, 1);
  } else if (chatType.value == "friends") {
    emit("node-click", code, 2);
  } else {
    emit("node-click", code, 3);
  }
};
</script>

<style scoped lang="scss">
.tab-content {
  height: calc(100vh - 88px);
  .tab-content-menu {
    @apply p-2 bg-white;
    button {
      @apply flex items-center gap-2 px-4 h-8 text-sm font-medium w-full transition-colors duration-200 rounded-md cursor-pointer text-gray-500 hover:bg-gray-100;
    }
    button.active {
      @apply bg-blue-100 text-blue-600;
    }
  }
}
.dark {
  .tab-content {
    .tab-content-menu {
      @apply bg-gray-800;
      button {
        @apply text-gray-400 hover:bg-gray-700;
      }
      button.active {
        @apply bg-blue-900 text-blue-400;
      }
    }
    .tab-content-br {
      @apply border-gray-700;
    }
  }
}
.el-scrollbar {
  height: calc(100vh - 300px);
}
</style>
