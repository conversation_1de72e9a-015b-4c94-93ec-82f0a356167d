<template>
  <el-dialog v-model="visible" :show-close="false" :close-on-click-modal="false" class="custom-dialog need-foot">
    <template #header>
      <button class="close-btn" @click="closeDialog">
        <font-awesome-icon :icon="['fas', 'times']" />
      </button>
      <div class="flex items-center gap-2">
        <font-awesome-icon :icon="['fas', 'users']" class="icon" />
        <span class="title">添加成员</span>
      </div>
    </template>
    <template #default>
      <div class="flex justify-between member">
        <el-tabs v-model="activeTab" class="custom-tabs">
          <el-tab-pane label="组织架构" name="1">
            <el-scrollbar height="240">
              <OrgTree :group-scope="groupInfo.groupScope" @node-click="handleClick" />
            </el-scrollbar>
          </el-tab-pane>
          <el-tab-pane label="我的好友" name="2">
            <el-scrollbar height="240">
              <FriendTree @node-click="handleClick" />
            </el-scrollbar>
          </el-tab-pane>
          <el-tab-pane label="我的团队" name="3">
            <el-scrollbar height="240">
              <TeamTree @node-click="handleClick" />
            </el-scrollbar>
          </el-tab-pane>
        </el-tabs>
        <el-transfer
          class="custom-transfer"
          v-model="transferValue"
          filterable
          filter-placeholder="搜索成员"
          :titles="['可选择人员', '已选择人员']"
          :button-texts="['移除', '添加']"
          :data="transferData"
          :props="transferProps"
        >
          <template #default="{ option }">
            <span>{{ option.name }}</span>
          </template>
        </el-transfer>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="AddGroupMember">
import { computed, ref } from "vue";
import { useTalkStore } from "@/stores/modules/talk";
import { useContactStore } from "@/stores/modules/friend";
import { useFriendStore } from "@/stores/modules/friends";
import OrgTree from "@/components/tree/org-tree.vue";
import FriendTree from "@/components/tree/friend-tree.vue";
import TeamTree from "@/components/tree/team-tree.vue";
import { getTeamMember } from "@/api/modules/team";
import { useOrgStore } from "@/stores/modules/org";

const talkStore = useTalkStore();
const contactStore = useContactStore();
const friendStore = useFriendStore();
const orgStore = useOrgStore();

const visible = ref(false);
const closeDialog = () => {
  visible.value = false;
};
const openDialog = () => {
  visible.value = true;
  transferValue.value = members.value.map((item: any) => item.member);
};
defineExpose({ openDialog, closeDialog });

const activeTab = ref("1");
const members = computed(() => contactStore.groupMembers[talkStore.activeChatId] || []);
const groupInfo = computed(() => contactStore.groupInfo[talkStore.activeChatId] || {});
const transferData = ref<any>([]);
const transferValue = ref<any>([]);

const handleClick = async (code: string) => {
  let newList: any[] = [];

  if (activeTab.value == "1") {
    if (!orgStore.orgUser[code] || orgStore.orgUser[code].length == 0) {
      await orgStore.getOrgUser({ orgCode: code });
    }
    newList = orgStore.orgUser[code];
  } else if (activeTab.value == "2") {
    newList = friendStore.friendList.filter((item: any) => item.status == "1" && item.groupId == code);
  } else {
    const res: any = await getTeamMember(code);
    if (res.code == 0) newList = res.data;
  }

  // 过滤当前 tab 的数据
  newList = newList.filter((item: any) => item.secretLevel >= groupInfo.value.secret);

  // 找出 transferValue 中的选中项，不在 newList 中的（来自旧数据）
  const selectedSet: any = new Set(transferValue.value);
  const preservedItems = transferData.value.filter(
    (item: any) => selectedSet.has(item.id) && !newList.some(n => n.id === item.id)
  );

  // 合并新数据和已选中的旧数据
  transferData.value = [...newList, ...preservedItems];
};

const transferProps = {
  key: "id",
  label: "name"
};

const emit = defineEmits(["add-member"]);

const handleConfirm = async () => {
  const arr = transferData.value.filter((item: any) => transferValue.value.includes(item.id));
  emit("add-member", arr);
};
</script>

<style lang="scss" scoped></style>
