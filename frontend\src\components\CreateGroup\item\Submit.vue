<template>
  <div class="group-info shadow border rounded-xl px-8 py-4">
    <el-scrollbar>
      <div class="info-title">基本信息</div>
      <div class="info-box bg-[#F9FAFB]">
        <div class="info-item">
          群组名称
          <span>{{ groupInfo.groupName }}</span>
        </div>
        <div class="info-item">
          群组密级
          <span v-if="groupInfo.secret">{{ SecretLevelConverter.fromNumber(groupInfo.secret, "obj") }}</span>
          <!-- <LevelBtn :message="groupInfo.secret"></LevelBtn> -->
        </div>
        <div class="info-item">
          群组类型
          <span>{{ groupInfo.groupType == "1" ? "官方群" : "普通群" }}</span>
        </div>
        <div class="info-item">
          成员范围
          <span v-if="groupInfo.groupScope == 'group_approve_0'">部门内</span>
          <span v-else-if="groupInfo.groupScope == 'group_approve_1'">跨部门</span>
          <span v-else-if="groupInfo.groupScope == 'group_approve_2'">跨场所</span>
          <span v-else-if="groupInfo.groupScope == 'group_approve_3'">跨单位</span>
          <span v-else="groupInfo.groupScope == 'group_approve_4'">跨集团</span>
        </div>
        <div class="info-item">
          归属项目
          <span>{{ groupInfo.belongProject }}</span>
        </div>
      </div>
      <div class="info-title">群组成员</div>
      <div class="info-box">
        <div v-for="member in groupInfo.members" class="flex items-center">
          <DynamicAvatar
            :id="member.id"
            :data-info="member"
            :relation-name="member.name"
            :online="member.online"
            :type-avatar="0"
            :size="32"
          />
          <div class="pl-3">
            <div class="mb-1 text-sm text-[#111827] font-medium">{{ member.name }}</div>
            <div class="text-xs text-[#6B7280]">{{ member.pathName || member.orgName }}</div>
          </div>
        </div>
      </div>
      <div class="info-title" v-if="groupInfo.approval && groupInfo.approval.length > 0">审批人</div>
      <div class="info-box" v-if="groupInfo.approval && groupInfo.approval.length > 0">
        <div v-for="member in groupInfo.approval" class="flex items-center">
          <DynamicAvatar
            :id="member.id"
            :data-info="member"
            :relation-name="member.name"
            :online="member.online"
            :type-avatar="0"
            :size="32"
          />
          <div class="pl-3">
            <div class="mb-1 text-sm text-[#111827] font-medium">{{ member.name }}</div>
            <div class="text-xs text-[#6B7280]">{{ member.pathName || member.orgName }}</div>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <div class="absolute bottom-0 right-0">
      <el-button type="default" @click="prev">上一步</el-button>
      <el-button type="primary" :loading="groupLoading" @click="create">创建</el-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="Submit">
import { useTalkStore } from "@/stores/modules/talk";
// import LevelBtn from '@/components/LevelBtn/index.vue'
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import { SecretLevelConverter } from "@/utils/secretLevelConverter";

const talkStore = useTalkStore();

const groupInfo = talkStore.createGroup;
const props = defineProps({
  groupLoading: {
    type: Boolean
  }
});
const emit = defineEmits(["prev-step", "next-step"]);
const prev = () => {
  emit("prev-step");
};
const create = () => {
  emit("next-step");
};
</script>

<style lang="scss" scoped>
.group-info {
  height: 280px;
}

.info-title {
  @apply text-[#374151] leading-5 mb-3;
}

.info-box {
  @apply px-4 pt-2 mb-3 border rounded-md;
}

.info-box > div {
  @apply mb-2;
}

.info-item {
  @apply w-1/2 odd:pr-4 even:pl-4 mb-4 inline-block text-[#4B5563] text-base leading-6;
}

.info-item span {
  @apply text-[#111827] font-medium float-right;
}
</style>
