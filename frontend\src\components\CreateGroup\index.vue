<template>
  <div class="dialog-box">
    <el-dialog v-model="isDialogVisible" :show-close="false" class="custom-dialog">
      <template #header>
        <button class="close-btn" @click="closeDialog">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'plus-circle']" class="icon" />
          <span class="title">创建研讨群</span>
        </div>
      </template>
      <div class="h-full rounded-xl relative">
        <el-steps :active="activeStep" finish-status="success" align-center>
          <el-step title="基本信息">
            <template #icon>
              <span class="text-xl">1</span>
            </template>
          </el-step>
          <el-step title="选择成员">
            <template #icon>
              <span class="text-xl">2</span>
            </template>
          </el-step>
          <el-step title="选择审批人" v-if="ifApproval">
            <template #icon>
              <span class="text-xl">3</span>
            </template>
          </el-step>
          <el-step title="创建群组">
            <template #icon>
              <span class="text-xl">{{ ifApproval ? "4" : "3" }}</span>
            </template>
          </el-step>
        </el-steps>
        <div class="step-box py-2 border-t border-gray-200">
          <basic-info v-if="activeStep == 0" @next-step="handleNextStep"></basic-info>
          <Member v-if="activeStep == 1" @prev-step="prev" @next-step="handleNextStep" />
          <Approval v-if="activeStep == 2 && ifApproval" @prev-step="prev" @next-step="handleNextStep" />
          <Submit :groupLoading="groupLoading" v-if="activeStep == 3 && ifApproval" @prev-step="prev" @next-step="create" />
          <Submit :groupLoading="groupLoading" v-if="activeStep == 2 && !ifApproval" @prev-step="prev" @next-step="create" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="CreateGroup">
import { ref, computed } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import { useRecentStore } from "@/stores/modules/recent";
import { saveGroup, saveGroupApprove } from "@/api/modules/contact";
import { useRouter } from "vue-router";
import BasicInfo from "./item/BasicInfo.vue";
import Member from "./item/Member.vue";
import Approval from "./item/Approval.vue";
import Submit from "./item/Submit.vue";
import { ContentType, MessageCode, MessageSendInfo } from "@/utils/websocket/messageInfo";
import { saveGroupMessage } from "@/api/modules/history";
import WebSocketClient from "@/utils/websocket/websocketClient";
import { ElMessage } from "element-plus";

const webSocketManager: any = WebSocketClient.getInstance();

const userStore = useUserStore();
const talkStore = useTalkStore();
const recentStore = useRecentStore();
const router = useRouter();

const groupInfo = computed(() => talkStore.createGroup);
const ifApproval = computed(() => (talkStore.createGroup.secret > 30 ? true : false));
const groupLoading = ref(false);
const isDialogVisible = ref(false);
const openDialog = async () => {
  activeStep.value = 0;
  isDialogVisible.value = true;
  talkStore.setNewGroup({});
  await talkStore.getApproveRole();
};
const closeDialog = () => {
  isDialogVisible.value = false;
};
defineExpose({ openDialog, closeDialog });

const activeStep = ref(0);

const prev = () => {
  if (activeStep.value-- < 0) activeStep.value = 0;
};

const next = async () => {
  if (activeStep.value++ > 3) activeStep.value = 3;
};

const handleNextStep = () => {
  next();
};

const create = async () => {
  groupInfo.value.memberList = groupInfo.value.members.map((item: any) => {
    return {
      member: item.id,
      memberName: item.name,
      memberRole: 0
    };
  });
  isDialogVisible.value = false;
  groupLoading.value = true;
  try {
    const res: any = await saveGroup(groupInfo.value);
    if (res.code == 0) {
      if (groupInfo.value.secret > 30) {
        let approveList: any = groupInfo.value.approval.map((item: any) => {
          return {
            userId: item.id,
            userName: item.name
          };
        });
        let userList: any = groupInfo.value.members.map((item: any) => {
          return {
            userId: item.id,
            userName: item.name
          };
        });
        let approve = {
          code: 3,
          data: {
            groupId: res.data.id,
            groupName: res.data.groupName,
            approveList: JSON.stringify(approveList),
            creator: res.data.groupOwnerId,
            creatorName: res.data.groupOwnerName,
            approveType: 300,
            type: 0,
            approveFlg: 0,
            userList: JSON.stringify(userList),
            levels: res.data.secret
          }
        };
        saveGroupApprove(approve).then(() => {
          ElMessage.success("已提交审批，请耐心等待！");
        });
      } else {
        let recentParams = {
          senderId: userStore.userId,
          contactId: res.data.id,
          chatType: res.data.groupType + 1,
          avatar: null,
          contactName: groupInfo.value.groupName,
          secret: groupInfo.value.secret
        };
        recentStore.addListRecents(recentParams).then(async _result => {
          const newMsg = {
            sender: userStore.userId,
            senderName: userStore.name,
            receiver: res.data.id,
            receiverName: res.data.groupName,
            secret: 30,
            msg: `${res.data.groupOwnerName} 创建了群组 ${res.data.groupName}`,
            msgType: ContentType.TIPS_TEXT
          };
          saveGroupMessage(newMsg).then(save => {
            let saveMsg = save.data;
            saveMsg.avatar = userStore.userInfo.avatar;
            // historyStore.setMsgHistory(saveMsg.receiver, saveMsg.id, saveMsg);
            recentStore.updateLastMsg(saveMsg.receiver, saveMsg.msg, saveMsg.id, saveMsg.msgType, saveMsg.updateTime);
            let msg: MessageSendInfo = {
              code: MessageCode.GROUP,
              data: {
                id: saveMsg.id,
                fromId: saveMsg.sender,
                toId: saveMsg.receiver,
                atId: [],
                isGroup: true,
                chatType: res.data.groupType + 1,
                contentId: "",
                content: {
                  msg: saveMsg.msg,
                  time: saveMsg.createTime,
                  secret: saveMsg.secret
                },
                contentType: ContentType.TIPS_TEXT
              }
            };
            webSocketManager.send(msg);
            talkStore.setActiveChat(saveMsg.receiver);
            talkStore.ifChat = true;
            ElMessage.success("创建成功");
            router.push({ name: "chat" });
          });
        });
      }
    }
  } finally {
    groupLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.dialog-box {
  :deep(.el-dialog) {
    height: 500px;
    @apply flex flex-col justify-between;

    .el-dialog__body {
      @apply flex-1;
    }

    .el-steps {
      @apply pb-1;
    }
  }
}

.dark {
  .step-box {
    @apply border-gray-700;
  }
}
</style>
