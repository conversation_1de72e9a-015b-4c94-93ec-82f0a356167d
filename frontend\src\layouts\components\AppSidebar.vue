<template>
  <el-aside>
    <div class="h-full flex flex-col justify-between overflow-y-auto overflow-x-hidden rounded py-8 px-3">
      <ul class="space-y-2 flex flex-col items-center">
        <li v-for="subItem in menuList as any" v-show="!subItem.meta?.isHide" :key="subItem.id || subItem.path">
          <el-tooltip effect="dark" placement="left" :content="subItem.meta.title">
            <div class="relative">
              <div
                class="router-item cursor-pointer"
                :class="{ 'active-item': currentPath === subItem.path }"
                @click="handleRouterClick(subItem)"
              >
                <component :is="getOfflineIcon(subItem.meta.icon)" class="router-icon" :style="getIconStyle(subItem.meta.icon)" />
              </div>
              <!-- 未读消息红点 -->
              <div
                v-if="unreadTotalNum > 0 && subItem.path === '/chat'"
                class="unread-dot absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white"
              ></div>
            </div>
          </el-tooltip>
        </li>
      </ul>
      <div class="flex items-center justify-center px-2">
        <el-dropdown trigger="click" placement="top" class="side-drop">
          <div class="relative rounded-full">
            <DynamicAvatar
              :id="userStore.userId"
              :data-info="userStore.userInfo"
              :relation-name="userStore.name"
              :typeAvatar="0"
              :size="32"
            />
            <div class="absolute top-0 left-0 bottom-0 right-0 z-10"></div>
          </div>
          <template #dropdown>
            <el-dropdown-menu class="side-drop-menu">
              <el-dropdown-item>
                <div>
                  <span class="block text-base font-medium">{{ userStore.name }}</span>
                  <span class="block truncate text-sm text-gray-500">
                    {{ userStore.userInfo.pathName || userStore.userInfo.orgName }}
                  </span>
                </div>
              </el-dropdown-item>
              <el-dropdown-item divided @click="pathToUser">
                <el-icon :size="16">
                  <UserFilled />
                </el-icon>
                <span>个人资料</span>
              </el-dropdown-item>
              <el-dropdown-item @click="pathToNotifications">
                <el-icon :size="16">
                  <Bell />
                </el-icon>
                <span>通知中心</span>
              </el-dropdown-item>
              <el-dropdown-item divided @click="logoutFn" class="logout">
                <el-icon :size="16">
                  <SwitchButton />
                </el-icon>
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <UserProfile ref="userProfile" />
      </div>
    </div>
  </el-aside>
</template>

<script setup lang="ts" name="Sidebar">
import { ref, watchEffect, h, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { logoutApi } from "@/api/modules/login";
import { useUserStore } from "@/stores/modules/user";
import { useAuthStore } from "@/stores/modules/auth";
import { useRecentStore } from "@/stores/modules/recent";
// import { useTalkStore } from "@/stores/modules/talk";
import { ElMessageBox, ElMessage } from "element-plus";
import { Icon } from "@iconify/vue";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import UserProfile from "@/components/ProfileComp/UserProfile.vue";
import { deleteAllData } from "@/utils/indexedDB";

// 导入离线图标
import homeBoldDuotone from "@iconify-icons/solar/home-bold-duotone";
import chatRoundLineBoldDuotone from "@iconify-icons/solar/chat-round-line-bold-duotone";
import usersGroupRoundedBoldDuotone from "@iconify-icons/solar/users-group-rounded-bold-duotone";
import bellBoldDuotone from "@iconify-icons/solar/bell-bold-duotone";
import menuDotsBoldDuotone from "@iconify-icons/solar/menu-dots-bold-duotone";
import settingsBoldDuotone from "@iconify-icons/solar/settings-bold-duotone";
import boxBoldDuotone from "@iconify-icons/solar/box-bold-duotone";
import widgetAddBoldDuotone from "@iconify-icons/solar/widget-add-bold-duotone";
import magicStick3BoldDuotone from "@iconify-icons/solar/magic-stick-3-bold-duotone";
import { useContactsStore } from "@/stores/modules/contacts";


const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const recentStore = useRecentStore()
const contactsStore = useContactsStore()
// const talkStore = useTalkStore();

const menuList = useAuthStore().authMenuListGet;

const currentPath = ref("");
const unreadTotalNum = ref(0);
watchEffect(() => {
  currentPath.value = route.path;
  unreadTotalNum.value = recentStore.listRecents.reduce((acc, curr) => acc + curr.unreadNum, 0);
});

const userProfile: any = ref(null);
const pathToUser = async () => {
  await userStore.getUserInfo();
  if (userProfile.value) userProfile.value.openDialog();
};
const pathToNotifications = () => {
  router.push("/notifications");
};

// 处理路由点击
const handleRouterClick = async (subItem: any) => {
  try {
    // 如果是聊天页面且已经在聊天页面，不需要重复处理
    if (subItem.path === "/chat" && route.path === "/chat") {
      return;
    }
    await router.push(subItem.path);

    // 如果是聊天页面，自动打开最近的聊天
    if (subItem.path === "/chat") {
      // 等待路由跳转完成后再处理聊天逻辑
      await nextTick();

      // 确保最近聊天数据已加载
      if (recentStore.listRecents.length === 0) {
        await recentStore.getListRecents();
      }

      // 如果有最近聊天记录且当前没有活跃聊天，自动打开第一个
      // if (recentStore.listRecents.length > 0 && !talkStore.ifChat) {
      //   const firstChat = recentStore.listRecents[0];

      //   // 🚀 立即响应：先切换UI状态
      //   talkStore.ifChat = true;
      //   talkStore.ifContact = false;
      //   talkStore.setActiveChat(firstChat.contactId);
      // }
    }
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};

// 离线图标映射
const offlineIconMap: Record<string, any> = {
  home: homeBoldDuotone,
  "comment-dots": chatRoundLineBoldDuotone,
  users: boxBoldDuotone, // 协同空间使用 box 图标
  "kiwi-bird": magicStick3BoldDuotone, // 百灵使用 magic-stick-3 图标
  "ellipsis-h": widgetAddBoldDuotone, // 更多使用 widget-add 图标
  cog: settingsBoldDuotone
};
// 获取离线图标组件
const getOfflineIcon = (iconStr: string) => {
  if (!iconStr) return h(Icon, { icon: homeBoldDuotone });

  // 解析FontAwesome格式 (fas:icon-name)
  const iconName = iconStr.includes(":") ? iconStr.split(":")[1] : iconStr;

  const iconData = offlineIconMap[iconName] || homeBoldDuotone;
  return h(Icon, { icon: iconData });
};
// 图标样式函数
const getIconStyle = (iconStr: string) => {
  if (!iconStr) return {};

  // 解析FontAwesome格式 (fas:icon-name)
  const iconName = iconStr.includes(":") ? iconStr.split(":")[1] : iconStr;

  return { color: "#3b82f6" };

  return {};
};
const questionBack = () => {
  // const screenWidth = window.screen.availWidth;
  // const screenHeight = window.screen.availHeight;
  const screenWidth = 1000;
  const screenHeight = 800;
  const url = import.meta.env.VITE_FEEDBACK_URL + "/?Pid=" + userStore.userInfo.pid;
  window.open(url, "_blank", "width=" + screenWidth + ",height=" + screenHeight + ",resizable=yes");
};
const logoutFn = () => {
  ElMessageBox.confirm("确定要退出登录吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      const res = await logoutApi();
      if (res.data) {
        let constactInfo: any = contactsStore.getCacheContactById(userStore.userId)

        if(constactInfo) {
          constactInfo.isOnline = false
          contactsStore.updateContact(constactInfo, ['isOnline'])
        }
        userStore.clearLocalStorage();
        deleteAllData();
        window.location.reload();
      }
    })
    .catch(() => {});
};
</script>

<style scoped lang="scss">
.el-aside {
  @apply h-full w-16 border-r border-gray-200 bg-gray-50;
  border-bottom-left-radius: 7px;

  .router-item {
    @apply w-12 h-12 flex items-center justify-center rounded-lg text-base font-medium text-gray-500 hover:bg-gray-100;
    pointer-events: auto !important;
    cursor: pointer !important;
    margin: 0 auto; /* 确保容器本身居中 */

    .router-icon {
      @apply h-8 w-8 flex-shrink-0 text-gray-600 transition duration-75 hover:text-gray-600;
      pointer-events: none;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .router-item.active-item {
    @apply bg-gray-100;

    .router-icon {
      @apply text-blue-500;
    }
  }

  /* 未读消息红点样式 */
  .unread-dot {
    animation: pulse-dot 2s infinite;
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }

  @keyframes pulse-dot {
    0% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
      transform: scale(1);
      box-shadow: 0 0 0 4px rgba(239, 68, 68, 0);
    }
    100% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
  }
}

.dark {
  .el-aside {
    @apply border-gray-700 bg-gray-800;

    .router-item {
      @apply text-white hover:bg-gray-700;

      .router-icon {
        @apply text-gray-400 hover:text-white;
      }
    }

    .router-item.active-item {
      @apply bg-gray-700;

      .router-icon {
        @apply text-blue-400;
      }
    }

    /* 暗色主题下的红点样式 */
    .unread-dot {
      @apply border-gray-800;
    }
  }
}
</style>
