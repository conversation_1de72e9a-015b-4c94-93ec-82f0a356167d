<template>
  <el-dropdown trigger="click" placement="top" ref="notice" popper-class="notices-popper">
    <slot :noticeNum="noticeNum"></slot>
    <template #dropdown>
      <div class="notices-container">
        <!-- 优化后的通知头部 -->
        <div class="notices-header">
          <div class="notices-header-content">
            <Icon :icon="notificationsRounded" class="notices-header-icon" />
            <span class="notices-header-title">系统通知</span>
            <div class="notices-count-badge" v-if="noticeNum > 0">{{ noticeNum }}</div>
          </div>
        </div>
        <!-- 空状态优化 -->
        <div v-if="notisList.length == 0" class="notices-empty">
          <div class="notices-empty-icon">
            <Icon :icon="notificationsOffRounded" />
          </div>
          <div class="notices-empty-text">暂无新通知</div>
        </div>

        <!-- 通知列表 -->
        <div v-else class="notices-list">
          <div v-for="item in notisList" :key="item.id" @click="gotoNotice(item)" class="notice-item">
            <!-- 通知图标 -->
            <div class="notice-icon">
              <Icon :icon="getNoticeIcon(item)" class="notice-icon-svg" />
            </div>

            <!-- 通知内容 -->
            <div class="notice-content">
              <template v-if="isDeptNotice(item)">
                <div class="notice-title">{{ item.title }}</div>
                <div class="notice-message" v-html="item.msg || item.content"></div>
              </template>
              <template v-else>
                <div class="notice-title">{{ approveType[item.title] }}</div>
                <div class="notice-message">{{ item.content }}</div>
              </template>
              <div class="notice-time">
                {{ dayjs(item.createTime).format("MM-DD HH:mm") }}
              </div>
            </div>

            <!-- 新通知指示器 -->
            <div v-if="item.status" class="notice-new-indicator"></div>
          </div>
        </div>
        <!-- 优化后的底部 -->
        <div class="notices-footer" @click="gotoMoreNotice">
          <Icon :icon="arrowForwardRounded" class="notices-footer-icon" />
          <span class="notices-footer-text">查看更多</span>
        </div>
      </div>
    </template>
  </el-dropdown>
</template>
<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useNoticeStore } from "@/stores/modules/notices.ts";
import { useRouter } from "vue-router";
import dayjs from "dayjs";
import { Icon } from "@iconify/vue";
// 导入离线图标
import notificationsRounded from "@iconify-icons/material-symbols/notifications-rounded";
import notificationsOffRounded from "@iconify-icons/material-symbols/notifications-off-rounded";
import arrowForwardRounded from "@iconify-icons/material-symbols/arrow-forward-rounded";
import campaignRounded from "@iconify-icons/material-symbols/campaign-rounded";
import groupAddRounded from "@iconify-icons/material-symbols/group-add-rounded";
import personAddRounded from "@iconify-icons/material-symbols/person-add-rounded";
import personRemoveRounded from "@iconify-icons/material-symbols/person-remove-rounded";
import infoRounded from "@iconify-icons/material-symbols/info-rounded";

const noticeStore = useNoticeStore();
const router = useRouter();

const notice: any = ref(null);
const approveType: any = {
  "300": "创建群组",
  "301": "增加成员",
  "302": "删除成员"
};

onMounted(async () => {
  if (noticeStore.noticeList.length == 0) {
    await noticeStore.getDefaultNotices();
  }
  if (noticeStore.approveList.length == 0) {
    await noticeStore.getApproveList();
  }
});

const notisList = computed(() => noticeStore.noticeList);
const noticeNum = computed(() => noticeStore.noticeCount + noticeStore.approveCount);
// 判断通知类型
const isDeptNotice = ({ noteType }: any) => noteType === "1";
// 根据通知类型跳转不同页面
const gotoNotice = (item: any) => {
  if (notice.value) notice.value.handleClose();
  if (item.content.includes("需要您审批")) {
    router.push("/notifications?type=0");
  } else {
    router.push("/notifications?type=1");
  }
};
const gotoMoreNotice = () => {
  if (notice.value) notice.value.handleClose();
  router.push("/notifications?type=1");
};
// 根据通知类型返回对应图标
const getNoticeIcon = (item: any) => {
  if (isDeptNotice(item)) {
    return campaignRounded; // 系统通知
  } else {
    const iconMap: any = {
      "300": groupAddRounded, // 创建群组
      "301": personAddRounded, // 增加成员
      "302": personRemoveRounded // 删除成员
    };
    return iconMap[item.title] || infoRounded;
  }
};
</script>
<style scoped lang="scss">
// 通知容器样式
.notices-container {
  width: 360px;
  max-height: 480px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 通知头部样式
.notices-header {
  flex-shrink: 0;
  background: rgba(249, 250, 251, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 16px 20px;

  .notices-header-content {
    display: flex;
    align-items: center;
    gap: 8px;

    .notices-header-icon {
      width: 20px;
      height: 20px;
      color: rgb(29, 78, 216);
    }

    .notices-header-title {
      font-size: 16px;
      font-weight: 600;
      color: rgb(17, 24, 39);
      flex: 1;
    }

    .notices-count-badge {
      background: rgb(239, 68, 68);
      color: white;
      font-size: 12px;
      font-weight: 600;
      padding: 2px 8px;
      border-radius: 12px;
      min-width: 20px;
      text-align: center;
    }
  }
}

// 通知列表容器
.notices-list {
  flex: 1;
  overflow-y: auto;

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}

// 通知项样式
.notice-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;

  &:hover {
    background: rgba(29, 78, 216, 0.04);
  }

  &:last-child {
    border-bottom: none;
  }

  .notice-icon {
    flex-shrink: 0;
    width: 36px;
    height: 36px;
    background: rgba(29, 78, 216, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;

    .notice-icon-svg {
      width: 18px;
      height: 18px;
      color: rgb(29, 78, 216);
    }
  }

  .notice-content {
    flex: 1;
    min-width: 0;

    .notice-title {
      font-size: 14px;
      font-weight: 600;
      color: rgb(17, 24, 39);
      margin-bottom: 4px;
      line-height: 1.4;
    }

    .notice-message {
      font-size: 13px;
      color: rgb(75, 85, 99);
      line-height: 1.5;
      margin-bottom: 6px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-word;
    }

    .notice-time {
      font-size: 12px;
      color: rgb(156, 163, 175);
      font-weight: 500;
    }
  }

  .notice-new-indicator {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 8px;
    height: 8px;
    background: rgb(239, 68, 68);
    border-radius: 50%;
    box-shadow: 0 0 0 2px white;
  }
}

// 空状态样式
.notices-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  .notices-empty-icon {
    width: 48px;
    height: 48px;
    background: rgba(156, 163, 175, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;

    svg {
      width: 24px;
      height: 24px;
      color: rgb(156, 163, 175);
    }
  }

  .notices-empty-text {
    font-size: 14px;
    color: rgb(156, 163, 175);
    font-weight: 500;
  }
}

// 底部样式
.notices-footer {
  flex-shrink: 0;
  background: rgba(249, 250, 251, 0.8);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  padding: 14px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(29, 78, 216, 0.05);

    .notices-footer-icon {
      transform: translateX(2px);
      color: rgb(29, 78, 216);
    }

    .notices-footer-text {
      color: rgb(29, 78, 216);
    }
  }

  .notices-footer-icon {
    width: 16px;
    height: 16px;
    color: rgb(75, 85, 99);
    transition: all 0.2s ease;
  }

  .notices-footer-text {
    font-size: 14px;
    font-weight: 500;
    color: rgb(75, 85, 99);
    transition: color 0.2s ease;
  }
}

// 深色主题适配
.dark {
  .notices-header {
    background: rgba(17, 24, 39, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);

    .notices-header-title {
      color: rgb(243, 244, 246);
    }

    .notices-header-icon {
      color: rgb(59, 130, 246);
    }
  }

  .notices-dropdown-menu {
    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .notice-item {
    border-bottom-color: rgba(255, 255, 255, 0.08);

    &:hover {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1));
    }

    .notice-icon {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(99, 102, 241, 0.15));
      border-color: rgba(59, 130, 246, 0.2);

      .notice-icon-svg {
        color: rgb(59, 130, 246);
      }
    }

    .notice-content {
      .notice-title {
        color: rgb(243, 244, 246);
      }

      .notice-message {
        color: rgb(156, 163, 175);
      }

      .notice-time {
        color: rgb(107, 114, 128);
      }
    }

    .notice-new-indicator {
      box-shadow:
        0 0 0 2px rgba(17, 24, 39, 0.8),
        0 2px 4px rgba(239, 68, 68, 0.3);
    }
  }

  .notices-empty {
    .notices-empty-icon {
      background: linear-gradient(135deg, rgba(75, 85, 99, 0.1), rgba(107, 114, 128, 0.1));

      svg {
        color: rgb(107, 114, 128);
      }
    }

    .notices-empty-text {
      color: rgb(107, 114, 128);
    }
  }

  .notices-footer {
    background: rgba(17, 24, 39, 0.95);
    border-top-color: rgba(255, 255, 255, 0.1);

    &:hover {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1));

      .notices-footer-icon {
        color: rgb(59, 130, 246);
      }

      .notices-footer-text {
        color: rgb(59, 130, 246);
      }
    }

    .notices-footer-icon {
      color: rgb(156, 163, 175);
    }

    .notices-footer-text {
      color: rgb(156, 163, 175);
    }
  }
}
</style>

<style lang="scss">
// 全局样式：重置 Element Plus dropdown 的默认样式
.notices-popper {
  padding: 0 !important;
  border: none !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
  border-radius: 12px !important;
  overflow: hidden !important;

  .el-dropdown-menu {
    padding: 0 !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }
}
</style>
