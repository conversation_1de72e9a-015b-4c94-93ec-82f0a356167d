/**
 * 日期格式化工具
 */

// 日期格式化
export function formatDate(date: Date | string | number, format: string = "YYYY-MM-DD"): string {
  if (!date) {
    return "";
  }

  // 如果是时间戳或字符串，转换为Date对象
  if (typeof date !== "object") {
    date = new Date(date);
  }

  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();
  const millisecond = date.getMilliseconds();

  // 替换格式字符串
  return format
    .replace(/YYYY/g, year.toString())
    .replace(/YY/g, (year % 100).toString().padStart(2, "0"))
    .replace(/MM/g, month.toString().padStart(2, "0"))
    .replace(/M/g, month.toString())
    .replace(/DD/g, day.toString().padStart(2, "0"))
    .replace(/D/g, day.toString())
    .replace(/HH/g, hour.toString().padStart(2, "0"))
    .replace(/H/g, hour.toString())
    .replace(/hh/g, (hour % 12 || 12).toString().padStart(2, "0"))
    .replace(/h/g, (hour % 12 || 12).toString())
    .replace(/mm/g, minute.toString().padStart(2, "0"))
    .replace(/m/g, minute.toString())
    .replace(/ss/g, second.toString().padStart(2, "0"))
    .replace(/s/g, second.toString())
    .replace(/SSS/g, millisecond.toString().padStart(3, "0"))
    .replace(/A/g, hour < 12 ? "AM" : "PM")
    .replace(/a/g, hour < 12 ? "am" : "pm");
}

// 相对时间格式化（例如：刚刚、5分钟前、1小时前等）
export function formatRelativeTime(date: Date | string | number): string {
  if (!date) {
    return "";
  }

  // 如果是字符串，转换为Date对象
  if (typeof date !== "object") {
    date = new Date(date);
  }

  const now = new Date();
  const diff = now.getTime() - date.getTime();

  // 小于1分钟
  if (diff < 60 * 1000) {
    return "刚刚";
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`;
  }

  // 小于24小时
  if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 60 * 1000))}小时前`;
  }

  // 小于30天
  if (diff < 30 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`;
  }

  // 小于12个月
  if (diff < 12 * 30 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (30 * 24 * 60 * 60 * 1000))}个月前`;
  }

  // 大于等于12个月
  return `${Math.floor(diff / (12 * 30 * 24 * 60 * 60 * 1000))}年前`;
}

// 格式化持续时间（例如：下载用时、剩余时间等）
export function formatDuration(milliseconds: number): string {
  if (milliseconds <= 0) {
    return "0秒";
  }

  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}天${hours % 24}小时${minutes % 60}分钟`;
  }

  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟${seconds % 60}秒`;
  }

  if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`;
  }

  return `${seconds}秒`;
}

// 计算两个日期之间的时间差
export function getDateDiff(date1: Date | string | number, date2: Date | string | number): number {
  if (!date1 || !date2) {
    return 0;
  }

  // 如果是字符串，转换为Date对象
  if (typeof date1 !== "object") {
    date1 = new Date(date1);
  }

  if (typeof date2 !== "object") {
    date2 = new Date(date2);
  }

  return Math.abs(date1.getTime() - date2.getTime());
}

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// 格式化下载速度
export function formatSpeed(bytesPerSecond: number): string {
  return formatFileSize(bytesPerSecond) + "/s";
}

// 计算预计剩余时间
export function calculateRemainingTime(bytesTotal: number, bytesLoaded: number, speed: number): string {
  if (speed === 0 || bytesLoaded >= bytesTotal) {
    return "0秒";
  }

  const remainingBytes = bytesTotal - bytesLoaded;
  const remainingTimeMs = (remainingBytes / speed) * 1000;

  return formatDuration(remainingTimeMs);
}

// 获取日期的年、月、日部分
export function getDateParts(date: Date | string | number): { year: number; month: number; day: number } {
  if (typeof date !== "object") {
    date = new Date(date);
  }

  return {
    year: date.getFullYear(),
    month: date.getMonth() + 1,
    day: date.getDate()
  };
}

// 获取时间的时、分、秒部分
export function getTimeParts(date: Date | string | number): { hour: number; minute: number; second: number } {
  if (typeof date !== "object") {
    date = new Date(date);
  }

  return {
    hour: date.getHours(),
    minute: date.getMinutes(),
    second: date.getSeconds()
  };
}

// 判断是否是同一天
export function isSameDay(date1: Date | string | number, date2: Date | string | number): boolean {
  const d1 = getDateParts(date1);
  const d2 = getDateParts(date2);

  return d1.year === d2.year && d1.month === d2.month && d1.day === d2.day;
}

// 判断是否是今天
export function isToday(date: Date | string | number): boolean {
  return isSameDay(date, new Date());
}

// 判断是否是昨天
export function isYesterday(date: Date | string | number): boolean {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return isSameDay(date, yesterday);
}

// 智能日期显示（今天显示时间，昨天显示"昨天"，其他显示日期）
export function smartDateDisplay(date: Date | string | number): string {
  if (isToday(date)) {
    return formatDate(date, "HH:mm:ss");
  }

  if (isYesterday(date)) {
    return "昨天 " + formatDate(date, "HH:mm:ss");
  }

  return formatDate(date, "YYYY-MM-DD HH:mm:ss");
}
