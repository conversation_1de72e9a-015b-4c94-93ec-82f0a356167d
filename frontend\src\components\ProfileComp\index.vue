<template>
  <div class="profile">
    <el-dialog v-model="dialogVisible" :show-close="false" width="400">
      <img src="@/assets/images/bg.png" class="not-img" alt="bg" />
      <div class="userInfo-top" v-if="profile?.id">
        <DynamicAvatar
          :id="profile?.id"
          :data-info="profile"
          :relation-name="profile.name"
          :online="profile?.online"
          :type-avatar="chatType"
          :size="80"
        />
        <div class="avatar-overlay"></div>
      </div>
      <div class="username p-4 text-xl font-semibold text-gray-900 border-gray-200">
        {{ chatType === 0 ? profile?.name : profile?.groupName }}
        <LevelBtn
          v-if="chatType == 0 ? profile?.secretLevel : profile?.secret"
          :message="chatType == 0 ? profile?.secretLevel : profile?.secret"
          :data-type="chatType == 0 ? 'user' : 'obj'"
        ></LevelBtn>
      </div>
      <div class="text-center" v-if="selectId != userStore.userId">
        <el-button class="messageBtn" @click="openChat">
          <font-awesome-icon :icon="['fas', 'comment-dots']" class="w-4 h-4 darkIcon" />
          <span class="ml-2 darkIcon">发送消息</span>
        </el-button>
      </div>
      <div class="p-4 space-y-6 text-base" v-if="chatType != 0">
        <el-descriptions :column="1" label-align="right">
          <el-descriptions-item label="群主：">{{ profile?.groupOwnerName }}</el-descriptions-item>
          <el-descriptions-item label="群组名称：">{{ profile?.groupName }}</el-descriptions-item>
          <el-descriptions-item label="群组公告：">{{ profile?.groupNotice }}</el-descriptions-item>
          <el-descriptions-item label="所属项目：">{{ profile?.belongProject }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="p-4 space-y-6 text-base" v-else>
        <el-descriptions :column="1">
          <el-descriptions-item label="组织：">{{ profile?.orgName }}</el-descriptions-item>
          <el-descriptions-item label="电话：">{{ profile?.otel }}</el-descriptions-item>
          <el-descriptions-item label="个性签名：">{{ profile?.description }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="Profile">
import { ref } from "vue";
import { getPersonInfo } from "@/api/modules/contact";
import DynamicAvatar from "../Avatar/DynamicAvatar.vue";
import { useContactStore } from "@/stores/modules/friend";
import { useTalkStore } from "@/stores/modules/talk";
import { useUserStore } from "@/stores/modules/user";
import { useRecentStore } from "@/stores/modules/recent";
import LevelBtn from "@/components/LevelBtn/index.vue";
import { useContactsStore } from "@/stores/modules/contacts";
import { ElMessage } from "element-plus";

const contactStore = useContactStore();
const usersStrore = useContactsStore();
const talkStore = useTalkStore();
const userStore = useUserStore();
const recentStore = useRecentStore();

const profile: any = ref({});
const selectId = ref("");
const chatType = ref(0);
const dialogVisible = ref(false);
const openProfile = async (id: string, type: any) => {
  selectId.value = id;
  let res: any = {};
  if (type) {
    chatType.value = type;
    if (contactStore.groupInfo[id]) {
      profile.value = contactStore.groupInfo[id];
    } else {
      await contactStore.getGroupInfo(id);
      profile.value = contactStore.groupInfo[id];
    }
  } else {
    chatType.value = 0;
    res = await getPersonInfo(id);
    if (res.code == 0) {
      profile.value = res.data;
    } else {
      ElMessage.error(res.msg)
      return
    }

    // 更新用户信息
    const { name, avatar, secretLevel, online } = res.data;
    usersStrore.updateContact(
      {
        id,
        name,
        avatar,
        secretLevel,
        isOnline: online == "on"
      },
      ["avatar", "isOnline"]
    );
  }
  
  dialogVisible.value = true;
};
defineExpose({ openProfile });

const openChat = async () => {
  let data = profile.value;
  const params = {
    senderId: userStore.userId,
    contactId: data.id,
    chatType: chatType.value,
    avatar: data.avatar,
    contactName: data.name ? data.name : data.groupName,
    secret: data.secretLevel ? data.secretLevel : data.secret
  };
  const res = await recentStore.addListRecents(params);
  if (res) {
    talkStore.setActiveChat(data.id);
    talkStore.ifContact = false;
    talkStore.ifChat = true;
    dialogVisible.value = false;
  }
};
</script>

<style scoped lang="scss">
.userInfo-top {
  position: relative;
  left: 20px;
  top: -40px;

  .avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 80px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 50%, rgba(255, 255, 255, 0) 100%);
    z-index: 2;
  }
}

.username {
  margin-top: -40px;
}

.profile {
  :deep(.el-dialog) {
    @apply rounded-lg;
    --el-dialog-padding-primary: 0;

    .el-dialog__header {
      display: none;
    }

    .el-input {
      @apply flex-1;
    }
  }

  img {
    width: 100%;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
  }
}

.messageBtn {
  width: 350px;
  height: 48px;
  border-radius: 1rem;
  background-color: #fcfafa;
}

.dark {
  .username {
    @apply text-white border-gray-700;
  }

  .darkIcon {
    @apply text-black;
  }
}
</style>
