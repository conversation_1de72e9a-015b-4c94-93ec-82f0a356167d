import { defineStore } from "pinia";
import { getContactInfoById } from "@/api/modules/contact";
import { ipc, isEE } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { ElMessage } from "element-plus";

export interface ContactsInfo {
  id: string;
  name: string;
  avatar: string;
  secretLevel: number;
  isOnline: boolean;
}
type ContactsInfoValueTypes = ContactsInfo[keyof ContactsInfo];

export const useContactsStore = defineStore("contacts", {
  state: () => ({
    ContactMap: new Map<string, ContactsInfo>()
  }),
  actions: {
    addContact(Contact: ContactsInfo) {
      if (!this.ContactMap.has(Contact.id)) {
        this.ContactMap.set(Contact.id, Contact);
        this.updateSqliteData();
      }
    },

    removeContact(id: string): void {
      this.ContactMap.delete(id);
    },

    updateContact(Contact: ContactsInfo, fields?: Array<keyof ContactsInfo>): void {
      let contactInfo = this.ContactMap.get(Contact.id) as Record<string, ContactsInfoValueTypes>;
      if (contactInfo && fields?.length) {
        if (contactInfo.isOnline != Contact.isOnline) {
          console.log(`${Contact.name}的登录状态更新为: ${Contact.isOnline ? '上线' : '下线'}`)
        }
        for (let field of fields) {
          if (field in contactInfo) {
            contactInfo[field] = Contact[field];
          }
        }
        this.updateSqliteData();
        return;
      } else {
        console.log(`${Contact.name}的登录状态更新为: ${Contact.isOnline ? '上线' : '下线'}`)
      }
      this.ContactMap.set(Contact.id, Contact);
      this.updateSqliteData();
    },

    updateSqliteData() {
      if (isEE) {
        const value = JSON.stringify(Array.from(this.ContactMap.entries()));
        ipc.send(ipcApiRoute.setPiniaState, ["contacts_ContactMap", value]);
      }
    },

    async getAllContacts(): Promise<ContactsInfo[]> {
      if (isEE) {
        const res = await ipc.invoke(ipcApiRoute.getPiniaState, "contacts_ContactMap");
        if (!res) return [];
        try {
          const contacts = JSON.parse(res);
          this.ContactMap = new Map(contacts);
        } catch (error: any) {
          ElMessage.error(error.message);
        }

        return res;
      }
      return Promise.resolve(Array.from(this.ContactMap.values()));
    },

    loadFromSessionStorage() {
      const storedData = sessionStorage.getItem("contacts");
      if (storedData) {
        const data = JSON.parse(storedData);
        this.ContactMap = new Map(data);
      }
    },
    saveToSessionStorage() {
      const serializedData = JSON.stringify(Array.from(this.ContactMap));
      sessionStorage.setItem("contacts", serializedData);
    },

    async getContactById(id: string, isGroup: boolean = false): Promise<ContactsInfo | undefined> {
      let contact = this.ContactMap.get(id);
      if (!contact) {
        const fetchedContact = await this.fetchContactInfo(id, isGroup);
        if (fetchedContact) {
          this.addContact(fetchedContact);
          contact = fetchedContact;
        }
      }
      return contact;
    },

    getCacheContactById(id: string): ContactsInfo | undefined {
      return this.ContactMap.get(id);
    },

    async fetchContactInfo(id: string, isGroup: boolean): Promise<ContactsInfo | undefined> {
      try {
        const response = await getContactInfoById(id, isGroup);

        return {
          ...response.data,
          id: response.data.id, // 保留前缀
          isOnline: isGroup ? false : response.data.isOnline == "on" // 群组无在线状态
        };
      } catch (error: any) {
        ElMessage.error(error.message);
        return undefined;
      }
    },

    queryContacts(query: Partial<ContactsInfo>): ContactsInfo[] {
      return Array.from(this.ContactMap.values()).filter(Contact => {
        return Object.keys(query).every(key => {
          return (Contact as any)[key] === (query as any)[key];
        });
      });
    },

    getPropertyById(id: string, property: keyof ContactsInfo): any {
      const Contact = this.ContactMap.get(id);
      return Contact ? Contact[property] : undefined;
    }
  },
  persist: {
    key: "lark-users-session",
    storage: sessionStorage,
    serializer: {
      serialize: state => {
        const mapData = Array.from(state.ContactMap.entries());
        return JSON.stringify(mapData);
      },
      deserialize: data => {
        const mapData = JSON.parse(data);
        const ContactMap = new Map();
        mapData.forEach(([key, value]: [string, any]) => {
          ContactMap.set(key, value);
        });
        return { ContactMap };
      }
    }
  }
});
