/**
 * 窗口动画工具类
 * 提供流畅的窗口操作动画效果
 */

export interface WindowAnimationOptions {
  duration?: number;
  easing?: string;
  scale?: number;
  opacity?: number;
}

export class WindowAnimations {
  private static defaultOptions: WindowAnimationOptions = {
    duration: 250,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    scale: 1,
    opacity: 1
  };

  /**
   * 创建窗口缩放动画
   */
  static createScaleAnimation(
    element: HTMLElement, 
    fromScale: number, 
    toScale: number, 
    options: WindowAnimationOptions = {}
  ): Promise<void> {
    const opts = { ...this.defaultOptions, ...options };
    
    return new Promise((resolve) => {
      element.style.transition = `transform ${opts.duration}ms ${opts.easing}`;
      element.style.transform = `scale(${fromScale})`;
      
      requestAnimationFrame(() => {
        element.style.transform = `scale(${toScale})`;
        
        setTimeout(() => {
          element.style.transition = '';
          resolve();
        }, opts.duration!);
      });
    });
  }

  /**
   * 创建窗口淡入淡出动画
   */
  static createFadeAnimation(
    element: HTMLElement,
    fromOpacity: number,
    toOpacity: number,
    options: WindowAnimationOptions = {}
  ): Promise<void> {
    const opts = { ...this.defaultOptions, ...options };
    
    return new Promise((resolve) => {
      element.style.transition = `opacity ${opts.duration}ms ${opts.easing}`;
      element.style.opacity = fromOpacity.toString();
      
      requestAnimationFrame(() => {
        element.style.opacity = toOpacity.toString();
        
        setTimeout(() => {
          element.style.transition = '';
          resolve();
        }, opts.duration!);
      });
    });
  }

  /**
   * 创建窗口弹性动画
   */
  static createBounceAnimation(
    element: HTMLElement,
    options: WindowAnimationOptions = {}
  ): Promise<void> {
    const opts = { ...this.defaultOptions, ...options };
    
    return new Promise((resolve) => {
      const keyframes = [
        { transform: 'scale(1)', offset: 0 },
        { transform: 'scale(1.1)', offset: 0.3 },
        { transform: 'scale(0.95)', offset: 0.6 },
        { transform: 'scale(1.02)', offset: 0.8 },
        { transform: 'scale(1)', offset: 1 }
      ];

      const animation = element.animate(keyframes, {
        duration: opts.duration,
        easing: opts.easing,
        fill: 'forwards'
      });

      animation.onfinish = () => resolve();
    });
  }

  /**
   * 创建窗口滑动动画
   */
  static createSlideAnimation(
    element: HTMLElement,
    direction: 'up' | 'down' | 'left' | 'right',
    distance: number = 20,
    options: WindowAnimationOptions = {}
  ): Promise<void> {
    const opts = { ...this.defaultOptions, ...options };
    
    const transforms = {
      up: `translateY(-${distance}px)`,
      down: `translateY(${distance}px)`,
      left: `translateX(-${distance}px)`,
      right: `translateX(${distance}px)`
    };

    return new Promise((resolve) => {
      element.style.transition = `transform ${opts.duration}ms ${opts.easing}`;
      element.style.transform = transforms[direction];
      
      requestAnimationFrame(() => {
        element.style.transform = 'translate(0, 0)';
        
        setTimeout(() => {
          element.style.transition = '';
          resolve();
        }, opts.duration!);
      });
    });
  }

  /**
   * 创建窗口脉冲动画
   */
  static createPulseAnimation(
    element: HTMLElement,
    intensity: number = 0.1,
    options: WindowAnimationOptions = {}
  ): Promise<void> {
    const opts = { ...this.defaultOptions, ...options };
    
    return new Promise((resolve) => {
      const keyframes = [
        { transform: 'scale(1)', opacity: '1', offset: 0 },
        { transform: `scale(${1 + intensity})`, opacity: '0.8', offset: 0.5 },
        { transform: 'scale(1)', opacity: '1', offset: 1 }
      ];

      const animation = element.animate(keyframes, {
        duration: opts.duration,
        easing: opts.easing,
        fill: 'forwards'
      });

      animation.onfinish = () => resolve();
    });
  }

  /**
   * 创建组合动画
   */
  static async createCombinedAnimation(
    element: HTMLElement,
    animations: Array<() => Promise<void>>
  ): Promise<void> {
    for (const animation of animations) {
      await animation();
    }
  }

  /**
   * 为窗口控制按钮添加反馈动画
   */
  static animateControlButton(
    button: HTMLElement,
    type: 'minimize' | 'maximize' | 'restore' | 'close'
  ): Promise<void> {
    const animationMap = {
      minimize: () => this.createSlideAnimation(button, 'down', 10, { duration: 200 }),
      maximize: () => this.createScaleAnimation(button, 1, 1.2, { duration: 150 }),
      restore: () => this.createBounceAnimation(button, { duration: 300 }),
      close: () => this.createPulseAnimation(button, 0.15, { duration: 200 })
    };

    return animationMap[type]();
  }

  /**
   * 创建窗口状态变化的视觉反馈
   */
  static createWindowStateIndicator(
    container: HTMLElement,
    state: 'minimizing' | 'maximizing' | 'restoring'
  ): void {
    // 移除现有的状态指示器
    const existingIndicator = container.querySelector('.window-state-indicator');
    if (existingIndicator) {
      existingIndicator.remove();
    }

    // 创建新的状态指示器
    const indicator = document.createElement('div');
    indicator.className = 'window-state-indicator';
    indicator.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(59, 130, 246, 0.9);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;
      z-index: 10000;
      pointer-events: none;
      animation: windowStateIndicatorFade 1.5s ease-out forwards;
    `;

    const stateText = {
      minimizing: '最小化中...',
      maximizing: '最大化中...',
      restoring: '还原中...'
    };

    indicator.textContent = stateText[state];
    container.appendChild(indicator);

    // 自动移除指示器
    setTimeout(() => {
      if (indicator.parentNode) {
        indicator.remove();
      }
    }, 1500);
  }
}

// 添加CSS动画到全局样式
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes windowStateIndicatorFade {
      0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
      }
      20% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }
      80% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }
      100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
      }
    }
  `;
  document.head.appendChild(style);
}

export default WindowAnimations; 