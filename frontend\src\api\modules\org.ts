import request from "@/api";

/**
 * 获取组织内联系人
 * @param {*} orgCode 组织编码
 * @param {*} roleCode 审批角色编码，可选
 */
export const getOrgUser = async (params: any) => {
  return await request.get("/admin-api/system/user/list", { params });
};
/**
 * 组织机构树形数据
 * @param {*} orgId 机构id/orgCode
 * @param {*} direction up/down
 * @param {*} level 机构层级
 */
export const getOrgData = async (params: any) => {
  return await request.get("/admin-api/system/org/tree", { params });
};
/**
 *
 * @param {*} orgId 机构id/orgCode
 * @param {*} direction up/down
 * @param {*} level 机构层级
 */
export const generateOrgByUserOrgId = async (params: any) => {
  return await request.get("/admin-api/system/org/generateOrgByUserOrgId", { params });
};
/**
 * 组织机构树形数据
 * @param {*} orgId 机构id/orgCode
 */
export const getOrgTreeByOrgId = async (orgId: any) => {
  return await request.get("/admin-api/system/org/getOrgTreeByOrgId?orgId=" + orgId);
};
