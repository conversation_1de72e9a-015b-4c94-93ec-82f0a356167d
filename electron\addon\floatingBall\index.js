// const Log = require("ee-core/log");
// const Conf = require("ee-core/config");
// const Addon = require("ee-core/addon");
// const path = require("path");
// const Ps = require("ee-core/ps");

// const { screen, ipcMain } = require("electron");

// const cfg = Conf.getValue("floatingBall");

// class FloatingBallAddon {
//   constructor() {
//     // this.create();
//   }

//   create() {
//     Log.info("[addon:FloatingBall] create");
//     const ballSize = 50;
//     const winWidth = ballSize;
//     const winHeight = ballSize;

//     const opt = {
//       width: winWidth,
//       height: winHeight,
//       type: "toolbar",
//       frame: false,
//       resizable: false,
//       show: false,
//       maximizable: false,
//       minimizable: false,
//       fullscreenable: false,
//       acceptFirstMouse: true,
//       transparent: true,
//       hasShadow: false,
//       alwaysOnTop: cfg.isBallAlwaysOnTop,
//       webPreferences: {
//         // preload,
//         devTools: true,
//       },
//     };

//     const FloatingBallWin = Addon.get("window").create("FloatingBall", opt);
//     const winContentsId = FloatingBallWin.webContents.id;

//     const [screenWidth, screenHeight] = [
//       screen.getPrimaryDisplay().workAreaSize.width,
//       screen.getPrimaryDisplay().workAreaSize.height,
//     ];
//     FloatingBallWin.setPosition(screenWidth - 160, screenHeight - 320);

//     const routeUrl = "#/desktop/floatingBall";
//     FloatingBallWin.loadURL("http://127.0.0.1:8080/" + routeUrl);
//     FloatingBallWin.webContents.openDevTools();
//     // if (Ps.isProd()) {
//     //   const mainServer = Conf.getValue('mainServer');
//     //   if (Conf.isFileProtocol(mainServer)) {
//     //     addr = mainServer.protocol + path.join(Ps.getHomeDir(), mainServer.indexPath);
//     //   } else {
//     //     addr = mainServer.protocol + mainServer.host + ':' + mainServer.port;
//     //   }
//     // }
//     // contentUrl = addr + content;
//     // const mainServer = Conf.getValue("mainServer");
//     // const addr =
//     // mainServer.protocol + path.join(Ps.getHomeDir(), mainServer.indexPath);
//     // if (process.env.VITE_DEV_SERVER_URL) {
//     //   FloatingBallWin.loadURL(url + routeUrl);
//     // } else {
//     // FloatingBallWin.loadFile(addr, { hash: routeUrl });
//     // }

//     const BallDialog = this.createDialog();

//     FloatingBallWin.on("ready-to-show", () => {
//       if (cfg.isBallShow) {
//         FloatingBallWin.show();
//       }
//     });

//     this.setPos = (x, y) => {
//       FloatingBallWin.setBounds({ x, y, width: winWidth, height: winHeight });
//     };

//     ipcMain.on("ball-move", (e, { x, y }) => {
//       this.setPos(x, y);
//     });

//     ipcMain.on("ball-moved", (e) => {
//       const [left, top] = FloatingBallWin.getPosition();

//       if (top <= 0) this.setPos(left, 0);
//       if (top > screenHeight - winHeight)
//         this.setPos(left, screenHeight - winHeight);
//       if (left <= 0) {
//         this.setPos(-winWidth / 2, top);
//         FloatingBallWin.webContents.send("adsorb-aside", "left");
//       }
//       if (left >= screenWidth - winWidth) {
//         this.setPos(screenWidth - winWidth / 2, top);
//         FloatingBallWin.webContents.send("adsorb-aside", "right");
//       }
//     });

//     ipcMain.on("ball-click", () =>
//       this.showOrHideSoftwareDialog(BallDialog, FloatingBallWin)
//     );

//     ipcMain.on("ball-leave", () => {
//       softwareDialog.hide();
//     });

//     FloatingBallWin.on("close", () => {
//       FloatingBallWin = null;
//     });

//     // return winContentsId;
//   }

//   createDialog() {
//     const opt = {
//       width: 600,
//       height: 400,
//       show: false,
//       frame: false, //要创建无边框窗口
//       resizable: false, //禁止窗口大小缩放
//       maximizable: false,
//       minimizable: false,
//       fullscreenable: false,
//       alwaysOnTop: true, //窗口是否总是显示在其他窗口之前
//       skipTaskbar: true,
//       transparent: true, //设置透明
//       hasShadow: false, //不显示阴影
//       webPreferences: {
//         // preload,
//         devTools: false, //关闭调试工具
//       },
//     };

//     const BallDialogWin = Addon.get("window").create("BallDialog", opt);
//     const routeUrl = "#/desktop/balldialog";
//     BallDialogWin.loadURL("http://127.0.0.1:8080/" + routeUrl);
//     BallDialogWin.on("blur", () => {
//       BallDialogWin.hide();
//     });

//     // 文件选择
//     ipcMain.handle("dialog:openFile", async (e, options = { type: "" }) => {
//       const { canceled, filePaths } = await dialog.showOpenDialog({
//         title: "选择文件",
//         properties: ["openFile", "multiSelections"],
//         ...options,
//       });

//       if (!canceled) {
//         if (options.type === "image") return getImageDetail(filePaths);
//         if (options.type === "file") return getFileDetail(filePaths);
//         return filePaths;
//       }
//     });

//     // 拖拽添加文件
//     ipcMain.handle("drag-file-into", (event, filePaths) => {
//       // return getFileDetail(filePaths)
//     });

//     // 打开文件
//     ipcMain.on("open-file", (event, filePath) => {
//       if (filePath) shell.openPath(filePath);
//     });

//     // 软件列表数据
//     ipcMain.on("set-category-list", (event, data) => {
//       // saveStore('categoryList', data)
//       // softwareDialog.webContents.send('refresh-page')
//     });
//     // 主进程
//     ipcMain.handle("get-category-list", async (event, someArgument) => {
//       const result = null;
//       return result;
//     });

//     return BallDialogWin;
//   }

//   showOrHideFloatingBall() {
//     if (!FloatingBallWin) return;

//     if (FloatingBallWin.isVisible()) {
//       FloatingBallWin.hide();
//     } else {
//       FloatingBallWin.show();
//       FloatingBallWin.setAlwaysOnTop(true, "screen-saver");
//     }
//   }

//   showOrHideSoftwareDialog(win, win1) {
//     if (!win) return;
//     const [screenWidth, screenHeight] = [
//       screen.getPrimaryDisplay().workAreaSize.width,
//       screen.getPrimaryDisplay().workAreaSize.height,
//     ];
//     const [left, top] = win1.getPosition();
//     const leftPos = left < screenWidth / 2 ? left + 80 : left - 630;
//     win.setPosition(leftPos, top - 50);

//     if (win.isVisible()) {
//       win.hide();
//     } else {
//       win.show();
//       win.setAlwaysOnTop(true, "screen-saver");
//     }
//   }
// }

// FloatingBallAddon.toString = () => "[class FloatingBallAddon]";
// module.exports = FloatingBallAddon;
