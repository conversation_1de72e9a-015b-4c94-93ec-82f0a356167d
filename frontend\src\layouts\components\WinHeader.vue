<template>
  <div class="win-header">
    <div>
      {{ route.meta.title }}
    </div>
    <div class="no-drag">
      <button class="control-btn close" @click="closeWindow" title="关闭">
        <font-awesome-icon :icon="['fas', 'xmark']" class="control-btn-icon" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { ipc } from "@/utils/ipcRenderer";

const props = defineProps<{
  targetWin: "main" | "config";
}>();

const route = useRoute();

const closeWindow = () => {
  if (props.targetWin == "main") {
    ipc.send(ipcApiRoute.close);
  } else {
    ipc.send(ipcApiRoute.closeCfgWin);
  }
};

defineExpose({
  closeWindow
});
</script>

<style lang="scss" scoped>
.win-header {
  -webkit-app-region: drag;
  @apply flex items-center justify-between select-none h-[40px] px-4 py-1 bg-white border-b border-gray-200;
}

.control-btn {
  @apply w-8 h-8 flex items-center justify-center rounded-lg p-1.5 hover:bg-gray-100;
  .control-btn-icon {
    @apply w-5 h-5 text-xl text-gray-600 hover:text-gray-800;
  }
}
.control-btn.close {
  @apply hover:bg-red-100;
  .control-btn-icon {
    @apply hover:text-red-600;
  }
}

.no-drag {
  -webkit-app-region: no-drag;
}
</style>
