<template>
  <div
    class="text-sm rounded-md cursor-pointer relative overflow-hidden flex items-center p-2"
    :class="[props.dataItem.sender == userStore.userId ? 'boxbgr' : 'boxbgl']"
    @click="togglePlay(props.dataItem.contentId)"
    :style="{ width: audioWidth + 60 + 'px' }"
  >
    <!-- <audio controls :src="downloadUrl" style="width: 250px"></audio> -->

    <!-- <button :class="{ 'is-playing': isPlaying }">
      {{ isPlaying ? "暂停" : "播放" }}
    </button> -->
    <audio ref="audioElement" :src="downloadUrl" @ended="handleEnded"></audio>
    <div class="flex items-center" v-if="props.dataItem.sender != userStore.userId">
      <div class="out">
        <div class="loader" :class="[{ animate: isPlaying }]"></div>
      </div>
      <p class="ml-2 text-gray-500" :style="{ width: audioWidth + 'px' }">{{ timeDataNum }}"</p>
    </div>
    <div v-else class="flex items-center">
      <p class="mr-2 text-black text-right" :style="{ width: audioWidth + 20 + 'px' }">{{ timeDataNum }}"</p>
      <div class="outs">
        <div class="loaders" :class="[{ animate: isPlaying }]"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import * as FileApi from "@/api/infra/file";
import { useUserStore } from "@/stores/modules/user";
const downloadUrl = ref("");
const timeDataNum = ref(0);
const userStore = useUserStore();

const audioElement = ref(null);
const isPlaying = ref(false);
const currentPlay = ref(-1);
const audioWidth = ref(0);

function handleEnded() {
  isPlaying.value = false;
  audioElement.value.currentTime = 0;
}
const togglePlay = currid => {
  currentPlay.value = props.currtIndex;
  audioElement.value.currentTime = 0;
  audioElement.value.load(); // 重新加载音频文件
  if (isPlaying.value) {
    audioElement.value.play();
  } else {
    audioElement.value.pause();
  }

  if (audioElement.value.paused) {
    audioElement.value.play();
    isPlaying.value = true;
  } else {
    audioElement.value.pause();
    isPlaying.value = false;
  }
};

function getAudioDuration(url) {
  return new Promise((resolve, reject) => {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    fetch(url)
      .then(response => response.arrayBuffer())
      .then(arrayBuffer => audioContext.decodeAudioData(arrayBuffer))
      .then(audioBuffer => resolve(audioBuffer.duration))
      .catch(error => reject(error));
  });
}

const props = defineProps({
  dataItem: {
    type: Object,
    required: true
  },
  currtIndex: {
    type: Number,
    required: true
  }
});
const downloadFun = async contentId => {
  const resMsg = await FileApi.getFileUrl({ id: contentId });
  if (resMsg.code == 0) {
    // downloadUrl.value = resMsg.data.url.split('?')[0];
    downloadUrl.value = resMsg.data?.url;
    getAudioDuration(downloadUrl.value)
      .then(duration => {
        timeDataNum.value = Math.floor(duration);
        audioWidth.value = timeDataNum.value + 50;
      })
      .catch(error => console.error("获取音频时长失败:", error));
  }
};

onMounted(() => {
  currentPlay.value = props.currtIndex;
  downloadFun(props.dataItem.contentId);
});
</script>
<style scoped lang="scss">
.boxbgl {
  background-color: #f3f4f6;
  justify-content: flex-start;
}
.boxbgr {
  background-color: #f3f4f6;
  justify-content: flex-end;
}
.out {
  display: flex;
  position: relative;
}

.out::before,
.out::after {
  content: "";
  display: block;
  background-color: #f3f4f6;
  position: absolute;
  width: 100%;
  height: 100%;
  clip-path: polygon(100% 0, 0 0, 0 50%);
  -webkit-clip-path: polygon(100% 0, 0 0, 0 50%);
  z-index: 9;
}
.out::after {
  left: 0;
  top: 0;
  -webkit-clip-path: polygon(100% 100%, 0 100%, 0 50%);
  clip-path: polygon(100% 100%, 0 100%, 0 50%);
  background-color: #f3f4f6;
}
.outs {
  display: flex;
  position: relative;
}

.outs::before,
.outs::after {
  content: "";
  display: block;
  background-color: #f3f4f6;
  position: absolute;
  width: 100%;
  height: 100%;
  clip-path: polygon(100% 0, 0 0, 100% 50%);
  -webkit-clip-path: polygon(100% 0, 0 0, 100% 50%);
  z-index: 9;
}
.outs::after {
  left: 0;
  top: 0;
  -webkit-clip-path: polygon(100% 100%, 0 100%, 100% 50%);
  clip-path: polygon(100% 100%, 0 100%, 100% 50%);
  background-color: #f3f4f6;
}
.loader {
  width: 15px;
  height: 30px;
  border-radius: 0 200px 200px 0;
  -webkit-mask: repeating-radial-gradient(farthest-side at left, #0000 0, #000 1px 18%, #0000 calc(18% + 1px) 33%);
  background: radial-gradient(farthest-side at left, #000 0 95%, #0000 0) left/0% 0% no-repeat #666;
}
.loaders {
  width: 15px;
  height: 30px;
  border-radius: 200px 0 0 200px;
  -webkit-mask: repeating-radial-gradient(farthest-side at right, #0000 0, rgb(29, 78, 216) 1px 18%, #0000 calc(18% + 1px) 33%);
  background: radial-gradient(farthest-side at right, rgb(29, 78, 216) 0 95%, #0000 0) right/0% 0% no-repeat #3b82f6;
}
.animate {
  animation: l10 1s infinite steps(4);
}
@keyframes l10 {
  100% {
    background-size: 120% 120%;
  }
}
</style>
