/**
 * 图标验证脚本
 * 检查所有图标文件是否正确生成并符合要求
 */

const fs = require('fs');
const path = require('path');

// 需要验证的图标文件
const requiredIcons = [
  // 构建用图标
  { path: 'build/icons/icon.ico', description: 'Windows 主图标 (ICO)' },
  { path: 'build/icons/icon.icns', description: 'macOS 主图标 (ICNS)' },
  { path: 'build/icons/icon.png', description: 'Linux 主图标 (PNG)' },
  { path: 'build/icons/favicon.ico', description: '网页图标 (ICO)' },
  
  // 各种尺寸的 PNG 图标
  { path: 'build/icons/16x16.png', description: '16x16 PNG 图标' },
  { path: 'build/icons/32x32.png', description: '32x32 PNG 图标' },
  { path: 'build/icons/64x64.png', description: '64x64 PNG 图标' },
  { path: 'build/icons/128x128.png', description: '128x128 PNG 图标' },
  { path: 'build/icons/256x256.png', description: '256x256 PNG 图标' },
  { path: 'build/icons/512x512.png', description: '512x512 PNG 图标' },
  
  // 应用程序运行时图标
  { path: 'public/images/logo-32.png', description: '窗口图标 (32x32)' },
  { path: 'public/images/tray.png', description: '托盘图标 (16x16)' },
  { path: 'public/images/<EMAIL>', description: '托盘图标高分辨率 (32x32)' },
  { path: 'public/images/tray_empty.png', description: '托盘闪烁图标 (16x16)' },
  { path: 'public/images/<EMAIL>', description: '托盘闪烁图标高分辨率 (32x32)' },
  
  // 深色主题图标
  { path: 'public/images/tray_dark.png', description: '深色主题托盘图标 (16x16)' },
  { path: 'public/images/<EMAIL>', description: '深色主题托盘图标高分辨率 (32x32)' },
  { path: 'public/images/tray_dark_empty.png', description: '深色主题托盘闪烁图标 (16x16)' },
  { path: 'public/images/<EMAIL>', description: '深色主题托盘闪烁图标高分辨率 (32x32)' },
  
  // 通知图标
  { path: 'public/images/notice/user_icon.png', description: '用户通知图标' },
  { path: 'public/images/notice/group_icon.png', description: '群组通知图标' },
  { path: 'public/images/notice/system_icon.png', description: '系统通知图标' },
];

// 配置文件验证
const configChecks = [
  {
    file: 'electron/config/builder.json',
    checks: [
      { path: 'win.icon', expected: 'build/icons/icon.ico', description: 'Windows 图标配置' },
      { path: 'mac.icon', expected: 'build/icons/icon.icns', description: 'macOS 图标配置' },
      { path: 'linux.icon', expected: 'build/icons/icon.png', description: 'Linux 图标配置' },
      { path: 'nsis.installerIcon', expected: 'build/icons/icon.ico', description: '安装程序图标配置' },
    ]
  },
  {
    file: 'electron/config/config.default.js',
    checks: [
      { pattern: /icon.*logo-32\.png/, description: '窗口图标配置' },
      { pattern: /icon.*tray\.png/, description: '托盘图标配置' },
      { pattern: /icon_dark.*tray_dark\.png/, description: '深色主题托盘图标配置' },
    ]
  }
];

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

/**
 * 获取文件大小
 */
function getFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
}

/**
 * 验证图标文件
 */
function verifyIcons() {
  console.log('🔍 验证图标文件...\n');
  
  let allPassed = true;
  const results = [];
  
  for (const icon of requiredIcons) {
    const exists = checkFileExists(icon.path);
    const size = exists ? getFileSize(icon.path) : 0;
    
    const result = {
      path: icon.path,
      description: icon.description,
      exists,
      size,
      status: exists && size > 0 ? '✅' : '❌'
    };
    
    results.push(result);
    
    if (!exists || size === 0) {
      allPassed = false;
    }
  }
  
  // 显示结果
  console.log('图标文件检查结果:');
  console.log('━'.repeat(80));
  
  for (const result of results) {
    const sizeStr = result.size > 0 ? `(${(result.size / 1024).toFixed(1)}KB)` : '';
    console.log(`${result.status} ${result.description}`);
    console.log(`   ${result.path} ${sizeStr}`);
  }
  
  return allPassed;
}

/**
 * 验证配置文件
 */
function verifyConfigs() {
  console.log('\n🔧 验证配置文件...\n');
  
  let allPassed = true;
  
  for (const config of configChecks) {
    console.log(`检查配置文件: ${config.file}`);
    
    if (!checkFileExists(config.file)) {
      console.log(`❌ 配置文件不存在: ${config.file}`);
      allPassed = false;
      continue;
    }
    
    const content = fs.readFileSync(config.file, 'utf8');
    
    for (const check of config.checks) {
      let passed = false;
      
      if (check.expected) {
        // 检查特定值
        passed = content.includes(check.expected);
      } else if (check.pattern) {
        // 检查正则模式
        passed = check.pattern.test(content);
      }
      
      const status = passed ? '✅' : '❌';
      console.log(`   ${status} ${check.description}`);
      
      if (!passed) {
        allPassed = false;
      }
    }
    
    console.log('');
  }
  
  return allPassed;
}

/**
 * 生成图标使用情况报告
 */
function generateReport() {
  console.log('📊 图标使用情况报告\n');
  
  console.log('图标用途分类:');
  console.log('━'.repeat(50));
  console.log('🖥️  应用程序窗口图标: public/images/logo-32.png');
  console.log('📱 系统托盘图标:');
  console.log('   • 浅色主题: public/images/tray.png');
  console.log('   • 深色主题: public/images/tray_dark.png');
  console.log('   • 高分辨率支持: @2x 版本');
  console.log('📦 安装程序图标: build/icons/icon.ico');
  console.log('🌐 网页图标: build/icons/favicon.ico');
  console.log('🔔 通知图标: public/images/notice/*.png');
  
  console.log('\n平台支持:');
  console.log('━'.repeat(50));
  console.log('🪟 Windows: ICO 格式 (多尺寸)');
  console.log('🍎 macOS: ICNS 格式');
  console.log('🐧 Linux: PNG 格式');
  
  console.log('\n主题支持:');
  console.log('━'.repeat(50));
  console.log('☀️  浅色主题: 标准图标');
  console.log('🌙 深色主题: 反色图标 (托盘)');
  console.log('📱 高分辨率: @2x 图标');
}

/**
 * 主函数
 */
function main() {
  console.log('🎨 图标验证工具\n');
  
  const iconsOk = verifyIcons();
  const configsOk = verifyConfigs();
  
  generateReport();
  
  console.log('\n' + '='.repeat(80));
  
  if (iconsOk && configsOk) {
    console.log('✅ 所有图标和配置验证通过！');
    console.log('🚀 应用程序图标系统已准备就绪');
  } else {
    console.log('❌ 发现问题，请检查上述错误');
    console.log('💡 运行 npm run icon 重新生成图标');
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = { main, verifyIcons, verifyConfigs };
