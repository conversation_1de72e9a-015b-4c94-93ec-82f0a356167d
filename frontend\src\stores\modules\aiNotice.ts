import { defineStore } from "pinia";
import { AiNoticeState, AiSummaryNotification } from "../interface";
import { getAiNoticeList, getNotificationStatistics } from "@/api/modules/ai/aiNotice";
import piniaPersistConfig from "../helper/persist";
import { formatRelativeTime } from "@/utils/formatTime";

export const useAiNoticeStore = defineStore("lark-ai-notice", {
  state: (): AiNoticeState => ({
    aiNoticeList: [],
    aiNotificationStats: []
  }),
  getters: {
    // 获取AI通知总数
    aiNotificationTotal: (state) => {
      return state.aiNotificationStats.reduce((total, stat) => total + stat.count, 0);
    },
    // 获取会议统计
    meetingStats: (state) => {
      console.log('🔍 meetingStats getter被调用');
      console.log('🔍 当前aiNotificationStats:', state.aiNotificationStats);
      const meetingStat = state.aiNotificationStats.find(stat => stat.type === 1);
      console.log('🔍 找到的会议统计:', meetingStat);
      const result = meetingStat ? meetingStat.count : 0;
      console.log('🔍 返回的会议数量:', result);
      return result;
    },
    // 获取公文统计
    documentStats: (state) => {
      const documentStat = state.aiNotificationStats.find(stat => stat.type === 2);
      return documentStat ? documentStat.count : 0;
    },
    // 获取邮件统计
    emailStats: (state) => {
      const emailStat = state.aiNotificationStats.find(stat => stat.type === 3);
      return emailStat ? emailStat.count : 0;
    },
    // 获取计划统计
    planStats: (state) => {
      const planStat = state.aiNotificationStats.find(stat => stat.type === 4);
      return planStat ? planStat.count : 0;
    }
  },
  actions: {
    // 获取智能通知列表
    async fetchAiNoticeList() {
      try {
        const { data } = await getAiNoticeList();

        const notificationList = data?.list || [];
        const mappedData = notificationList.map((item: AiSummaryNotification) => {
          let category = "通知"; // Default category
          let priority: "high" | "medium" | "low" = "medium";
          let icon = ["fas", "bell"]; // Default icon

          switch (item.type) {
            case 1:
              category = "会议";
              priority = "high";
              icon = ["fas", "calendar-alt"];
              break;
            case 2:
              category = "任务";
              priority = "medium";
              icon = ["fas", "tasks"];
              break;
            case 3:
              category = "邮件";
              priority = "medium";
              icon = ["fas", "envelope"];
              break;
            default:
              // For other types like '告警', we can just adjust priority/icon
              if (item.title.includes("告警")) {
                priority = "high";
                icon = ["fas", "triangle-exclamation"];
              }
              break;
          }

          return {
            ...item,
            time: formatRelativeTime(item.createTime),
            category,
            priority,
            icon
          };
        });

        this.aiNoticeList = mappedData;
      } catch (error) {
        console.error("Failed to fetch AI notice list", error);
        this.aiNoticeList = [];
      }
    },

    // 获取AI智能通知统计数据
    async fetchAiNotificationStats() {
      try {
        console.log('开始获取AI智能通知统计数据...');
        const result = await getNotificationStatistics();
        console.log('API响应:', result);
        console.log('result.code:', result?.code, 'result.data:', result?.data);

        if (result && result.code === 0 && result.data) {
          this.aiNotificationStats = result.data;
          console.log('✅ AI智能通知统计数据保存成功:', result.data);
          console.log('✅ 当前store中的aiNotificationStats:', this.aiNotificationStats);
        } else {
          console.warn('❌ 获取AI智能通知统计数据失败:', result?.msg || '未知错误');
          console.warn('❌ 条件检查: result存在?', !!result, 'code===0?', result?.code === 0, 'data存在?', !!result?.data);
          this.aiNotificationStats = [];
        }
      } catch (error) {
        console.error('❌ 获取AI智能通知统计失败:', error);
        this.aiNotificationStats = [];
      }
    }
  },
  persist: piniaPersistConfig("lark-ai-notice")
});
