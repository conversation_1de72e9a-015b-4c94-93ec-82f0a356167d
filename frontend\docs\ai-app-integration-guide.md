# AI应用集成指南

## 概述

本文档说明了如何将AI应用卡片与AI聊天组件进行集成，实现点击应用卡片时传递相应的配置参数进行表单渲染。

## 数据流程

```
AI应用卡片 → 点击事件 → 配置数据转换 → IPC通信 → AI聊天窗口 → 表单渲染
```

## 1. AI应用配置数据结构

### AiApp 接口
```typescript
interface AiApp {
  id: number           // 应用编号
  name: string         // 应用名称
  description: string  // 应用描述
  type: number         // 应用类型
  status: number       // 应用状态
  config?: AppConfig   // 应用配置
  icon: string | any   // 应用图标
  sort?: number        // 排序
  createTime: string   // 创建时间
}

interface AppConfig {
  fields: FormField[]  // 表单字段定义
}

interface FormField {
  label: string        // 字段显示标签
  name: string         // 字段名称
  class: string        // CSS样式类名
  value: string | number // 默认值
  type?: string        // 输入类型
  options?: Array<{label: string, value: string}> // 选项列表
}
```

## 2. 配置数据示例

### 青年交友应用
```javascript
{
  id: 1,
  name: '青年交友',
  description: '青年社交交友平台',
  config: {
    fields: [
      {
        label: '你的年龄',
        name: 'age',
        value: '90后',
        type: 'select',
        options: [
          { label: '70后', value: '1970' },
          { label: '80后', value: '1980' },
          { label: '90后', value: '1990' },
          { label: '00后', value: '2000' }
        ]
      },
      {
        label: '聊天风格',
        name: 'chatStyle',
        value: '友好',
        type: 'select',
        options: [
          { label: '友好', value: '友好' },
          { label: '幽默', value: '幽默' },
          { label: '正式', value: '正式' },
          { label: '随性', value: '随性' }
        ]
      }
    ]
  }
}
```

## 3. 点击处理逻辑

### openApp 函数流程

1. **配置数据转换**：将应用的 `config.fields` 转换为键值对对象
2. **字段格式转换**：转换为AI聊天组件期望的 `FormField` 格式
3. **数据传递**：通过多种方式传递配置数据

```typescript
const openApp = (app: AiApp) => {
  // 1. 转换配置值
  const configValues: Record<string, any> = {}
  if (app.config?.fields) {
    app.config.fields.forEach(field => {
      configValues[field.name] = field.value
    })
  }

  // 2. 转换字段格式
  const configFields = app.config?.fields?.map(field => ({
    label: field.label,
    name: field.name,
    class: field.class,
    value: field.value,
    type: field.type || 'text',
    ...(field.options && { options: field.options }),
    ...(field.type === 'range' && {
      min: 0,
      max: field.name === 'creativity' ? 1 : 100,
      step: field.name === 'creativity' ? 0.1 : 1
    })
  })) || []

  // 3. 准备完整配置数据
  const appConfigData = {
    appInfo: {
      id: app.id,
      name: app.name,
      description: app.description,
      type: app.type,
      status: app.status,
      icon: typeof app.icon === 'string' ? app.icon : 'mdi:robot',
      sort: app.sort || 0,
      createTime: app.createTime
    },
    config: configValues,
    configFields: configFields
  }

  // 4. 传递配置数据
  ;(window as any).aiAppConfig = appConfigData
  
  // 5. 打开AI聊天窗口
  ipc?.invoke(ipcApiRoute.openAiChatWindow)
    .then(() => {
      // 通过IPC再次发送配置数据
      setTimeout(() => {
        if (ipc) {
          ipc.send('ai-app-config', appConfigData)
        }
      }, 500)
    })
}
```

## 4. AI聊天组件接收配置

### 接收方式

1. **window对象**：`window.aiAppConfig`
2. **IPC消息**：监听 `ai-app-config` 事件

```typescript
// 组件挂载时接收配置
onMounted(() => {
  // 方式1: 从window对象接收
  if ((window as any).aiAppConfig) {
    receiveConfigData((window as any).aiAppConfig)
  }

  // 方式2: 监听IPC消息
  if (ipc) {
    ipc.on('ai-app-config', (_event: any, data: AppConfigData) => {
      receiveConfigData(data)
    })
  }
})
```

### 配置数据处理

```typescript
const receiveConfigData = (data: AppConfigData) => {
  appConfigData.value = data
  currentConfig.value = { ...data.config }
  hasConfigData.value = true

  // 处理配置字段
  if (data.configFields && data.configFields.length > 0) {
    configFields.value = data.configFields.map(field => ({
      ...field,
      value: data.config[field.name] || field.value
    }))
  }

  // 自动进入配置模式
  if (configFields.value.length > 0) {
    isConfigMode.value = true
  }
}
```

## 5. 表单渲染

### 支持的字段类型

1. **select（选择框）**：显示为标签按钮组
2. **range（滑块）**：显示为滑块控件
3. **number（数字）**：显示为数字输入框
4. **text（文本）**：显示为文本输入框

### 渲染逻辑

```vue
<!-- 选择类型字段 -->
<div v-for="field in selectFields" :key="field.name" class="config-tag-group">
  <span class="tag-label">{{ field.label }}</span>
  <div class="tag-options">
    <button
      v-for="option in field.options"
      :key="option.value"
      @click="updateConfigField(field.name, option.value)"
      class="tag-option"
      :class="{ active: field.value === option.value }"
    >
      {{ option.label }}
    </button>
  </div>
</div>

<!-- 滑块类型字段 -->
<div v-for="field in rangeFields" :key="field.name" class="config-range-group">
  <span class="range-label">{{ field.label }}</span>
  <div class="range-control">
    <input
      v-model.number="field.value"
      type="range"
      class="range-slider"
      :min="field.min || 0"
      :max="field.max || 1"
      :step="field.step || 0.1"
      @input="updateConfigField(field.name, Number($event.target.value))"
    />
    <span class="range-value">{{ field.value }}</span>
  </div>
</div>
```

## 6. 测试验证

### 测试步骤

1. 点击AI应用卡片
2. 检查控制台输出的配置数据
3. 验证AI聊天窗口是否正确打开
4. 确认配置表单是否正确渲染
5. 测试配置参数的修改和发送

### 调试信息

```javascript
// 在openApp函数中添加的调试信息
console.log('准备传递给AI聊天组件的配置数据:', appConfigData)
console.log('AI聊天窗口已打开，配置数据已传递')
console.log('配置数据已通过IPC发送')
```

## 7. 注意事项

1. **数据格式一致性**：确保配置数据格式与AI聊天组件期望的格式一致
2. **异步处理**：使用setTimeout确保窗口完全加载后再发送IPC消息
3. **错误处理**：添加适当的错误处理和用户反馈
4. **类型安全**：使用TypeScript接口确保类型安全

## 8. 扩展功能

### 添加新的字段类型

1. 在 `FormField` 接口中添加新的类型定义
2. 在AI聊天组件中添加对应的渲染逻辑
3. 在 `openApp` 函数中添加字段转换逻辑

### 支持更复杂的配置

1. 嵌套配置对象
2. 条件显示字段
3. 字段验证规则
4. 动态字段生成

这样就完成了AI应用卡片与AI聊天组件的完整集成，实现了点击应用卡片时传递相应配置参数进行表单渲染的功能。
