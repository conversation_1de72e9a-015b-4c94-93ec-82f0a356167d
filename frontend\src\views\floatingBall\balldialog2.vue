<template>
  <DragContainer name="dialogWin2">
    <DeepChat />
  </DragContainer>
</template>
<script lang="ts" setup>
// 导入 DeepChat 组件
import DeepChat from "@/components/DeepChat/DeepChatContainer2.vue";
import DragContainer from "./DragContainer.vue";
</script>

<style scoped>
.search-container {
  width: 100%;
  padding: 20px;
  background-color: #e9e1e1;
  border-radius: 20px;
  box-shadow: 0 2px 10px rgba(228, 226, 226, 0.1);
}

.search-icon {
  color: #999;
  margin-right: 8px;
}

.suggestions {
  margin-top: 10px;
  border-top: 1px solid #817f7f;
  padding-top: 10px;
}

.suggestion-item {
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.suggestion-item:hover {
  background-color: #c61010;
  border-radius: 10px;
  /* 圆角 */
}

.custom-input :deep(.el-input__wrapper) {
  background-color: transparent;
  /* 背景颜色 */
  width: 100%;
  /* 宽度 */
  height: 70px;
  border: 1px solid #ccc;
  /* 边框颜色 */
  border-radius: 20px;
  /* 圆角 */
  box-sizing: border-box;
}

.results {
  margin-top: 20px;
}

.result-item {
  padding: 15px;
  border-bottom: 1px solid #cc1414;
}

.result-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.result-description {
  font-size: 14px;
  color: #e20c0c;
  margin-bottom: 10px;
}

.result-tags {
  display: flex;
  gap: 8px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.shortcuts,
.common-entries {
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
</style>
