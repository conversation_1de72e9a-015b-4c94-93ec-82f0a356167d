/**
 * Markdown 转 Tiptap 编辑器内容的工具函数
 * 
 * 该工具将 markdown 格式的文本转换为 Tiptap 编辑器可以理解的 HTML 格式
 */

import MarkdownIt from 'markdown-it';
import { defaultMarkdownParser } from '@tiptap/pm/markdown';

// 初始化 markdown-it 解析器
const md = new MarkdownIt({
  html: true,          // 允许HTML标签
  breaks: true,        // 换行符转换为<br>
  linkify: true,       // 自动链接化URL
  typographer: true    // 启用智能引号等排版增强
});

/**
 * 使用 Tiptap 的 markdown parser 将 markdown 转换为 ProseMirror 节点
 * @param markdownText - markdown 格式的文本
 * @returns ProseMirror 文档节点的 JSON 表示
 */
export function markdownToTiptapJson(markdownText: string) {
  if (!markdownText || typeof markdownText !== 'string') {
    return null;
  }
  
  try {
    // 使用 Tiptap 的默认 markdown parser
    const doc = defaultMarkdownParser.parse(markdownText.trim());
    return doc?.toJSON();
  } catch (error) {
    console.warn('Tiptap markdown 解析失败，回退到 HTML 解析:', error);
    return null;
  }
}

/**
 * 将 markdown 文本转换为 Tiptap 编辑器可以识别的 HTML 格式
 * @param markdownText - markdown 格式的文本
 * @returns 转换后的 HTML 字符串
 */
export function markdownToTiptapHtml(markdownText: string): string {
  if (!markdownText || typeof markdownText !== 'string') {
    return '';
  }
  
  // 使用 markdown-it 将 markdown 转换为 HTML
  const html = md.render(markdownText.trim());
  
  // 对生成的 HTML 进行一些调整，使其更适合 Tiptap
  return processTiptapHtml(html);
}

/**
 * 处理转换后的 HTML，使其更适合 Tiptap 编辑器
 * @param html - 原始 HTML
 * @returns 处理后的 HTML
 */
function processTiptapHtml(html: string): string {
  let processedHtml = html;
  
  // 移除外层的 <p> 标签包装（如果整个内容只有一个段落）
  processedHtml = processedHtml.trim();
  if (processedHtml.startsWith('<p>') && processedHtml.endsWith('</p>') && processedHtml.split('</p>').length === 2) {
    processedHtml = processedHtml.slice(3, -4);
  }
  
  // 确保代码块使用正确的 class
  processedHtml = processedHtml.replace(
    /<pre><code class="language-(\w+)">/g,
    '<pre><code class="language-$1">'
  );
  
  // 处理表格，添加必要的样式类
  processedHtml = processedHtml.replace(
    /<table>/g,
    '<table class="tiptap-table">'
  );
  
  // 处理块引用，确保样式正确
  processedHtml = processedHtml.replace(
    /<blockquote>/g,
    '<blockquote class="tiptap-blockquote">'
  );
  
  return processedHtml;
}

/**
 * 检查文本是否包含 markdown 语法
 * @param text - 要检查的文本
 * @returns 是否包含 markdown 语法
 */
export function hasMarkdownSyntax(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }
  
  // 检查常见的 markdown 语法模式
  const markdownPatterns = [
    /^#{1,6}\s+/m,           // 标题 (# ## ### 等)
    /\*\*[^*]+\*\*/,         // 粗体 (**text**)
    /\*[^*]+\*/,             // 斜体 (*text*)
    /`[^`]+`/,               // 行内代码 (`code`)
    /```[\s\S]*?```/,        // 代码块 (```code```)
    /^\s*[-*+]\s+/m,         // 无序列表 (- * +)
    /^\s*\d+\.\s+/m,         // 有序列表 (1. 2.)
    /^\s*>\s+/m,             // 引用 (>)
    /\[([^\]]+)\]\(([^)]+)\)/, // 链接 [text](url)
    /!\[([^\]]*)\]\(([^)]+)\)/, // 图片 ![alt](url)
    /^\s*\|.*\|\s*$/m,       // 表格 (|col1|col2|)
    /^\s*-{3,}\s*$/m,        // 分隔线 (---)
  ];
  
  return markdownPatterns.some(pattern => pattern.test(text));
}

/**
 * 智能检测并转换内容
 * 如果内容包含 markdown 语法，则转换为 HTML
 * 否则直接返回原文本（用 p 标签包装）
 * @param content - 要处理的内容
 * @returns 处理后的 HTML 内容或 JSON 内容
 */
export function smartConvertContent(content: string): string | object {
  if (!content || typeof content !== 'string') {
    return '';
  }
  
  const trimmedContent = content.trim();
  
  // 如果包含 markdown 语法，进行转换
  if (hasMarkdownSyntax(trimmedContent)) {
    // 首先尝试使用 Tiptap 的 markdown parser
    const tiptapJson = markdownToTiptapJson(trimmedContent);
    if (tiptapJson) {
      return tiptapJson;
    }
    
    // 如果 Tiptap parser 失败，回退到 HTML 转换
    return markdownToTiptapHtml(trimmedContent);
  }
  
  // 否则直接用段落标签包装
  // 处理换行符，将多个换行转换为段落分隔
  const paragraphs = trimmedContent
    .split(/\n\s*\n/)  // 按双换行分割段落
    .map(p => p.replace(/\n/g, '<br>'))  // 单换行转为 <br>
    .filter(p => p.trim())  // 过滤空段落
    .map(p => `<p>${p.trim()}</p>`)  // 包装为段落
    .join('');
  
  return paragraphs || `<p>${trimmedContent}</p>`;
}

/**
 * 为 Tiptap 编辑器准备内容插入
 * @param content - 要插入的内容
 * @param position - 插入位置 ('end' | 'cursor' | 'replace')
 * @returns 格式化后的内容对象
 */
export function prepareTiptapContent(content: string, position: 'end' | 'cursor' | 'replace' = 'end') {
  const convertedContent = smartConvertContent(content);
  
  return {
    content: convertedContent,
    position,
    // 添加一些样式类来区分 AI 生成的内容
    className: 'ai-generated-content',
    // 标识内容类型
    isJson: typeof convertedContent === 'object',
    isHtml: typeof convertedContent === 'string' && convertedContent.includes('<')
  };
}
