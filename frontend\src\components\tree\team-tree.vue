<template>
  <div class="tree">
    <el-tree class="custom-tree" :data="teamData" :props="defaultProps" node-key="id" @node-click="handleNodeClick">
      <template #default="{ data }">
        <div class="team-group flex-1 flex items-center">
          <span class="flex-1">{{ data.teamName }}</span>
          <span v-if="data.id != 'default' && data.id != 'team'" class="actions flex items-center hidden" click.stop>
            <el-button link icon="circle-plus" @click.stop="openDialog(4, data)"></el-button>
            <el-button link icon="edit" @click.stop="openDialog(2, data)"></el-button>
            <el-button link icon="delete" @click.stop="openDialog(3, data)"></el-button>
          </span>
        </div>
      </template>
    </el-tree>
    <el-dialog v-model="dialogVisible" :show-close="false" class="custom-dialog need-foot">
      <template #header>
        <button class="close-btn" @click="dialogVisible = false">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'calendar']" class="icon" />
          <span class="title">{{ dialogInfo.title }}</span>
        </div>
      </template>
      <template #default>
        <div v-if="dialogInfo.type == 1">
          <el-input v-model="dialogInfo.teamName" placeholder="请输入团队名称"></el-input>
        </div>
        <div v-else-if="dialogInfo.type == 2">
          <el-input v-model="dialogInfo.teamName"></el-input>
        </div>
        <div v-else-if="dialogInfo.type == 3">确定删除团队 {{ dialogInfo.teamName }}</div>
        <div v-else>
          <div class="flex justify-between member">
            <el-tabs v-model="activeTab" class="custom-tabs">
              <el-tab-pane label="组织架构" name="1">
                <el-scrollbar height="240">
                  <OrgTree @node-click="handleClick" />
                </el-scrollbar>
              </el-tab-pane>
              <el-tab-pane label="我的好友" name="2">
                <el-scrollbar height="240">
                  <FriendTree @node-click="handleClick" />
                </el-scrollbar>
              </el-tab-pane>
            </el-tabs>
            <el-transfer
              class="custom-transfer"
              v-model="transferValue"
              filterable
              filter-placeholder="搜索成员"
              :titles="['可选择人员', '已选择人员']"
              :button-texts="['移除', '添加']"
              :props="transferProps"
              :data="transferData"
            >
              <template #default="{ option }">
                <span>{{ option.name }}</span>
              </template>
            </el-transfer>
          </div>
        </div>
      </template>
      <template #footer>
        <el-button type="default" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSure">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="TeamTree">
import { ref, onMounted, computed } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { useTeamStore } from "@/stores/modules/team";
import { useFriendStore } from "@/stores/modules/friends";
import * as teamApi from "@/api/modules/team";
import OrgTree from "./org-tree.vue";
import FriendTree from "./friend-tree.vue";
import { updateTeamMember } from "@/api/modules/team";
import { useOrgStore } from "@/stores/modules/org";

const userStore = useUserStore();
const teamStore = useTeamStore();
const friendStore = useFriendStore();
const orgStore = useOrgStore();

const teamData = computed(() => teamStore.teamList);

onMounted(async () => {
  await teamStore.getTeamList();
});

const defaultProps = {
  label: "teamName",
  children: "children",
  value: "id"
};

const dialogVisible = ref(false);
const dialogInfo: any = ref({
  type: 0,
  data: {},
  teamName: "",
  title: ""
});
const openDialog = (num: number, data: any) => {
  dialogVisible.value = true;
  dialogInfo.value.type = num;
  dialogInfo.value.data = data;
  dialogInfo.value.teamName = data.teamName;
  if (num == 1) {
    dialogInfo.value.title = "增加团队";
  } else if (num == 2) {
    dialogInfo.value.title = "修改团队";
  } else if (num == 3) {
    dialogInfo.value.title = "删除团队";
  } else {
    dialogInfo.value.title = "添加团队成员";
  }
};

const emit = defineEmits(["node-click"]);
const handleNodeClick = (data: any) => {
  emit("node-click", data.id);
};

const addGroup = async () => {
  const params = {
    userId: userStore.userId,
    teamName: dialogInfo.value.teamName,
    teamType: "1"
  };
  const res: any = await teamApi.addTeam(params);
  if (res.code == 0) {
    dialogVisible.value = false;
    await teamStore.getTeamList();
  }
};

const updateGroup = async () => {
  const params = {
    id: dialogInfo.value.data.id,
    userId: userStore.userId,
    teamName: dialogInfo.value.teamName,
    teamType: dialogInfo.value.data.teamType
  };
  const res: any = await teamApi.updateTeam(params);
  if (res.code == 0) {
    dialogVisible.value = false;
    await teamStore.getTeamList();
    teamStore.updateTeamName(dialogInfo.value.data.id, dialogInfo.value.teamName);
  }
};

const deleteGroup = async () => {
  const params = {
    id: dialogInfo.value.data.id
  };
  const res: any = await teamApi.deleteTeam(params);
  if (res.code == 0) {
    dialogVisible.value = false;
    teamStore.deleteTeam(params.id);
  }
};

const activeTab = ref("1");
const transferData = ref<any>([]);
const transferValue = ref<any>([]);
const transferProps = {
  key: "id",
  label: "name"
};
const handleClick = async (code: string) => {
  let newList: any[] = [];

  if (activeTab.value == "1") {
    if (!orgStore.orgUser[code] || orgStore.orgUser[code].length == 0) {
      await orgStore.getOrgUser({ orgCode: code });
    }
    newList = orgStore.orgUser[code];
    newList.sort((a: any, b: any) => {
      if (a.orderId === b.orderId) {
        return b.online - a.online; // 在线的优先，所以返回值相反
      } else {
        return a.orderId - b.orderId; // orderId 升序排列
      }
    });
  } else {
    newList = friendStore.friendList.filter((item: any) => item.status == "1" && item.groupId == code);
  }

  // 找出 transferValue 中的选中项，不在 newList 中的（来自旧数据）
  const selectedSet = new Set(transferValue.value);
  const preservedItems = transferData.value.filter(
    (item: any) => selectedSet.has(item.id) && !newList.some(n => n.id === item.id)
  );

  // 合并新数据和已选中的旧数据
  transferData.value = [...newList, ...preservedItems];
};

const addTeamMember = async () => {
  const params = {
    userId: userStore.userId,
    teamsId: dialogInfo.value.data.id,
    increaseId: transferValue.value
  };
  const res: any = await updateTeamMember(params);
  if (res.code == 0) {
    dialogVisible.value = false;
    emit("node-click", dialogInfo.value.data.id);
  }
};

const handleSure = () => {
  if (dialogInfo.value.type == 1) {
    addGroup();
  } else if (dialogInfo.value.type == 2) {
    updateGroup();
  } else if (dialogInfo.value.type == 3) {
    deleteGroup();
  } else {
    addTeamMember();
  }
};
</script>

<style scoped lang="scss">
.tree {
  :deep(.el-tabs) {
    .el-tabs__item {
      height: 40px;
    }
  }
}

.address-book .team-group:hover {
  .actions {
    @apply block;
  }
}
</style>
