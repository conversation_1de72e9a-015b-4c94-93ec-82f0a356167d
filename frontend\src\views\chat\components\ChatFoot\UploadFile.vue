<template>
  <div class="uploadbox">
    <el-upload
      method="post"
      multiple
      :action="uploadUrl"
      :on-progress="handleProgress"
      :before-upload="handleBeforeUpload"
      :show-file-list="false"
      :on-success="handleSuccess"
      :headers="headers"
      :data="objInfo"
      :on-error="handleErro"
      :accept="props.upType"
    >
      <slot :upload="changeSecret"></slot>
    </el-upload>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from "@/stores/modules/user";
import { useHistoryStore } from "@/stores/modules/history";
import { useTalkStore } from "@/stores/modules/talk";
import { useRecentStore } from "@/stores/modules/recent";
import { ref } from "vue";
import { ElMessage } from "element-plus";
import WebSocketClient from "@/utils/websocket/websocketClient";
import * as historyApi from "@/api/modules/history";
import { MessageCode, MessageSendInfo, ContentType } from "@/utils/websocket/messageInfo";
import { ipc, isEE } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";

const historyStore = useHistoryStore();
const userStore = useUserStore();
const talkStore = useTalkStore();
const recentStore = useRecentStore();

const props = defineProps({
  upType: {
    type: String
  },
  fileType: {
    type: Number
  }
});

const uploadUrl = import.meta.env.VITE_API_URL + "admin-api/infra/file/chunkedUpload";
const headers = ref({
  ContentType: "multipart/form-data",
  Authorization: "Bearer " + userStore.token
});

// 设置上传body参数
const objInfo = ref({
  fileId: ""
});

const secret = ref(30);
const changeSecret = (data: number) => {
  secret.value = data;
};

const emit = defineEmits(["model-value"]);
const handleBeforeUpload = (file: any) => {
  if (file.size > 500 * 1024 * 1024) {
    ElMessage.error("文件大小不能超过500MB");
    return false;
  }
  const typesToBlock = [".c", ".exe", ".dll", ".bat", ".iso", ".cmd", ".sh"];
  const fileName = file.name.toLowerCase();
  if (typesToBlock.some(type => fileName.endsWith(type))) {
    ElMessage.error("该类型文件可能存在风险，请打包后上传");
    return false;
  }
  const fileSize = file.size > 0;
  if (!fileSize) {
    ElMessage.error("上传文件不能为空");
  } else {
    let fInfo = {
      chatType: props.fileType,
      sender: userStore.userId,
      senderName: userStore.name,
      receiver: talkStore.activeChatId,
      receiverName: talkStore.activeContact.contactName,
      secret: secret.value,
      msg: file.name,
      msgType: 1,
      quoteId: "",
      atUserIds: "",
      contentId: ""
    };
    setShortHistory(file, fInfo);
    emit("model-value");
  }

  return fileSize;
};
const setShortHistory = (file: any, info: any) => {
  historyStore.addMsgBuffer(file.uid, info);
  objInfo.value.fileId = file.uid;
  let newMsg: any = {
    avatar: userStore.avatar,
    cancel: 0,
    createTime: new Date().getTime(),
    deleteFlag: 1,
    id: file.uid.toString(),
    msg: file.name,
    contentId: new Date().getTime(),
    msgType: 1,
    receiver: info.receiver,
    secret: info.secret,
    sender: info.sender,
    senderName: info.senderName,
    isTop: "0",
    online: "on",
    isRead: "1",
    progress: 0,
    proStatus: 0
  };
  historyStore.setMsgHistory(info.receiver, file.uid, newMsg);
};
const handleProgress = (event: any, file: any) => {
  let dataInfo = historyStore.msgBuffer[file.uid];
  let index = historyStore.msgHistory[dataInfo.receiver].findIndex((item: any) => item.id == file.uid);
  if (event.percent < 99) {
    historyStore.msgHistory[dataInfo.receiver][index].progress = parseInt(event.percent);
  }
  if (event.percent == 100) {
    historyStore.msgHistory[dataInfo.receiver][index].progress = 96;
  }
};
const webSocketManager = WebSocketClient.getInstance(); // 无需参数
const handleSuccess = async (resMsg: any, file: any) => {
  if (resMsg.code == 0) {
    if (resMsg.data.id == 0) {
      let dataInfo = historyStore.msgBuffer[file.uid];
      let index = historyStore.msgHistory[dataInfo.receiver].findIndex((item: any) => item.id == file.uid);
      historyStore.msgHistory[dataInfo.receiver][index].proStatus = 1;
      historyStore.deleteMsgBuffer(file.uid);
      ElMessage.error("文件上传失败");
      return;
    } else {
      let dataInfo = historyStore.msgBuffer[resMsg.data.fileId];
      if (dataInfo) {
        let index = historyStore.msgHistory[dataInfo.receiver].findIndex((item: any) => item.id == file.uid);
        historyStore.msgHistory[dataInfo.receiver][index].progress = 100;
        historyStore.msgHistory[dataInfo.receiver][index].proStatus = 0;
        dataInfo.contentId = resMsg.data.id;
        let res: any = {};
        if (dataInfo.chatType != 0) {
          res = await historyApi.saveGroupMessage(dataInfo);
        } else {
          res = await historyApi.saveUserMessage(dataInfo);
        }
        if (res.code == 0) {
          let addMsg: any = res.data;
          addMsg.fileId = resMsg.data.fileId;
          addMsg.chatType = dataInfo.chatType;
          historyStore.deleteMsgBuffer(resMsg.data.fileId, addMsg.receiver, addMsg.id);
          if (dataInfo.chatType == 0) {
            historyStore.updateHistory(addMsg.receiver, addMsg.fileId, addMsg);
            recentStore.updateLastMsg(addMsg.receiver, addMsg.msg, addMsg.id, addMsg.msgType, addMsg.updateTime);
          }
          recentStore.updateOrder(addMsg.receiver);
          let msg: MessageSendInfo = {
            code: dataInfo.chatType ? MessageCode.GROUP : MessageCode.PRAIVTE,
            data: {
              id: addMsg.id,
              fromId: addMsg.sender,
              toId: addMsg.receiver,
              atId: [],
              isGroup: dataInfo.chatType ? true : false,
              contentId: addMsg.contentId,
              chatType: dataInfo.chatType,
              content: {
                msg: addMsg.msg,
                time: addMsg.createTime,
                secret: addMsg.secret,
                fileId: addMsg.fileId,
                quoteSenderName: addMsg.quoteSenderName,
                quoteContent: addMsg.quoteContent,
                quoteMessageFile: addMsg.quoteMessageFile
              },
              contentType: addMsg.msgType
            }
          };
          webSocketManager.send(msg);
          talkStore.setStatus(true);

          // 存储消息数据到客户端
          if (isEE) {
            let ipcMsg = JSON.stringify(addMsg);
            if (dataInfo.chatType) {
              ipc.invoke(ipcApiRoute.addGroupMessage, ipcMsg);
            } else {
              ipc.invoke(ipcApiRoute.addUserMessage, ipcMsg);
            }
          }
        }
      }
    }
  } else {
    let dataInfo = historyStore.msgBuffer[file.uid];
    let index = historyStore.msgHistory[dataInfo.receiver].findIndex((item: any) => item.id == file.uid);
    historyStore.msgHistory[dataInfo.receiver][index].proStatus = 1;
    historyStore.deleteMsgBuffer(file.uid);
    ElMessage.error("文件上传失败!");
  }
};
const handleErro = (res: any) => {
  ElMessage.error(res.msg);
};
</script>
<style lang="scss" scoped>
.uploadbox {
  :deep(.el-upload) {
    @apply w-full;
  }
}
</style>
