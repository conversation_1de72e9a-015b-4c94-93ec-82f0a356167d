# AI应用配置字段示例

## 概述

AiAppWidget.vue中的每个AI应用现在都有一个结构化的配置对象，包含HTML5表单字段定义。每个配置字段都包含以下属性：

- `label`: 字段显示标签（中文）
- `name`: 字段名称
- `class`: CSS样式类名
- `value`: 默认值
- `type`: 输入类型（text, select, number, range等）
- `options`: 选项列表（用于select类型）

## 配置字段类型

### 1. 青年交友应用配置

```typescript
config: {
  fields: [
    {
      label: '模型选择',
      name: 'model',
      class: 'form-control',
      value: 'gpt-3.5-turbo',
      type: 'select',
      options: [
        { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },
        { label: 'GPT-4', value: 'gpt-4' },
        { label: 'Claude-3', value: 'claude-3' }
      ]
    },
    {
      label: '聊天风格',
      name: 'chatStyle',
      class: 'form-control',
      value: '友好',
      type: 'select',
      options: [
        { label: '友好', value: '友好' },
        { label: '幽默', value: '幽默' },
        { label: '正式', value: '正式' },
        { label: '随性', value: '随性' }
      ]
    },
    {
      label: '年龄范围',
      name: 'ageRange',
      class: 'form-control',
      value: '18-30',
      type: 'select'
    },
    {
      label: '兴趣标签',
      name: 'interests',
      class: 'form-control',
      value: '音乐,电影,旅行',
      type: 'text'
    }
  ]
}
```

### 2. 智能伴读应用配置

包含阅读类型、阅读速度、语言偏好等字段。

### 3. 模型生成应用配置

包含生成类型、创意程度、输出长度、生成风格等字段，支持文本、图像、代码、音频等多种生成类型。

### 4. 文件审查应用配置

包含审查类型、审查严格度、支持文件类型、输出格式等字段。

## 使用方式

当用户点击AI应用时，`openApp`函数会：

1. 提取应用的配置字段
2. 将配置字段转换为键值对对象
3. 将应用信息和配置参数传递给AI聊天窗口

```typescript
const openApp = (app: AiApp) => {
  // 将配置字段转换为键值对对象
  const configValues: Record<string, any> = {}
  if (app.config?.fields) {
    app.config.fields.forEach(field => {
      configValues[field.name] = field.value
    })
  }
  
  // 传递给AI聊天窗口
  ipc?.invoke(ipcApiRoute.openAiChatWindow, {
    appInfo: {
      id: app.id,
      name: app.name,
      description: app.description,
      type: app.type
    },
    config: configValues
  })
}
```

## 扩展配置

要添加新的配置字段，只需在对应应用的`config.fields`数组中添加新的字段对象即可。支持的输入类型包括：

- `text`: 文本输入
- `select`: 下拉选择
- `number`: 数字输入
- `range`: 滑块输入
- `checkbox`: 复选框
- `radio`: 单选按钮

这种结构化的配置方式使得每个AI应用都可以有自己独特的参数设置，提供更好的用户体验和功能定制能力。
