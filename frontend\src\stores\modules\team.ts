import { defineStore } from "pinia";
import piniaPersistConfig from "../helper/persist";
import * as teamApi from "@/api/modules/team";
import { useUserStore } from "./user";

interface Team {
  teamList: Array<any>;
  teamMember: Record<string, any>;
}

const userStore = useUserStore();

export const useTeamStore = defineStore("lark-team", {
  state: (): Team => ({
    teamList: [],
    teamMember: {}
  }),
  getters: {},
  actions: {
    // 获取团队
    async getTeamList() {
      const res: any = await teamApi.getTeamList(userStore.userId);
      if (res.code == 0) {
        this.teamList = res.data.list;
      }
    },
    // 新增团队
    async addTeam(params: any) {
      const res = await teamApi.addTeam(params);
      const obj = {
        id: res.data,
        teamName: params.teamName,
        teamType: params.teamType
      };
      this.teamList.push(obj);
    },
    // 删除团队
    deleteTeam(id: string) {
      const index = this.teamList.findIndex(item => item.id == id);
      this.teamList.splice(index, 1);
    },
    // 修改团队名称
    updateTeamName(id: string, name: string) {
      const index = this.teamList.findIndex((item: any) => item.id == id);
      this.teamList[index].teamName = name;
    },
    // 获取团队成员
    async getTeamMember(id: string) {
      const res: any = await teamApi.getTeamMember(id);
      if (res.code == 0) {
        this.teamMember[id] = res.data;
      }
    }
  },
  persist: piniaPersistConfig("lark-team")
});
