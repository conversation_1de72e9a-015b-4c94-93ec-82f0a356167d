const { Application } = require("ee-core");
const Log = require("ee-core/log");
const Addon = require("ee-core/addon");

// 新增：引入增量更新类
const IncrementalUpdater = require("./addon/autoUpdater/IncrementalUpdater");

class Index extends Application {
  constructor() {
    super();
    // this === eeApp;
    // 新增：创建增量更新实例
    this.incrementalUpdater = new IncrementalUpdater();
  }

  /**
   * core app have been loaded
   */
  async ready() {
    // do some things
    Log.info(12354);
  }

  /**
   * electron app ready
   */
  async electronAppReady() {
    // do some things
    // 新增：初始化自动更新
    // this.incrementalUpdater.init();
    // 检查更新
    this.incrementalUpdater.checkForUpdates();
  }

  /**
   * main window have been loaded
   */
  async windowReady() {
    // do some things
    // 延迟加载，无白屏
    const winOpt = this.config.windowsOption;
    if (winOpt.show == false) {
      const win = this.electron.mainWindow;
      // 设置整个窗口为可拖拽区域
      // win.setDraggable(true);
      win.once("ready-to-show", () => {
        win.show();
        win.focus();
      });
    }

    // 清空过期聊天数据的任务，延迟执行避免阻塞主线程
    setTimeout(() => {
      const MessageService = require("./service/database/message");
      // 清空用户信息
      new MessageService("user_message").clearExpiredMessage();
      // 清空群组信息
      new MessageService("group_message").clearExpiredMessage();
    }, 20000);
  }

  /**
   * before app close
   */
  async beforeClose() {
    // do some things
    Log.info("[Index] beforeClose");
    // 新增：软件退出时调用更新替换逻辑
    await this.incrementalUpdater.replaceExecutableOnShutdown();
  }
}

Index.toString = () => "[class Index]";
module.exports = Index;
