<template>
  <div class="dialog-box">
    <el-dialog v-model="visible" :show-close="false" :close-on-click-modal="false" class="custom-dialog">
      <template #header>
        <button class="close-btn" @click="closeDialog">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'book']" class="icon" />
          <span class="title">公文</span>
        </div>
      </template>
      <div>
        <el-form :model="formData" :rules="rules" require-asterisk-position="right" ref="ruleForm">
          <el-form-item label="公文名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入公文名称"></el-input>
          </el-form-item>
          <el-form-item label="公文描述" prop="description">
            <WangEditor :content="formData.description" @changeContent="changeContent" />
            <el-input
              v-model="formData.description"
              type="textarea"
              style="display: none"
              placeholder="请输入描述信息"
            ></el-input>
          </el-form-item>
          <el-form-item label="上传附件" prop="uploadfile">
            <el-upload
              class="w-full"
              ref="uploadRef"
              method="post"
              v-model:file-list="fileList"
              multiple
              :action="uploadUrl"
              :before-upload="handleBeforeUpload"
              :on-success="handleSuccess"
              :headers="headers"
              :data="objInfo"
              :on-error="handleErro"
            >
              <div class="select-secret">
                <el-select size="small" v-model="uploadFileSecret">
                  <el-option v-for="item in levelList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </div>
              <template #trigger>
                <el-button size="small" type="primary">选择文件</el-button>
              </template>
              <template #file="{ file }">
                <div class="file-list pl-1">
                  <el-icon :size="16" color="#409EFF">
                    <Link />
                  </el-icon>
                  <LevelBtn class="ml-1" :message="(file as any).secret" v-if="(file as any).secret"></LevelBtn>
                  <span class="mr-2 ml-2">{{ file.name }} </span>
                  <template v-if="file.status == 'success' || file.status == 'fail'">
                    <label class="icon-upload">
                      <el-icon class="icon-success" v-if="file.status == 'success'" size="16" color="#02c626">
                        <SuccessFilled />
                      </el-icon>
                      <el-icon class="icon-delete" size="16" color="#c60202" @click="handleRemove(uploadRef, file)">
                        <RemoveFilled />
                      </el-icon>
                    </label>
                  </template>
                  <template v-else>
                    <label class="icon-upload"
                      ><el-icon class="is-loading">
                        <Loading /> </el-icon
                      >{{ file.percentage == 100 ? 99 : file.percentage }}%</label
                    >
                  </template>
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
        <div class="bottom-0 right-0">
          <el-button type="primary" :loading="docLoading" @click="submitForm(ruleForm, fileList)">提交</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, watchEffect } from "vue";
import { useTalkStore } from "@/stores/modules/talk";
import { useUserStore } from "@/stores/modules/user";
import { ElMessage, FormInstance, UploadFile, UploadInstance, UploadRawFile } from "element-plus";
import { officialDocCreate } from "@/api/modules/contact";
import { SecretLevelConverter } from "@/utils/secretLevelConverter";
import { ContentType } from "@/utils/websocket/messageInfo";
import WangEditor from "@/components/Editor/Index.vue";
import LevelBtn from "@/components/LevelBtn/index.vue";
import { SuccessFilled } from "@element-plus/icons-vue";

const talkStore = useTalkStore();
const userStore = useUserStore();

const visible = ref(false);
const docLoading = ref(false);
interface Respnone {
  code: number;
  data: {
    id: string;
  };
}

interface UploadFileVo extends UploadFile {
  secret: number;
}

const formData = ref<{
  name: string;
  description: string;
  businessId: string;
  messageOfficialParamFileList: {
    fileId: string;
    fileName: string;
    secret: number;
  }[];
}>({
  name: "",
  description: "",
  messageOfficialParamFileList: [],
  businessId: ""
});

watchEffect(() => {
  formData.value.businessId = talkStore.activeChatId;
});
// 验证规则
const rules = {
  name: [{ required: true, message: "请输入公文名称", trigger: "blur" }],
  description: [{ required: true, message: "请输入描述信息", trigger: "change" }]
};

const emit = defineEmits(["send-message"]);

const uploadUrl = import.meta.env.VITE_API_URL + "admin-api/infra/file/chunkedUpload";
const headers = ref({
  ContentType: "multipart/form-data",
  Authorization: "Bearer " + userStore.token
});

// 设置上传body参数
const objInfo = ref({
  fileId: ""
});

// 表单实例
const ruleForm = ref();

const submitForm = async (ruleFormRef: FormInstance | undefined, fileList: UploadFileVo[]) => {
  if (!ruleFormRef) return;
  ruleFormRef.validate(async (valid: boolean) => {
    if (valid) {
      formData.value.messageOfficialParamFileList = [];
      if (fileList.length) {
        for (let file of fileList) {
          if (file.status != "success") {
            ElMessage.error("有未完成文件，请稍后再试");
            return;
          }
          const response = file.response as Respnone;
          if (response.code == 0) {
            formData.value.messageOfficialParamFileList.push({
              fileId: response.data.id,
              fileName: file.name,
              secret: file.secret
            });
          }
        }
      }
      docLoading.value = true;
      try {
        const res: any = await officialDocCreate(formData.value);
        if (res.code == 0) {
          ElMessage.success("保存成功");
          emit("send-message", res.data, ContentType.OFFICIAL_DOCUMENT);
          closeDialog();
        }
      } finally {
        docLoading.value = false;
      }
    } else {
      ElMessage.error("请检查表单项!");
    }
  });
};

watch(visible, newVal => {
  if (!newVal && ruleForm.value) {
    ruleForm.value.resetFields();
    fileList.value = [];
  }
});

const changeContent = (content: string) => {
  formData.value.description = content;
};

const uploadFileSecret = ref(30);
const contact: any = ref({});
const contactSecret = ref(30);
const levelList: any = ref([]);

watchEffect(() => {
  contact.value = talkStore.activeContact;
  contactSecret.value = Math.min(contact.value.secret, userStore.secretLevel);
  levelList.value = SecretLevelConverter.getOptions("obj").filter((item: any) => item.value <= contactSecret.value);
});

const uploadRef = ref();
const fileList = ref<UploadFileVo[]>([]);
const handleBeforeUpload = (file: UploadRawFile) => {
  if (file.size > 500 * 1024 * 1024) {
    ElMessage.error("文件大小不能超过500MB");
    return false;
  }
  const typesToBlock = [".c", ".exe", ".dll", ".bat", ".iso", ".cmd", ".sh"];
  const fileName = file.name.toLowerCase();
  if (typesToBlock.some(type => fileName.endsWith(type))) {
    ElMessage.error("该类型文件可能存在风险，请打包后上传");
    return false;
  }
  const fileSize = file.size > 0;
  if (!fileSize) {
    ElMessage.error("上传文件不能为空");
    return false;
  }

  return fileSize;
};

const handleSuccess = async (resMsg: any, uploadFile: UploadFile) => {
  if (resMsg.code == 0) {
    if (resMsg.data.id == 0) {
      ElMessage.success("文件上传失败");
      return;
    }
  }
  fileList.value.forEach((file: UploadFileVo) => {
    if (uploadFile.uid == file.uid) {
      file.secret = uploadFileSecret.value;
    }
  });
};
const handleErro = (res: any) => {
  ElMessage.error(res.msg);
};

const handleRemove = (uploadRef: UploadInstance | null, file: UploadFile) => {
  if (uploadRef) {
    uploadRef.handleRemove(file);
  }
};

const openDialog = () => {
  visible.value = true;
};
const closeDialog = () => {
  visible.value = false;
};
defineExpose({ openDialog, closeDialog });
</script>

<style lang="scss" scoped>
.el-form-item {
  @apply block;

  :deep(.el-form-item__content) {
    flex-wrap: nowrap;
  }
}

.select-secret {
  width: 100px;
  display: block;
  margin-right: 10px;
  float: left;
}

:deep(.file-list) {
  cursor: pointer;
  display: flex;
  align-items: center;

  .icon-delete {
    display: none;
  }

  &:hover {
    .icon-delete {
      display: inline-block;
    }

    .icon-success {
      display: none;
    }
  }

  .icon-upload {
    height: 32px;
    line-height: 36px;
    cursor: pointer;
  }
}

:deep(.el-upload-list) {
  max-height: 143px;
  overflow-y: auto;
}
</style>
