<template>
  <div class="edit-container" ref="editorRef" />
</template>
<script lang="ts" setup>
import { ref, watchEffect, onMounted, nextTick } from "vue";
import E from 'wangeditor';

const editorRef = ref()
const editor = ref()

const setContent = (htmlContent: string) => {
  if (editor.value) {
    editor.value.txt.html(htmlContent);
  }
}

const props = defineProps({
  content: {
    type: String,
    defualt: ''
  }
})
const emits = defineEmits(['changeContent'])

onMounted(() => {
  nextTick(() => {
    if (editorRef.value) {
      let editorInstance = new E(editorRef.value);
      // 自定义配置（可选）
      editorInstance.customConfig.menus = ['head', 'bold', 'fontSize', 'fontName', 'italic', 'underline', 'strikeThrough', 'indent', 'lineHeight', 'foreColor', 'backColor', 'link', 'list', 'justify', 'quote', 'emoticon', 'image', 'table', 'code', 'splitLine', 'undo', 'redo' ];

      editorInstance.customConfig.onchange = (newHtml: string) => {
        let content = ''
        if (newHtml == '<p><br></p>') {
          setContent('');
        } else {
          content = newHtml
        }

        emits('changeContent', content)
      }

      // 创建编辑器实例
      editorInstance.create();
      editor.value = editorInstance
    }
  })
})

watchEffect(() => {
  setContent(props.content as string)
})


</script>
<style lang="scss" scoped>
.edit-container{
  :deep(.w-e-text-container) {
    ol{
      list-style: decimal;
    }

    ul {
      list-style: disc
    }
  }
}
</style>