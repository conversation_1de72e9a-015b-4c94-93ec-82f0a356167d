<template>
  <div class="create-group">
    <el-form :model="formData" :rules="rules" ref="ruleForm" label-width="5em">
      <el-form-item label="群组名称" prop="groupName">
        <el-input v-model="formData.groupName" placeholder="请输入群组名称，3~10个字符"></el-input>
      </el-form-item>
      <el-form-item label="群组密级" prop="secret">
        <el-select v-model="formData.secret" placeholder="请选择群组密级">
          <template v-for="item in secretLevel">
            <el-option :label="item.label" :value="item.value"></el-option>
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="群组类型" prop="groupType">
        <el-select v-model="formData.groupType" placeholder="请选择群组类型">
          <el-option label="普通群" value="0"></el-option>
          <el-option label="官方群" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="成员范围" prop="groupScope">
        <el-select v-model="formData.groupScope" placeholder="请选择成员范围">
          <el-option v-for="item in roleList" :label="item.remark" :value="item.code" :key="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="归属项目" prop="belongProject">
        <el-input v-model="formData.belongProject" placeholder="请输入归属项目"></el-input>
      </el-form-item>
      <div class="warning pt-1 text-gray-600 leading-6">
        说明：<br />请详细填写群组信息，研讨内容符合保密要求，群组成员需共同承担保密责任。
      </div>
    </el-form>
    <div class="absolute bottom-0 right-0">
      <el-button type="primary" @click="submitForm">下一步</el-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="BasicInfo">
import { ref, computed } from "vue";
import { useTalkStore } from "@/stores/modules/talk";
import { useUserStore } from "@/stores/modules/user";
import { ElMessage } from "element-plus";
import { SecretLevelConverter } from "@/utils/secretLevelConverter";

const talkStore = useTalkStore();
const userStore = useUserStore();

const formData: any = computed(() => talkStore.createGroup);
const roleList = computed(() => talkStore.approveRole);
const secretLevel: any = computed(() =>
  SecretLevelConverter.getOptions("obj").filter(item => item.value <= userStore.secretLevel)
);

// 验证规则
const rules = {
  groupName: [
    { required: true, message: "请输入群组名称", trigger: "blur" },
    { min: 3, max: 10, message: "长度应为3~10个字符", trigger: "blur" }
  ],
  secret: [{ required: true, message: "请选择群组密级", trigger: "change" }],
  belongProject: [{ required: true, message: "请输入归属项目", trigger: "blur" }],
  groupType: [{ required: true, message: "请选择群组类型", trigger: "change" }],
  groupScope: [{ required: true, message: "请选择成员范围", trigger: "change" }]
};  

// 表单实例
const ruleForm: any = ref(null);

const emit = defineEmits(["next-step"]);

const submitForm = () => {
  ruleForm.value.validate((valid: any) => {
    if (valid) {
      talkStore.setNewGroup(formData.value);
      emit("next-step");
    } else {
      ElMessage.error("请检查表单项!");
      return false;
    }
  });
};
</script>

<style lang="scss" scoped>
.el-form {
  @apply flex flex-wrap justify-between;
}
.el-form-item {
  width: 49%;
  @apply flex-col mb-3;
}

.el-form-item :deep(.el-form-item__label) {
  @apply justify-start relative before:absolute before:right-0 font-medium;
}

.dark .create-group {
  .warning {
    @apply text-gray-400;
  }
}
</style>
