import { defineStore } from "pinia";
import piniaPersistConfig from "../helper/persist";
import * as eventApi from "@/api/modules/event";

export const useCalendarStore = defineStore("lark-event", {
  state: () => ({
    events: {} as any,
    isFetching: false
  }),
  getters: {},
  actions: {
    updateToDoData(time: string, data: Array<any>) {
      Object.assign(this.events, { [time]: data });
    },
    // 获取日程
    async getEventList() {
      const res: any = await eventApi.getEventList();
      if (res.code == 0) {
        res.data.list.forEach((item: any) => {
          if (this.events[item.dateTime]) {
            const index = this.events[item.dateTime]?.findIndex((it: { id: any }) => it.id === item.id);
            if (index !== -1) {
              this.events[item.dateTime][index] = item;
            } else {
              this.events[item.dateTime].push(item);
            }
          } else {
            this.events[item.dateTime] = [item];
          }
        });
      }
    }
  },
  persist: piniaPersistConfig("lark-event-session")
});
