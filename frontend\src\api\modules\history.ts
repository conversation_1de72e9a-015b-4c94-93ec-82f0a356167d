import request from "@/api";

// 获取个人聊天历史消息 {pageSize, pageNo, receiver}
export const getUserMsgHistory = async (params: any) => {
  return await request.get("/admin-api/chat/userMessage/pageUserMessage", { params });
};
// 获取群组聊天历史消息 {pageSize, pageNo, receiver}
export const getGroupMsgHistory = async (params: any) => {
  return await request.get("/admin-api/chat/groupMessage/pageGroupMessage", { params });
};
// 保存个人消息到数据库
export const saveUserMessage = async (params: any) => {
  return await request.post("/admin-api/chat/userMessage/saveUserMessage", params);
};
// 保存群组消息到数据库
export const saveGroupMessage = async (params: any) => {
  return await request.post("/admin-api/chat/groupMessage/saveGroupMessage", params);
};
// 撤回个人消息 {id}
export const cancelUserMessage = async (params: any) => {
  return await request.put("/admin-api/chat/userMessage/cancelUserMessage", params);
};
// 撤回群组消息 {id}
export const cancelGroupMessage = async (params: any) => {
  return await request.put("/admin-api/chat/groupMessage/cancelGroupMessage", params);
};
// 设置已读消息时间
export const setReadTime = async (params: any) => {
  return await request.post("/admin-api/chat/messageRead/create", params);
};
// 置顶群组消息 {receiver, id, isTop}
export const pinGroupMessage = async (params: any) => {
  return await request.put("/admin-api/chat/groupMessage/topGroupMessage", params);
};