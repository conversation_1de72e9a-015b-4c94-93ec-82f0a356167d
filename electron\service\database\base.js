const { SqliteStorage } = require('ee-core/storage');
const { getDataDir } = require('ee-core/ps');
const path = require('path');

/**
 * 基础数据库服务类
 * 基于electron-egg框架推荐的数据库操作模式
 */
class BaseDbService {
  constructor(options = {}) {
    const { dbname = 'app.db' } = options;
    this.dbname = dbname;
    this.db = undefined;
    this._init();
  }

  /**
   * 初始化数据库连接
   */
  _init() {
    try {
      // 定义数据文件路径
      const dbFile = path.join(getDataDir(), "db", this.dbname);
      
      // SQLite连接选项
      const sqliteOptions = {
        timeout: 6000,
        verbose: process.env.NODE_ENV === 'development' ? console.log : null
      };
      
      // 创建存储实例
      this.storage = new SqliteStorage(dbFile, sqliteOptions);
      this.db = this.storage.db;
      
      console.log(`数据库连接成功: ${dbFile}`);
    } catch (error) {
      console.error('数据库初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行SQL查询
   * @param {string} sql - SQL语句
   * @param {Array} params - 参数
   */
  query(sql, params = []) {
    try {
      const stmt = this.db.prepare(sql);
      return stmt.all(params);
    } catch (error) {
      console.error('SQL查询失败:', error);
      throw error;
    }
  }

  /**
   * 执行SQL插入/更新/删除
   * @param {string} sql - SQL语句
   * @param {Array} params - 参数
   */
  run(sql, params = []) {
    try {
      const stmt = this.db.prepare(sql);
      return stmt.run(params);
    } catch (error) {
      console.error('SQL执行失败:', error);
      throw error;
    }
  }

  /**
   * 获取单条记录
   * @param {string} sql - SQL语句
   * @param {Array} params - 参数
   */
  get(sql, params = []) {
    try {
      const stmt = this.db.prepare(sql);
      return stmt.get(params);
    } catch (error) {
      console.error('SQL查询失败:', error);
      throw error;
    }
  }

  /**
   * 事务处理
   * @param {Function} callback - 事务回调函数
   */
  transaction(callback) {
    const transaction = this.db.transaction(callback);
    return transaction;
  }

  /**
   * 关闭数据库连接
   */
  close() {
    if (this.db) {
      this.db.close();
      console.log('数据库连接已关闭');
    }
  }
}

BaseDbService.toString = () => '[class BaseDbService]';
module.exports = BaseDbService; 