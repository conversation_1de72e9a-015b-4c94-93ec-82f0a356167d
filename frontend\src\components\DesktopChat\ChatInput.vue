<template>
  <div class="flex flex-col space-y-2">
    <el-input
      v-model="input"
      :autosize="{ minRows: 2, maxRows: 6 }"
      type="textarea"
      placeholder="请输入内容..."
      @keydown.enter="submit"
      size="large"
      style=""
    />
    <el-button type="primary" @click="newSession" size="small" class="self-start"> 新建会话 </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const input = ref("");
const emit = defineEmits<{
  (e: "send", value: string): void;
  (e: "new-session"): void;
}>();

function submit() {
  if (!input.value.trim()) return;
  emit("send", input.value);
  input.value = "";
}

function newSession() {
  // eslint-disable-next-line vue/custom-event-name-casing
  emit("new-session");
}
</script>
