import { useContactsStore } from "@/stores/modules/contacts";
const contactStore = useContactsStore();
contactStore.loadFromSessionStorage();

export interface MessageInfo {
  code: number;
  data: [
    {
      id?: string;
      fromId: {
        id: string;
        name: string;
        avatar: string;
        secretLevel: number;
      };
      toId: {
        id: string;
        name: string;
        avatar: string;
        secretLevel: number;
      };
      toName: string;
      atId?: string[];
      time: string;
      isGroup?: boolean;
      groupOwnerId: string;
      contentId?: string;
      contentType?: number;
      content: any;
      orgCode?: string;
    }
  ];
}

// Content类型枚举
export enum MessageCode {
  PRAIVTE = 200100, // 私聊消息
  GROUP = 200200, // 群组消息
  SYSTEM = 200300, // 系统消息
  PARTICIPATES_RELAY = 200201, // 参与接龙
  PARTICIPATES_VOTE = 200202, // 参与投票
  GROUP_MODIFY = 200204, // 群信息变更
  OFFICIAL_DOCUMENT = 200205, // 公文状态
  GET_ONLINE_STATUS = 700300 // 查询在线状态
}

// Content类型枚举
export enum ContentType {
  TEXT = 0, // 文本消息
  FILE = 1, // 文件消息
  IMAGE = 2, // 图片消息
  AUDIO = 3, // 音频
  VOTE = 4, // 投票
  RELAY = 5, // 接龙
  SYSTEM = 6, // 系统消息
  TIPS_TEXT = 7, // 群提示文本
  EMOJI = 8, //动态表情
  OFFICIAL_DOCUMENT = 9, // 公文
  GROUP_MODIFY_NAME = 10, //修改群组名称
  GROUP_MODIFY_NOTICE = 11, //群通知
  GROUP_MODIFY_OWNER = 12, // 改群主
  GROUP_DISSLOVE = 13, //群解散
  GROUP_ADD_MEMBER = 14, //增加群成员
  GROUP_DELETE_MEMBER = 15, //删除群成员
  GROUP_LEAVE = 16, //解散群
  BACKSPACE = 20, // 撤回
  UNREAD = 30, // 消息未读
  PIN = 31, //置顶消息
  UNPIN = 32 //取消置顶
}

// 修改 MessageSendInfo 接口
export interface MessageSendInfo {
  code: MessageCode;
  data: {
    id?: string;
    fromId: string;
    toId: string;
    atId?: string[];
    isGroup?: boolean;
    contentId?: string;
    chatType?: number; //0单聊1普通群2官方群
    content: any;
    contentType?: ContentType;
  };
}

export interface MessageOnlineInfo {
  code: number;
  data: [
    {
      status: string;
      time: string;
      userId: string;
      userName: string;
      avatar: string;
      secretLevel: number;
    }
  ];
}

// 修改转换函数中的类型引用
export async function convertMessage(sendInfo: MessageSendInfo): Promise<MessageInfo> {
  // 并行获取发送方和接收方信息
  const [fromInfo, toInfo] = await Promise.all([
    contactStore.getContactById(sendInfo.data.fromId),
    contactStore.getContactById(sendInfo.data.toId, sendInfo.data.isGroup)
  ]);

  if (!fromInfo || !toInfo) {
    throw new Error("联系人信息缺失，转换失败");
  }

  return {
    code: sendInfo.code,
    data: {
      ...sendInfo.data,
      contentType: sendInfo.data.contentType, // 保持枚举值传递
      fromId: {
        id: fromInfo.id,
        name: fromInfo.name,
        avatar: fromInfo.avatar,
        secretLevel: fromInfo.secretLevel
      },
      toId: {
        id: toInfo.id,
        name: toInfo.name,
        avatar: toInfo.avatar,
        secretLevel: toInfo.secretLevel
      },
      toName: toInfo.name,
      time: new Date().toISOString(),
      groupOwnerId: "" // 根据实际业务补充群主ID
    }
  };
}
