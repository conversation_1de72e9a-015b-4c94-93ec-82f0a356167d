<template>
  <div class="vote-box rounded-lg border border-gray-200 bg-white shadow-md p-4 hover:shadow-lg transition-shadow duration-300">
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center space-x-2 mr-2">
        <span
          class="head flex items-center gap-1 font-medium text-blue-700 hover:bg-blue-100 rounded-md px-2 py-1 text-xs bg-blue-50"
        >
          <font-awesome-icon :icon="['fas', 'poll']" class="text-xs" />
          投票
        </span>
        <div class="ongoing text-xs px-2 py-0.5 rounded font-medium bg-gray-50 text-gray-700" :class="{ active: vote.isActive }">
          {{ vote.isActive ? "进行中" : "已结束" }}
        </div>
      </div>
      <div class="time flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 bg-gray-50 px-2 py-0.5 rounded-md">
        <font-awesome-icon :icon="['fas', 'clock']" class="text-xs" />
        截止: {{ vote.deadline }}
      </div>
    </div>
    <h3 class="title mb-2 text-base font-bold text-gray-900 hover:text-blue-600 cursor-pointer transition-colors duration-200">
      {{ vote.title }}
    </h3>
    <p class="description mb-3 text-sm text-gray-600 bg-white/80 p-2 rounded border-l-2 border-blue-300">
      {{ vote.description }}
    </p>
    <div class="space-y-1.5 mb-3">
      <div v-for="item in vote.optionsDOList" :key="item.id" @click="voteEvent(item.id)">
        <el-tooltip content="确认投票" :placement="item.sender == userStore.userId ? 'right' : 'left'">
          <div
            class="vote-item relative rounded border border-gray-200 bg-white hover:bg-blue-50 hover:shadow-sm transition-all duration-200 cursor-pointer"
          >
            <div class="p-3">
              <div class="flex items-center justify-between mb-1.5">
                <span class="vote-item-title text-sm font-medium text-gray-900" :class="{ active: vote.selectId == item.id }">
                  {{ item.text }}
                </span>
                <span
                  class="vote-item-percent text-xs font-medium rounded px-1.5 py-0.5 bg-gray-100 text-gray-700"
                  :class="{ active: vote.selectId == item.id }"
                >
                  {{ voteTotal ? ((item.votes / voteTotal) * 100).toFixed(0) : 0 }}%
                </span>
              </div>
              <div class="w-full">
                <el-progress
                  :class="{ active: vote.selectId == item.id }"
                  :percentage="voteTotal ? (item.votes / voteTotal) * 100 : 0"
                  :stroke-width="6"
                  :show-text="false"
                ></el-progress>
              </div>
              <div class="mt-1 flex items-center justify-between">
                <span class="vote-item-num text-xs text-gray-500"> {{ item.votes }} 票 </span>
                <span class="vote-item-true flex items-center text-xs text-blue-600" v-if="vote.selectId == item.id">
                  <font-awesome-icon :icon="['fas', 'check-circle']" class="mr-1 text-xs" />
                  已投票
                </span>
              </div>
              <div class="vote-item-num flex mt-1 text-xs text-gray-500" v-if="item.users.length > 0">
                <span class="mr-1">投票者</span>
                <div class="flex-1 flex flex-wrap space-x-1">
                  <span v-for="user in item.users">{{ user }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-tooltip>
      </div>
    </div>
    <div class="vote-br flex items-center justify-between pt-2">
      <div class="flex items-center space-x-1.5">
        <DynamicAvatar :id="vote.creator ?? ''" :data-info="vote" :relation-name="vote.creatorName" :size="20" :type-avatar="0" />
        <span className="vote-by text-xs text-gray-500"> 由{{ vote.creatorName }}创建 </span>
      </div>
      <span class="vote-total text-xs bg-blue-50 text-blue-700 px-2 py-0.5 rounded font-medium"> {{ voteTotal }}人参与 </span>
    </div>
    <div class="absolute top-0 left-0 bottom-0 right-0 bg-transparent" v-if="vote.selectId || !vote.isActive"></div>
  </div>
</template>

<script setup lang="ts" name="MessageVote">
import { computed, onMounted } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { useHistoryStore } from "@/stores/modules/history";
import { useTalkStore } from "@/stores/modules/talk";
import { useContactStore } from "@/stores/modules/friend";
import { getVoteRecords } from "@/api/modules/contact";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import WebSocketClient from "@/utils/websocket/websocketClient";
import { MessageCode } from "@/utils/websocket/messageInfo";
import { sendMessage } from "@/utils/websocket/messageService";

const talkStore = useTalkStore();
const userStore = useUserStore();
const historyStore = useHistoryStore();
const contactStore = useContactStore();

const props = defineProps({
  voteId: {
    type: String,
    required: true
  }
});

onMounted(async () => {
  await historyStore.getVoteInfo(props.voteId);
});
const members = computed(() => contactStore.groupMembers[talkStore.activeChatId] || []);
const vote = computed(() => {
  if (!historyStore.voteList[props.voteId]) {
    return {};
  } else {
    historyStore.voteList[props.voteId].optionsDOList.forEach((item: any) => {
      let arr: any = [];
      members.value.forEach((member: any) => {
        if (item.userIds && item.userIds.includes(member.member)) {
          arr.push(member.memberName);
        }
      });
      item.users = arr;
    });
    if (new Date(historyStore.voteList[props.voteId].deadline) > new Date()) {
      historyStore.voteList[props.voteId].isActive = true;
    } else {
      historyStore.voteList[props.voteId].isActive = false;
    }
    return historyStore.voteList[props.voteId];
  }
});

const voteTotal = computed(() => vote.value.optionsDOList?.reduce((acc: any, item: any) => acc + item.votes, 0) || 0);

const webSocketManager = WebSocketClient.getInstance();

let flag = false;
const voteEvent = async (optionId: any) => {
  if (!flag) flag = true;
  else return;
  if (new Date(vote.value.deadline) < new Date()) {
    vote.value.isActive = false;
    ElMessage.warning("投票已结束");
    return;
  }
  let params = {
    userId: userStore.userId,
    voteId: Number(props.voteId),
    optionId: optionId,
    votedAt: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")
  };
  const res: any = await getVoteRecords(params);
  if (res.code == 0) {
    ElMessage.success("投票成功");
    try {
      await sendMessage(webSocketManager!, {
        code: MessageCode.PARTICIPATES_VOTE,
        receiverId: talkStore.activeChatId,
        isGroup: true,
        content: {
          voteId: props.voteId,
          optionId,
          resData: res.data
        }
      });
    } catch (error: any) {
      ElMessage.error(error.message);
    }
  }

  flag = false;
};
</script>

<style scoped lang="scss">
.vote-box {
  min-width: 335px;
  position: relative;
  background: rgb(222, 233, 255) !important;
  border-color: rgb(168, 211, 254) !important;
  border-radius: 12px;
}
.vote-item {
  @apply border-gray-200 hover:border-blue-300 hover:bg-gray-50;
  .vote-item-title.active {
    @apply text-blue-700;
  }
  .vote-item-percent.active {
    @apply bg-blue-100 text-blue-700;
  }
  :deep(.el-progress) {
    .el-progress-bar__outer {
      @apply bg-gray-200;
    }
    .el-progress-bar__inner {
      @apply bg-blue-100;
    }
    &.active .el-progress-bar__inner {
      @apply bg-blue-600;
    }
  }
}
.dark {
  .vote-box {
    background: rgba(30, 58, 138, 0.05) !important;
    border-color: rgba(29, 78, 216, 0.2) !important;
    @apply border-gray-700 bg-gray-800;
    .head {
      @apply text-cyan-900 bg-blue-900/40 hover:bg-cyan-300;
    }
    .time {
      @apply bg-gray-700;
    }
    .title {
      @apply text-white hover:text-blue-400;
    }
    .description {
      @apply text-gray-300 bg-gray-700/40 border-blue-700;
    }
  }
  .vote-item {
    @apply text-gray-300 bg-gray-700/40 border-blue-700 border-gray-700 hover:border-blue-700 hover:bg-gray-700/60;
    .vote-item-title {
      @apply text-white;
      &.active {
        @apply text-blue-400;
      }
    }
    .vote-item-percent {
      @apply bg-gray-700 text-gray-300;
      &.active {
        @apply bg-blue-900/40 text-blue-400;
      }
    }
    :deep(.el-progress) {
      .el-progress-bar__outer {
        @apply bg-gray-700;
      }
      .el-progress-bar__inner {
        @apply bg-blue-900/40;
      }
      &.active .el-progress-bar__inner {
        @apply bg-blue-500;
      }
    }
    .vote-item-num {
      @apply text-gray-400;
    }
    .vote-item-true {
      @apply text-blue-400;
    }
  }
  .vote-br {
    @apply border-gray-700;
  }
  .vote-by {
    @apply text-gray-400;
  }
  .vote-total {
    @apply bg-blue-900/30 text-blue-400;
  }
}
</style>
