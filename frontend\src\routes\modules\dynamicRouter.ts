import { useAuthStore } from "@/stores/modules/auth";
import { useUserStore } from "@/stores/modules/user";
import { ElNotification } from "element-plus";
import { LOGIN_URL } from "@/config";
import router from "@/routes/index";
import { RouteRecordRaw } from "vue-router";
// 引入 views 文件夹下所有 vue 文件
const modules = import.meta.glob("@/views/**/*.vue");

/**
 * @description 初始化动态路由
 */
export const initDynamicRouter = async () => {
  const userStore = useUserStore();
  const authStore = useAuthStore();

  try {
    // 1.获取菜单列表 && 按钮权限列表
    await authStore.getAuthMenuList();
    // await authStore.getAuthButtonList();

    // 2.判断当前用户有没有菜单权限,当前账号无权限则跳转登录页并清空token
    if (!authStore.authMenuListGet.length) {
      ElNotification({
        title: "无权限访问",
        message: "当前账号无任何菜单权限，请联系系统管理员！",
        type: "warning",
        duration: 3000
      });
      userStore.setToken("");
      router.replace(LOGIN_URL);
      return Promise.reject("No permission");
    }

    // 3.添加动态路由
    authStore.flatMenuListGet.forEach((item: any) => {
      // 删除children属性
      // item.children && delete item.children;
      item.path = `/${item.path}`;
      item.meta = {
        icon: item.icon ? item.icon : "",
        title: item.name,
        isHide: !item.visible,
        isKeepAlive: item.keepAlive
      };
      item.name = item.componentName;
      // 如果item.component是字符串，则将其转换为对应的组件路径
      if (item.component && typeof item.component == "string") {
        item.component = modules["/src/views/" + item.component + ".vue"];
      }
      // 如果item.meta.isFull为true，则将其添加为全局路由；否则添加为layout路由
      if (item.meta.isFull) {
        router.addRoute(item as unknown as RouteRecordRaw);
      } else {
        router.addRoute("layout", item as unknown as RouteRecordRaw);
      }
    });
  } catch (error) {
    // 当按钮 || 菜单请求出错时，重定向到登陆页
    userStore.setToken("");
    router.replace(LOGIN_URL);
    return Promise.reject(error);
  }
};
