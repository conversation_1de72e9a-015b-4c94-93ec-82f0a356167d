<template>
  <div class="app-store-page">
    <!-- 可滚动的内容容器 -->
    <el-scrollbar>
      <!-- 背景容器 -->
      <div class="background-container"></div>

      <!-- 常用链接区域 -->
      <div class="favorite-section">
        <div class="section-header">
          <h2 class="favorite-title">常用链接</h2>
          <el-button type="primary" size="default" class="add-link-btn" @click="openLinkDialog('create')">
            <el-icon><Plus /></el-icon>
            添加链接
          </el-button>
        </div>
        <div class="favorite-container">
          <div v-if="favoriteList.length" class="favorite-grid">
            <div v-for="item in favoriteList" :key="item.id" class="favorite-item">
              <div class="favorite-content" @click="openLinkInBrowser(item)">
                <div class="favorite-icon-wrapper">
                  <!-- <template v-if="linkImgUrl[item.image]">
                    <img :src="linkImgUrl[item.image]" class="favorite-icon" />
                  </template> -->
                  <div class="favorite-icon-avatar" :style="{ background: generateAvatarColor(item.title.charAt(0)) }">
                    {{ item.title.charAt(0) }}
                  </div>
                </div>
                <span class="favorite-name" :title="item.title">{{
                  item.title.length > 5 ? item.title.substring(0, 5) + "..." : item.title
                }}</span>
              </div>
              <div class="favorite-delete" @click.stop="removeFavorite(item)">
                <el-icon><Delete /></el-icon>
              </div>
            </div>
          </div>
          <div v-else class="empty-favorites">
            <el-icon class="empty-icon"><Star /></el-icon>
            <div class="empty-text">暂无我的链接，可以点击应用卡片上的'加入我的'按钮添加</div>
          </div>
        </div>
      </div>

      <!-- 全部链接区域 -->
      <div class="favorite-section">
        <div class="section-header">
          <h2 class="favorite-title">全部链接</h2>
        </div>
      </div>

      <!-- 应用列表 -->
      <div class="app-store-container">
        <NoData v-if="list.length == 0" />
        <div v-else class="app-grid">
          <div v-for="item in list" :key="item.id" class="app-card" @click="openLinkInBrowser(item)">
            <!-- 第一行：图标和名称 -->
            <div class="app-header-row">
              <div class="app-icon-wrapper" @click.stop="openDetail(item)">
                <div
                  class="app-icon-avatar"
                  :style="{ background: generateAvatarColor(item.title ? item.title.charAt(0) : 'A') }"
                >
                  {{ item.title ? item.title.charAt(0) : "A" }}
                </div>
              </div>

              <div class="app-title-wrapper">
                <h3 class="app-name">{{ item.title }}</h3>
                <span class="app-type">{{ item.personal === "1" ? "个人应用" : "系统应用" }}</span>
              </div>
            </div>

            <!-- 第二行：描述 -->
            <div class="app-description-row">
              <p class="app-description">{{ item.description || "这是一个实用的应用工具" }}</p>
            </div>

            <!-- 第三行：所属组织和更新时间 -->
            <div class="app-metadata-row">
              <div class="metadata-item">
                <el-icon class="metadata-icon"><Location /></el-icon>
                <span class="metadata-text">{{ item.orgCode || "系统应用" }}</span>
              </div>
              <div class="metadata-item">
                <el-icon class="metadata-icon"><VideoPlay /></el-icon>
                <span class="metadata-text">{{ formatDate(new Date()) }}</span>
              </div>
            </div>

            <!-- 第四行：加入常用按钮和启动方式图标 -->
            <div class="app-action-row">
              <button
                class="launch-button"
                :class="{
                  'favorite-active': isFavorite(item.id),
                  'launch-enabled': item.state === '1' && !isFavorite(item.id),
                  'launch-disabled': item.state !== '1'
                }"
                @click.stop="toggleFavorite(item)"
              >
                <el-icon class="launch-icon">
                  <Star v-if="isFavorite(item.id)" />
                  <StarFilled v-else-if="item.state === '1'" />
                  <VideoPause v-else />
                </el-icon>
                <span class="launch-text">{{ getFavoriteButtonText(item) }}</span>
              </button>

              <!-- 联系客服 -->
              <button class="launch-button favorite-active" v-if="item.personal === '0'" @click.stop="openChat(item.userId)">
                <span class="launch-text">联系客服</span>
              </button>

              <!-- 启动方式图标 -->
              <div class="launch-methods" v-if="item.state === '1'">
                <div class="launch-method-item" @click.stop="handleLaunchMethod(item, item.image)" title="启动">
                  <img :src="linkImgUrl[item.image]" alt="Chrome" class="method-icon" />
                </div>
                <!-- <div class="launch-method-item" @click.stop="handleLaunchMethod(item, 'local')" title="本地启动">
                  <el-icon class="method-icon local-icon"><Monitor /></el-icon>
                </div> -->
              </div>
            </div>

            <!-- 管理员操作菜单 -->
            <div v-if="isAdmin || (item.personal === '1' && item.userId === userStore.userId)" class="admin-controls" @click.stop>
              <el-dropdown trigger="click" placement="bottom-end">
                <button class="admin-menu-btn">
                  <el-icon><MoreFilled /></el-icon>
                </button>
                <template #dropdown>
                  <el-dropdown-menu class="admin-dropdown">
                    <el-dropdown-item @click="openLinkDialog('update', item)">
                      <el-icon><Edit /></el-icon>编辑链接
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleDelete(item.id)" divided class="danger-action">
                      <el-icon><Delete /></el-icon>禁用链接
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>

            <!-- 悬停效果 -->
            <div class="hover-effect"></div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="load-more-container" v-if="list.length > 0">
        <el-button v-if="hasMore" @click="loadMore" :loading="loading" type="primary" size="large" class="load-more-btn">
          {{ loading ? "加载中..." : "加载更多" }}
        </el-button>
        <div v-else class="no-more-data">
          <span>已显示全部 {{ total }} 个应用</span>
        </div>
      </div>
    </el-scrollbar>

    <!-- 添加链接对话框 -->
    <el-dialog v-model="addLinkDialogVisible" :title="dialogInfo.title" width="500px">
      <el-form ref="addLinkFormRef" :model="addLinkForm" :rules="addLinkRules" label-width="80px" label-position="left">
        <el-form-item label="名称" prop="title">
          <el-input v-model="addLinkForm.title" placeholder="请输入链接名称" clearable />
        </el-form-item>
        <el-form-item label="链接地址" prop="linkAddress">
          <el-input v-model="addLinkForm.linkAddress" placeholder="请输入链接地址，如：https://www.example.com" clearable />
        </el-form-item>
        <el-form-item label="链接状态" prop="state">
          <el-select v-model="addLinkForm.state" placeholder="请选择">
            <el-option v-for="item in data.state" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="浏览器" prop="image">
          <el-select v-model="addLinkForm.image" placeholder="请选择浏览器">
            <el-option v-for="item in data.image" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="链接参数" prop="param">
          <el-input v-model="addLinkForm.param" placeholder="请输入链接参数" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="addLinkForm.description" type="textarea" placeholder="请输入链接描述（可选）" :rows="3" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addLinkDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSure" :loading="addLinkLoading"> 确 定 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="应用详情" width="500px">
      <div v-loading="detailLoading" class="detail-dialog-body">
        <template v-if="detailData">
          <p><strong>名称：</strong>{{ detailData.title }}</p>
          <p><strong>链接：</strong>{{ detailData.linkAddress }}</p>
          <p><strong>状态：</strong>{{ detailData.state === "1" ? "已启用" : "未启用" }}</p>
          <p><strong>类型：</strong>{{ detailData.personal === "1" ? "个人应用" : "系统应用" }}</p>
          <p><strong>所属单位：</strong>{{ detailData.orgCode }}</p>
          <p><strong>描述：</strong>{{ detailData.description }}</p>
        </template>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关 闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="more">
import { onMounted, reactive, ref } from "vue";
import { LinkApi, AppLinkRespVO } from "@/api/modules/link";
import { useUserStore } from "@/stores/modules/user";
import { useRecentStore } from "@/stores/modules/recent";
import { useTalkStore } from "@/stores/modules/talk";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { getPersonInfo } from "@/api/modules/contact";
import NoData from "@/components/NoData/index.vue";
import { formatDate } from "@/utils/formatTime";
import { ipc, isEE } from "@/utils/ipcRenderer";
import { Location, Edit, Delete, VideoPlay, VideoPause, MoreFilled, Star, StarFilled, Plus } from "@element-plus/icons-vue";

const userStore = useUserStore();
const recentStore = useRecentStore();
const talkStore = useTalkStore();
const router = useRouter();

const isAdmin = ref(userStore.userInfo?.role === "admin");
// isAdmin.value = true;
const linkImgUrl: any = ref({});

// 常用链接列表
const favoriteList = ref<AppLinkRespVO[]>([]);

// 添加链接对话框相关
const data = {
  state: [
    {
      value: "1",
      label: "已启用"
    },
    {
      value: "0",
      label: "未启用"
    }
  ],
  image: [
    {
      value: "chrome",
      label: "谷歌"
    },
    {
      value: "ie",
      label: "IE"
    },
    {
      value: "firefox",
      label: "火狐"
    },
    {
      value: "edge",
      label: "Edge"
    },
    {
      value: "liebao",
      label: "猎豹"
    }
  ]
};
const addLinkDialogVisible = ref(false);
const addLinkLoading = ref(false);
const addLinkFormRef = ref();
const addLinkForm = ref({
  title: "",
  linkAddress: "",
  description: "",
  state: "",
  image: "",
  param: ""
});
const dialogInfo = ref({
  type: "",
  title: ""
});

const addLinkRules = {
  title: [
    { required: true, message: "请输入链接名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
  ],
  linkAddress: [
    { required: true, message: "请输入链接地址", trigger: "blur" },
    {
      pattern: /^https?:\/\/.+/,
      message: "请输入有效的链接地址，必须以 http:// 或 https:// 开头",
      trigger: "blur"
    }
  ],
  state: [{ required: true, message: "链接状态不能为空", trigger: "blur" }],
  image: [{ required: true, message: "浏览器不能为空", trigger: "blur" }]
};

/** 获取常用链接 */
const getFavoriteList = async () => {
  try {
    const res: any = await LinkApi.getUserFavoriteLinks(userStore.userInfo?.orgCode || "");
    if (res.code === 0 && res.data) {
      favoriteList.value = res.data;
    }
  } catch {
    // 忽略错误
  }
};
const getLinkImgUrl = () => {
  const emojiModules = import.meta.glob("@/assets/link/*.png", { eager: true, import: "default" });

  Object.keys(emojiModules).forEach(path => {
    // 获取文件名（不含扩展名）
    const fileName = path.split("/").pop()?.split(".")[0];
    if (fileName) {
      linkImgUrl.value[fileName] = emojiModules[path];
    }
  });
};

const list = ref<AppLinkRespVO[]>([]); // 列表的数据
const total = ref(0); // 列表的总页数
const loading = ref(false); // 加载状态
const hasMore = ref(true); // 是否还有更多数据
const allData = ref<AppLinkRespVO[]>([]); // 所有数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 12,
  title: "" as string,
  state: "" as string,
  personal: "" as string, // 改为空字符串，显示所有应用类型
  orgCode: "" as string
});

/** 查询列表 */
const getList = async (isLoadMore = false) => {
  try {
    loading.value = true;

    // 根据是否输入标题关键字决定调用搜索接口还是获取全部可见链接接口
    let res: any;
    if (queryParams.title && queryParams.title.trim() !== "") {
      // 调用后端搜索接口
      res = await LinkApi.searchLinks(userStore.userInfo?.orgCode || "", queryParams.title.trim());
    } else {
      // 调用获取全部可见链接接口
      res = await LinkApi.getUserVisibleLinks(userStore.userInfo?.orgCode || "");
    }
    if (res.code === 0 && res.data) {
      // 根据查询参数过滤列表
      let filteredList = res.data;
      if (queryParams.title) {
        filteredList = filteredList.filter((item: AppLinkRespVO) =>
          item.title.toLowerCase().includes(queryParams.title!.toLowerCase())
        );
      }
      if (queryParams.state) {
        filteredList = filteredList.filter((item: AppLinkRespVO) => item.state === queryParams.state);
      }
      if (queryParams.personal) {
        filteredList = filteredList.filter((item: AppLinkRespVO) => item.personal === queryParams.personal);
      }

      allData.value = filteredList;
      total.value = filteredList.length;

      if (isLoadMore) {
        // 加载更多：获取下一页数据
        const startIndex = (queryParams.pageNo - 1) * queryParams.pageSize;
        const endIndex = startIndex + queryParams.pageSize;
        const newItems = filteredList.slice(startIndex, endIndex);
        list.value = [...list.value, ...newItems];

        // 检查是否还有更多数据
        hasMore.value = endIndex < filteredList.length;
      } else {
        // 初始加载或搜索：重置列表
        queryParams.pageNo = 1;
        const endIndex = queryParams.pageSize;
        list.value = filteredList.slice(0, endIndex);
        hasMore.value = endIndex < filteredList.length;
      }
    }
  } catch (error) {
    ElMessage.error("获取应用列表失败");
  } finally {
    loading.value = false;
  }
};

/** 加载更多 */
const loadMore = async () => {
  if (loading.value || !hasMore.value) return;

  queryParams.pageNo++;
  await getList(true);
};

/** 禁用应用操作 */
const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm("确定要禁用该应用吗？", "提示", {
      type: "warning"
    });
    await LinkApi.setLinkState(id, "0");
    ElMessage.success("禁用成功");
    await getList(false);
  } catch {}
};

/** 启动应用 */
// 打开详情对话框
const detailDialogVisible = ref(false);
const detailLoading = ref(false);
const detailData = ref<AppLinkRespVO | null>(null);

const openDetail = async (item: AppLinkRespVO) => {
  detailDialogVisible.value = true;
  detailLoading.value = true;
  try {
    const res: any = await LinkApi.getLink(item.id);
    if (res.code === 0) {
      detailData.value = res.data;
    } else {
      ElMessage.error(res.data?.msg || "获取详情失败");
    }
  } catch (error) {
    ElMessage.error("获取详情失败");
  } finally {
    detailLoading.value = false;
  }
};

const handleLaunch = async (item: AppLinkRespVO) => {
  if (item.state !== "1") {
    ElMessage.warning("应用未启用，无法启动");
    return;
  }

  try {
    // 检查访问权限
    const res = await LinkApi.checkLinkPermission(item.id, item.orgCode);
    if (res.data?.code === 0 && res.data?.data) {
      // TODO: 实现应用启动逻辑
      ElMessage.success(`正在启动应用：${item.title}`);
    } else {
      ElMessage.warning("您没有权限访问此应用");
    }
  } catch (error) {
    ElMessage.error("应用启动失败");
  }
};

/** 通过指定方式启动应用 */
const handleLaunchMethod = async (item: AppLinkRespVO, method: string) => {
  if (item.state !== "1") {
    ElMessage.warning("应用未启用，无法启动");
    return;
  }

  try {
    // 检查访问权限
    const res = await LinkApi.checkLinkPermission(item.id, item.orgCode);
    if (res.data?.code === 0 && res.data?.data) {
      // 确保链接以http://或https://开头
      let url = item.linkAddress;
      if (!url.startsWith("http://") && !url.startsWith("https://")) {
        url = "https://" + url;
      }

      // 如果是Electron环境，使用IPC调用指定浏览器
      if (isEE && ipc) {
        let browserType = "default";
        switch (method) {
          case "chrome":
            browserType = "Chrome";
            break;
          case "firefox":
            browserType = "Firefox";
            break;
          case "local":
            browserType = "default";
            break;
          default:
            ElMessage.error("不支持的启动方式");
            return;
        }

        try {
          await ipc.invoke("os/openUrlByLocalBrowser", {
            browserType: browserType,
            url: url
          });
          ElMessage.success(
            `正在通过${method === "chrome" ? "Chrome浏览器" : method === "firefox" ? "Firefox浏览器" : "默认浏览器"}启动：${item.title}`
          );
        } catch (error) {
          console.error("调用本地浏览器失败:", error);
          // 如果IPC调用失败，回退到window.open
          window.open(url, "_blank");
          ElMessage.success(`正在启动：${item.title}`);
        }
      } else {
        // 非Electron环境，使用传统的window.open
        window.open(url, "_blank");
        ElMessage.success(`正在启动：${item.title}`);
      }
    } else {
      ElMessage.warning("您没有权限访问此应用");
    }
  } catch (error) {
    ElMessage.error("应用启动失败");
  }
};

/** 客户服务 */
const openChat = async (userId: any) => {
  if (!userId) {
    ElMessage.error("无用户userId");
    return;
  }
  const res: any = await getPersonInfo(userId);
  if (res.data?.code == 0) {
    const data = res.data?.data;
    const params = {
      senderId: userStore.userId,
      contactId: data.id,
      chatType: 0,
      avatar: data.avatar,
      contactName: data.name,
      secret: data.secretLevel
    };
    const res_ = await recentStore.addListRecents(params);
    if (res_) {
      talkStore.setActiveChat(data.id);
      talkStore.ifChat = true;
      talkStore.ifContact = false;
      router.push({ name: "chat" });
    }
  }
};

/** 根据字符生成固定颜色 */
const generateAvatarColor = (char: string) => {
  // 定义一组更深色调的美观渐变色
  const colorPalettes = [
    "linear-gradient(135deg, #4c63d2 0%, #5a4fcf 100%)", // 深紫蓝
    "linear-gradient(135deg, #e91e63 0%, #c2185b 100%)", // 深粉红
    "linear-gradient(135deg, #2196f3 0%, #1976d2 100%)", // 深天蓝
    "linear-gradient(135deg, #009688 0%, #00695c 100%)", // 深青绿
    "linear-gradient(135deg, #ff5722 0%, #d84315 100%)", // 深橙红
    "linear-gradient(135deg, #673ab7 0%, #512da8 100%)", // 深紫色
    "linear-gradient(135deg, #e91e63 0%, #ad1457 100%)", // 深玫红
    "linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)", // 深紫罗兰
    "linear-gradient(135deg, #ff9800 0%, #f57c00 100%)", // 深橙色
    "linear-gradient(135deg, #4caf50 0%, #388e3c 100%)", // 深绿色
    "linear-gradient(135deg, #ff6f00 0%, #e65100 100%)", // 深橙黄
    "linear-gradient(135deg, #795548 0%, #5d4037 100%)", // 深棕色
    "linear-gradient(135deg, #607d8b 0%, #455a64 100%)", // 深蓝灰
    "linear-gradient(135deg, #3f51b5 0%, #303f9f 100%)", // 深靛蓝
    "linear-gradient(135deg, #ff5722 0%, #bf360c 100%)", // 深红橙
    "linear-gradient(135deg, #8bc34a 0%, #689f38 100%)" // 深草绿
  ];

  // 根据字符的Unicode码点生成固定索引
  const charCode = char.charCodeAt(0);
  const index = charCode % colorPalettes.length;

  return colorPalettes[index];
};

/** 检查链接是否在常用列表中 */
const isFavorite = (linkId: number) => {
  return favoriteList.value.some(item => item.id === linkId);
};

/** 获取按钮文本 */
const getFavoriteButtonText = (item: AppLinkRespVO) => {
  if (item.state !== "1") return "未启用";
  return isFavorite(item.id) ? "已添加" : "加入我的";
};

/** 切换常用状态 */
const toggleFavorite = async (item: AppLinkRespVO) => {
  if (item.state !== "1") {
    ElMessage.warning("应用未启用，无法加入我的");
    return;
  }

  try {
    // 直接处理收藏状态，不进行权限检查
    if (isFavorite(item.id)) {
      // 从我的链接中移除
      try {
        // 直接调用removeLinkState接口移除常用链接
        await LinkApi.removeLinkState(item.id);
        ElMessage.success(`已从我的链接中移除：${item.title}`);
        // 重新获取我的链接列表
        await getFavoriteList();
      } catch (error: any) {
        // 如果有错误信息就显示，否则显示默认错误
        ElMessage.error(error?.msg || "移除失败");
      }
    } else {
      // 添加到我的链接
      try {
        // 直接调用setLinkState接口设置状态来添加常用链接
        await LinkApi.setLinkState(item.id, "1");
        ElMessage.success(`已加入我的链接：${item.title}`);
        // 重新获取我的链接列表
        await getFavoriteList();
      } catch (error: any) {
        // 如果有错误信息就显示，否则显示默认错误
        ElMessage.error(error?.msg || "添加失败");
      }
    }
  } catch (error) {
    ElMessage.error("操作失败");
  }
};

/** 直接从我的链接中移除 */
const removeFavorite = async (item: AppLinkRespVO) => {
  try {
    // 直接调用removeLinkState接口移除常用链接
    await LinkApi.removeLinkState(item.id);
    ElMessage.success(`已从我的链接中移除：${item.title}`);
    // 重新获取我的链接列表
    await getFavoriteList();
  } catch (error: any) {
    // 如果有错误信息就显示，否则显示默认错误
    ElMessage.error(error?.msg || "移除失败");
  }
};

const handleSure = () => {
  if (dialogInfo.value.type === "create") {
    handleAddLink();
  } else {
    handleUpdateLink();
  }
};

/** 打开添加链接对话框 */
const openLinkDialog = (type: string, item?: any) => {
  dialogInfo.value.type = type;
  if (type === "create") {
    addLinkForm.value = {
      title: "",
      linkAddress: "",
      description: "",
      state: "",
      image: "",
      param: ""
    };
    dialogInfo.value.title = "添加链接";
  } else {
    addLinkForm.value = item;
    dialogInfo.value.title = "编辑链接";
  }
  addLinkDialogVisible.value = true;
};

/** 处理添加链接 */
const handleAddLink = async () => {
  if (!addLinkFormRef.value) return;

  try {
    await addLinkFormRef.value.validate();
    addLinkLoading.value = true;

    // 调用后端API添加链接
    const linkData = {
      title: addLinkForm.value.title,
      linkAddress: addLinkForm.value.linkAddress,
      description: addLinkForm.value.description || "个人添加的链接",
      state: addLinkForm.value.state,
      image: addLinkForm.value.image,
      personal: "1", // 标记为个人应用
      userId: userStore.userInfo?.id || "",
      param: addLinkForm.value.param || "{}",
      orgCode: userStore.userInfo?.orgCode || "PERSONAL",
      phone: userStore.userInfo?.phone || ""
    };

    // 调用添加链接API
    const res: any = await LinkApi.addLink(linkData);
    if (res.code === 0) {
      ElMessage.success("链接添加成功");
      addLinkDialogVisible.value = false;

      // 刷新列表
      await getList(false);
      await getFavoriteList();
    } else {
      ElMessage.error(res.msg || "添加链接失败");
    }
  } catch (error: any) {
    ElMessage.error(error?.message || "添加链接失败");
  } finally {
    addLinkLoading.value = false;
  }
};

/** 处理编辑链接 */
const handleUpdateLink = async () => {
  if (!addLinkFormRef.value) return;

  try {
    await addLinkFormRef.value.validate();
    addLinkLoading.value = true;
    // addLinkForm.value.userId = userStore.userId;

    // 调用编辑链接API
    const res: any = await LinkApi.updateLink(addLinkForm.value);
    if (res.code === 0) {
      ElMessage.success("链接编辑成功");
      addLinkDialogVisible.value = false;

      // 刷新列表
      await getList(false);
      await getFavoriteList();
    } else {
      ElMessage.error(res.msg || "编辑链接失败");
    }
  } catch (error: any) {
    ElMessage.error(error?.message || "编辑链接失败");
  } finally {
    addLinkLoading.value = false;
  }
};

/** 通过浏览器打开链接 */
const openLinkInBrowser = (item: AppLinkRespVO) => {
  if (!item.linkAddress) {
    ElMessage.warning("链接地址不存在");
    return;
  }

  // 确保链接以http://或https://开头
  let url = item.linkAddress;
  if (!url.startsWith("http://") && !url.startsWith("https://")) {
    url = "https://" + url;
  }

  try {
    // 如果是Electron环境，使用IPC调用本地浏览器
    if (isEE && ipc) {
      // 使用默认浏览器打开链接
      ipc
        .invoke("os/openUrlByLocalBrowser", {
          browserType: "default", // 使用默认浏览器
          url: url
        })
        .then(() => {
          ElMessage.success(`正在打开：${item.title}`);
        })
        .catch((error: any) => {
          console.error("调用本地浏览器失败:", error);
          // 如果IPC调用失败，回退到window.open
          window.open(url, "_blank");
          ElMessage.success(`正在打开：${item.title}`);
        });
    } else {
      // 非Electron环境，使用传统的window.open
      window.open(url, "_blank");
      ElMessage.success(`正在打开：${item.title}`);
    }
  } catch (error) {
    console.error("打开链接失败:", error);
    ElMessage.error("打开链接失败");
  }
};

/** 初始化 **/
onMounted(() => {
  getFavoriteList();
  getList(false);
  getLinkImgUrl();
});
</script>

<style lang="scss" scoped>
// 背景容器样式
.background-container {
  // 使用 absolute 让背景跟随父容器裁剪圆角
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f8fafc;
  z-index: -1;
  pointer-events: none;
}

// 主容器样式
.app-store-page {
  height: 100vh;
  padding: 0;
  margin: 0;
  position: relative;
  overflow: hidden;
}

// 可滚动容器样式
// .scrollable-container {
//   height: 100%;
//   overflow-y: auto;
//   overflow-x: hidden;
//   position: relative;
// }

// 搜索区域样式
.search-section {
  position: relative;
  padding: 20px 0;
  overflow: hidden;
  z-index: 1;
}

.search-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.search-form-container {
  background: white;
  border-radius: 24px;
  padding: 32px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.search-form {
  .search-row {
    display: flex;
    gap: 20px;
    align-items: flex-end;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
    }
  }

  .search-input-wrapper {
    flex: 1;
    min-width: 300px;

    @media (max-width: 768px) {
      min-width: 100%;
    }
  }

  .filter-controls {
    display: flex;
    gap: 16px;
    align-items: center;

    @media (max-width: 768px) {
      width: 100%;
      flex-wrap: wrap;
    }

    .action-buttons {
      display: flex;
      gap: 12px;

      @media (max-width: 768px) {
        width: 100%;

        .el-button {
          flex: 1;
        }
      }
    }
  }
}

// 输入框样式
.main-search-input {
  :deep(.el-input__wrapper) {
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    background: white;
    box-shadow: none;
    transition: all 0.3s ease;
    padding: 8px 16px;
    height: 44px;

    &:hover {
      border-color: #667eea;
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    }

    &.is-focus {
      border-color: #667eea;
      box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    }
  }

  :deep(.el-input__inner) {
    font-size: 14px;
    color: #1f2937;

    &::placeholder {
      color: #9ca3af;
    }
  }
}

// 选择框样式
.filter-select {
  :deep(.el-select__wrapper) {
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    background: white;
    transition: all 0.3s ease;

    &:hover {
      border-color: #667eea;
    }

    &.is-focus {
      border-color: #667eea;
    }
  }
}

// 按钮样式
.search-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 10px;
  padding: 10px 20px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  height: 44px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

.refresh-btn {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  padding: 10px 20px;
  color: #6b7280;
  font-weight: 600;
  transition: all 0.3s ease;
  height: 44px;

  &:hover {
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-2px);
  }
}

// 应用容器样式
.app-store-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px 40px;
  position: relative;
  z-index: 1;
}

.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

// 应用卡片样式
.app-card {
  position: relative;
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 200px;

  /* 取消hover效果 */
  &:hover {
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    border-color: rgba(0, 0, 0, 0.05);

    .hover-effect {
      opacity: 0;
    }

    .app-icon,
    .app-icon-avatar,
    .launch-button {
      transform: none;
    }
  }
}

.hover-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: 20px;
}

// 第一行：图标和名称
.app-header-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.app-icon-wrapper {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  flex-shrink: 0;
}

.app-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.app-icon-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  color: white;
  text-transform: uppercase;
  transition: transform 0.3s ease;
}

.app-title-wrapper {
  flex: 1;
  min-width: 0;

  .app-name {
    font-size: 16px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 4px 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .app-type {
    font-size: 11px;
    color: #6b7280;
    font-weight: 500;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
  }
}

// 第二行：描述
.app-description-row {
  margin-bottom: 12px;

  .app-description {
    margin: 0;
    font-size: 13px;
    line-height: 1.4;
    color: #6b7280;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 36px;
  }
}

// 第三行：元数据
.app-metadata-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .metadata-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #6b7280;

    .metadata-icon {
      font-size: 12px;
      color: #9ca3af;
    }

    .metadata-text {
      font-weight: 500;
    }
  }
}

// 第四行：启动按钮和启动方式
.app-action-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  margin-top: auto;
}

.launch-button {
  flex: 1;
  height: 36px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;

  &.favorite-active {
    background: #ffffff;
    color: #4b5563;
    border: 1px solid #e5e7eb;
    box-shadow: none;

    &:hover {
      background: #f9fafb;
      color: #1f2937;
    }
  }

  &.launch-enabled {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.25);

    &:hover {
      box-shadow: 0 4px 8px rgba(102, 126, 234, 0.35);
      transform: translateY(-1px);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  &.launch-disabled {
    background: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;

    &:hover {
      background: #e5e7eb;
    }
  }

  .launch-icon {
    font-size: 16px;
  }

  .launch-text {
    font-weight: 600;
  }
}

// 启动方式图标
.launch-methods {
  display: flex;
  align-items: center;
  gap: 8px;
}

.launch-method-item {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f1f5f9;
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: scale(0.95);
  }

  .method-icon {
    width: 18px;
    height: 18px;
    object-fit: contain;

    &.local-icon {
      font-size: 18px;
      color: #667eea;
    }
  }
}

// 管理员控制区域
.admin-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.admin-menu-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);

  &:hover {
    background: white;
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .el-icon {
    font-size: 16px;
    color: #6b7280;
  }
}

// 下拉菜单样式
:deep(.admin-dropdown) {
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  padding: 8px 0;

  .el-dropdown-menu__item {
    padding: 12px 16px;
    font-size: 14px;
    color: #374151;
    transition: all 0.15s ease;

    &:hover {
      background: #f3f4f6;
      color: #1f2937;
    }

    &.danger-action {
      color: #ef4444;

      &:hover {
        background: #fef2f2;
        color: #dc2626;
      }
    }

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
}

// 加载更多样式
.load-more-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 24px 60px;
  display: flex;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.load-more-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  min-width: 140px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(0);
  }

  &.is-loading {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.no-more-data {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #9ca3af;
  font-size: 14px;

  span {
    position: relative;
    padding: 0 20px;

    &::before,
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      width: 40px;
      height: 1px;
      background: #e5e7eb;
    }

    &::before {
      left: -50px;
    }

    &::after {
      right: -50px;
    }
  }
}

// 深色主题支持
.dark {
  .app-store-page {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }

  .search-background {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }

  .search-form-container {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .app-card {
    background: #1f2937;
    border-color: rgba(255, 255, 255, 0.1);

    &:hover {
      border-color: rgba(102, 126, 234, 0.3);
    }
  }

  .app-name {
    color: #f9fafb;
  }

  .app-type {
    background: #374151;
    color: #d1d5db;
  }

  .app-description {
    color: #d1d5db;
  }

  .metadata-text {
    color: #d1d5db;
  }

  .launch-method-item {
    background: #374151;
    border-color: #4b5563;

    &:hover {
      background: #4b5563;
      border-color: #667eea;
    }
  }

  .launch-disabled {
    background: #374151;
    color: #9ca3af;

    &:hover {
      background: #4b5563;
    }
  }

  .admin-menu-btn {
    background: rgba(31, 41, 55, 0.9);

    &:hover {
      background: rgba(31, 41, 55, 1);
    }

    .el-icon {
      color: #d1d5db;
    }
  }

  .no-more-data {
    color: #6b7280;

    span {
      &::before,
      &::after {
        background: #374151;
      }
    }
  }
}

// 响应式优化
@media (max-width: 1024px) {
  .search-content {
    padding: 0 20px;
  }

  .app-store-container {
    padding: 0 20px 30px;
  }

  .load-more-container {
    padding: 20px 20px 50px;
  }
}

@media (max-width: 768px) {
  .search-section {
    padding: 40px 0 30px;
  }

  .search-form-container {
    padding: 24px;
  }

  .app-card {
    padding: 20px;
    min-height: 180px;
  }

  .app-header-row {
    gap: 10px;
    margin-bottom: 10px;
  }

  .app-icon-wrapper {
    width: 44px;
    height: 44px;
  }

  .app-icon-avatar {
    font-size: 18px;
  }

  .app-name {
    font-size: 15px;
  }

  .app-description-row {
    margin-bottom: 10px;

    .app-description {
      font-size: 12px;
      min-height: 32px;
    }
  }

  .app-metadata-row {
    margin-bottom: 12px;

    .metadata-item {
      font-size: 10px;

      .metadata-icon {
        font-size: 11px;
      }
    }
  }

  .launch-button {
    height: 32px;
    font-size: 13px;
  }

  .launch-method-item {
    width: 28px;
    height: 28px;

    .method-icon {
      width: 16px;
      height: 16px;

      &.local-icon {
        font-size: 16px;
      }
    }
  }
}

@media (max-width: 480px) {
  .search-section {
    padding: 30px 0 20px;
  }

  .search-title {
    font-size: 28px;
  }

  .search-subtitle {
    font-size: 14px;
  }

  .search-form-container {
    padding: 20px;
    border-radius: 16px;
  }

  .app-store-container {
    padding: 0 16px 30px;
  }

  .app-card {
    padding: 16px;
    min-height: 160px;
  }

  .load-more-container {
    padding: 20px 16px 40px;
  }
}

// 滚动容器滚动条样式优化
// .scrollable-container {
//   /* Webkit 浏览器滚动条样式 */
//   &::-webkit-scrollbar {
//     width: 8px;
//   }

//   &::-webkit-scrollbar-track {
//     background: rgba(0, 0, 0, 0.1);
//     border-radius: 4px;
//   }

//   &::-webkit-scrollbar-thumb {
//     background: rgba(102, 126, 234, 0.3);
//     border-radius: 4px;
//     transition: all 0.3s ease;

//     &:hover {
//       background: rgba(102, 126, 234, 0.5);
//     }
//   }

//   /* Firefox 滚动条样式 */
//   scrollbar-width: thin;
//   scrollbar-color: rgba(102, 126, 234, 0.3) rgba(0, 0, 0, 0.1);
// }

/* 全局滚动条样式（更兼容的方式） */
// * {
//   &::-webkit-scrollbar {
//     width: 8px;
//   }

//   &::-webkit-scrollbar-track {
//     background: rgba(0, 0, 0, 0.1);
//     border-radius: 4px;
//   }

//   &::-webkit-scrollbar-thumb {
//     background: rgba(102, 126, 234, 0.3);
//     border-radius: 4px;
//     transition: all 0.3s ease;

//     &:hover {
//       background: rgba(102, 126, 234, 0.5);
//     }
//   }
// }
.favorite-section {
  max-width: 1200px;
  margin: 0 auto 24px;
  padding: 0 24px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 40px 0 20px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }
  }

  .favorite-title {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;

    &::before {
      content: "";
      display: inline-block;
      width: 4px;
      height: 24px;
      background: #667eea;
      border-radius: 2px;
    }

    @media (max-width: 768px) {
      justify-content: center;
    }
  }

  .add-link-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.25);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(102, 126, 234, 0.35);
    }

    &:active {
      transform: scale(0.98);
    }

    .el-icon {
      margin-right: 6px;
      font-size: 16px;
    }

    @media (max-width: 768px) {
      width: 100%;
      justify-content: center;
    }
  }

  /* 第一部分标题取消顶部外边距 */
  .favorite-section .favorite-title {
    margin-top: 0;
  }

  .favorite-container {
    background-color: #ffffff;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(102, 126, 234, 0.1);
    margin-bottom: 0;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(102, 126, 234, 0.2);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    }
  }

  .favorite-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 14px;
  }

  .favorite-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;

    &:hover {
      transform: translateY(-4px);

      .favorite-delete {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .favorite-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      cursor: pointer;
    }

    .favorite-delete {
      position: absolute;
      top: 8px;
      right: 8px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: rgba(239, 68, 68, 0.9);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      opacity: 0;
      transform: translateY(-5px);
      transition: all 0.3s ease;
      z-index: 2;
      box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);

      &:hover {
        background-color: rgba(220, 38, 38, 1);
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.5);
      }

      .el-icon {
        font-size: 14px;
      }
    }
  }

  .favorite-icon-wrapper {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 8px;
    position: relative;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

    &:hover {
      transform: scale(1.05);
    }
  }

  .favorite-icon {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
  }

  .favorite-icon-avatar {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 700;
    color: white;
    transition: all 0.3s ease;
  }

  .favorite-name {
    font-size: 12px;
    font-weight: 500;
    color: #4b5563;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    max-width: 70px;
    display: block;
    margin: 0 auto;
  }

  // 空常用链接区域样式
  .empty-favorites {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16px;
    padding: 30px;
    text-align: center;
    border: 1px dashed #e2e8f0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 120px;

    .empty-icon {
      font-size: 32px;
      color: #ff9800;
      margin-bottom: 16px;
    }

    .empty-text {
      color: #64748b;
      font-size: 14px;
      max-width: 400px;
      margin: 0 auto;
    }
  }
}
.favorite-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.favorite-item {
  width: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}
.favorite-item:hover {
  transform: translateY(-3px);
}
.favorite-icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  background-color: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}
.favorite-item:hover .favorite-icon-wrapper {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}
.favorite-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
}
.favorite-icon-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  color: white;
  text-transform: uppercase;
}
.favorite-name {
  font-size: 13px;
  color: #374151;
  text-align: center;
  line-height: 1.3;
  margin-top: 2px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 34px;
}

.favorite-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 40px 0 20px;
  display: flex;
  align-items: center;
  gap: 8px;

  &::before {
    content: "";
    display: inline-block;
    width: 4px;
    height: 24px;
    background: #667eea;
    border-radius: 2px;
  }
}

/* 第一部分标题设置顶部外边距 */
.favorite-section:first-of-type .section-header {
  margin-top: 20px;
}

// 添加链接对话框样式
:deep(.el-dialog) {
  border-radius: 16px;

  .el-dialog__header {
    padding: 24px 24px 0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .el-dialog__body {
    padding: 24px;
  }

  .el-dialog__footer {
    padding: 0 24px 24px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;

    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      box-shadow: 0 2px 4px rgba(102, 126, 234, 0.25);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.35);
      }
    }
  }
}

// 表单样式优化
:deep(.el-form-item) {
  margin-bottom: 24px;

  .el-form-item__label {
    font-weight: 600;
    color: #374151;
  }

  .el-input__wrapper {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;

    &:hover {
      border-color: #667eea;
    }

    &.is-focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }

  .el-textarea__inner {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;

    &:hover {
      border-color: #667eea;
    }

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
}
</style>
