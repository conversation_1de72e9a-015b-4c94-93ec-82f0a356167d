<template>
  <div class="emoji-wrappers border border-gray-200 rounded-lg">
    <el-scrollbar>
      <common-emoji class="emoji-wrapper" @insertEmoji="insertEmoji" v-if="activeIndex == 1" />
      <casic-emoji class="emoji-wrapper" @sendEmoji="sendEmoji" v-if="activeIndex == 0" />
    </el-scrollbar>
    <div class="flex border-t border-gray-200">
      <span
        class="linebox cursor-pointer mx-2 py-2 flex justify-center relative border-blue-500"
        :class="{ 'border-t-2': activeIndex == 0 }"
        @click="handleClick(0)"
      >
        <img :src="casicEmoji['抱抱']" alt="" class="w-8 h-8" />
      </span>
      <span
        class="linebox cursor-pointer mx-2 py-2 flex justify-center relative border-t-2"
        :class="{ 'border-blue-500': activeIndex == 1 }"
        @click="handleClick(1)"
      >
        <img :src="commonEmoji['[呲牙]']" alt="" class="w-8 h-8" />
      </span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from "vue";
import CasicEmoji from "./CasicEmoji.vue";
import CommonEmoji from "./CommonEmoji.vue";
import { useTalkStore } from "@/stores/modules/talk";

const talkStore = useTalkStore();
const commonEmoji = computed(() => talkStore.commonEmoji);
const casicEmoji = computed(() => talkStore.casicEmoji);

const activeIndex = ref(0);
// 处理文本插入类型的表情
const insertEmoji = (content: any) => {
  emojiHandler("insert", content);
};
// 处理直接发送类型的表情
const sendEmoji = (content: any) => {
  emojiHandler("send", content);
};

const emit = defineEmits(["emoji"]);
const emojiHandler = (type: any, content: any) => {
  emit("emoji", { type, content });
};
const handleClick = (event: any) => {
  activeIndex.value = event;
};
</script>

<style lang="less">
.linebox {
  margin-top: -1px;
}
.dark {
  .emoji-wrappers,
  .border-t {
    @apply border-gray-700;
  }
}
</style>
