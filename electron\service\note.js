'use strict'

const { Service } = require('ee-core')
const knex = require('../database/knex')

class NoteService extends Service {
  constructor(ctx) {
    super(ctx)
    console.log('闪记服务加载成功')
    this.db = knex
    this.tableName = 'quick_notes'
  }

  /**
   * 保存笔记
   * @param {string} content - 笔记内容
   * @param {string} title - 笔记标题
   * @param {number} id - 笔记ID，用于更新 (可选)
   * @param {string} originalContent - 原始内容 (可选)
   */
  async save(content, title, id, originalContent = null) {
    // 构建基础笔记数据
    const noteData = {
      content: content,
      title: title || content.substring(0, 20) + (content.length > 20 ? '...' : ''),
      char_count: content.length,
      word_count: this._countWords(content),
      original_content: originalContent || content,
      updated_at: this.db.fn.now()
    }

    try {
      if (id) {
        // 更新现有笔记
        await this.db(this.tableName).where({ id }).update(noteData)
        return await this.getById(id)
      } else {
        // 创建新笔记，添加created_at字段
        noteData.created_at = this.db.fn.now()
        const [insertedId] = await this.db(this.tableName).insert(noteData)
        return await this.getById(insertedId)
      }
    } catch (error) {
      console.error('保存笔记到数据库失败:', error)
      throw error
    }
  }

  /**
   * 获取所有笔记
   * @param {object} options - 查询选项
   * @param {string} options.userId - 用户ID
   * @param {string} options.noteType - 笔记类型
   * @param {string} options.category - 分类
   * @param {number} options.status - 状态
   * @param {number} options.limit - 限制数量
   * @param {number} options.offset - 偏移量
   */
  async getAll(options = {}) {
    try {
      let query = this.db(this.tableName)
        .select('*')
        .where('status', options.status || 1)
        .orderBy('created_at', 'desc')
      
      // 添加过滤条件
      if (options.userId) {
        query = query.where('user_id', options.userId)
      }
      if (options.noteType) {
        query = query.where('note_type', options.noteType)
      }
      if (options.category) {
        query = query.where('category', options.category)
      }
      if (options.limit) {
        query = query.limit(options.limit)
      }
      if (options.offset) {
        query = query.offset(options.offset)
      }

      const notes = await query
      
      // 处理标签字段
      return notes.map(note => ({
        ...note,
        tags: note.tags ? JSON.parse(note.tags) : []
      }))
    } catch (error) {
      console.error('从数据库获取笔记失败:', error)
      throw error
    }
  }

  /**
   * 根据ID获取笔记
   * @param {number} id - 笔记ID
   */
  async getById(id) {
    try {
      const note = await this.db(this.tableName).where({ id }).first()
      if (note && note.tags) {
        note.tags = JSON.parse(note.tags)
      }
      return note
    } catch (error) {
      console.error('根据ID获取笔记失败:', error)
      throw error
    }
  }

  /**
   * 删除笔记 (软删除)
   * @param {number} id - 笔记ID
   * @param {boolean} hardDelete - 是否硬删除
   */
  async delete(id, hardDelete = false) {
    try {
      if (hardDelete) {
        // 硬删除
        return await this.db(this.tableName).where({ id }).del()
      } else {
        // 软删除
        return await this.db(this.tableName)
          .where({ id })
          .update({ 
            status: 0, 
            updated_at: this.db.fn.now() 
          })
      }
    } catch (error) {
      console.error('删除笔记失败:', error)
      throw error
    }
  }

  /**
   * 搜索笔记
   * @param {string} keyword - 搜索关键词
   * @param {object} options - 搜索选项
   */
  async search(keyword, options = {}) {
    try {
      let query = this.db(this.tableName)
        .select('*')
        .where('status', options.status || 1)
        .where(function() {
          this.where('title', 'like', `%${keyword}%`)
            .orWhere('content', 'like', `%${keyword}%`)
            .orWhere('category', 'like', `%${keyword}%`)
        })
        .orderBy('created_at', 'desc')
      
      if (options.userId) {
        query = query.where('user_id', options.userId)
      }
      if (options.limit) {
        query = query.limit(options.limit)
      }

      const notes = await query
      return notes.map(note => ({
        ...note,
        tags: note.tags ? JSON.parse(note.tags) : []
      }))
    } catch (error) {
      console.error('搜索笔记失败:', error)
      throw error
    }
  }

  /**
   * 获取笔记统计信息
   * @param {string} userId - 用户ID
   */
  async getStats(userId) {
    try {
      const totalQuery = this.db(this.tableName)
        .count('id as count')
        .where('status', 1)
      
      if (userId) {
        totalQuery.where('user_id', userId)
      }

      const [totalResult] = await totalQuery
      const totalCount = totalResult.count

      // 今日统计
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const todayQuery = this.db(this.tableName)
        .count('id as count')
        .where('status', 1)
        .where('created_at', '>=', today.toISOString())
      
      if (userId) {
        todayQuery.where('user_id', userId)
      }

      const [todayResult] = await todayQuery
      const todayCount = todayResult.count

      return {
        totalCount,
        todayCount
      }
    } catch (error) {
      console.error('获取笔记统计失败:', error)
      throw error
    }
  }

  /**
   * 统计文字数量
   * @param {string} content - 内容
   * @private
   */
  _countWords(content) {
    if (!content) return 0
    // 简单的中英文字数统计
    const chineseCount = (content.match(/[\u4e00-\u9fa5]/g) || []).length
    const englishCount = (content.match(/[a-zA-Z]+/g) || []).join('').length
    return chineseCount + Math.ceil(englishCount / 4) // 4个英文字母约等于1个中文字
  }
}

NoteService.toString = () => "[class NoteService]"

module.exports = NoteService 