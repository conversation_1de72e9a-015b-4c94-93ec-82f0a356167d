import request from "@/api";

// 获取待办事项列表
export const getEventList = async () => {
  return await request.get("/admin-api/chat/workCalendar/page");
};
// 删除待办 {id}
export const deleteEvent = async (params: any) => {
  return await request.delete("/admin-api/chat/workCalendar/delete", { params });
};
// 新增待办 {title, contents, times, dateTime}
export const addEvent = async (params: any) => {
  return await request.post("/admin-api/chat/workCalendar/create", params);
};
// 修改待办 {id, title, contents, times, dateTime}
export const updateEvent = async (params: any) => {
  return await request.put("/admin-api/chat/workCalendar/update", params);
};
