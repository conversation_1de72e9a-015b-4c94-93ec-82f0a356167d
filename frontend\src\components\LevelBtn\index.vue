<template>
  <span :class="secret.className">{{ secret.label }}</span>
</template>

<script setup lang="ts" name="LevelBtn">
import { computed, PropType, ref } from "vue";
import { SecretLevelConverter } from "@/utils/secretLevelConverter";

const props = defineProps({
  message: {
    type: [String, Number],
    required: true
  },
  dataType: {
    type: String as PropType<"obj" | "user">,
    default: "obj"
  }
});

const secret: any = computed(() => {
  const level = Number(props.message);
  if (props.message && !isNaN(level)) return SecretLevelConverter.getLevelInfo(level, props.dataType);
  return {};
});
</script>

<style scoped lang="scss">
span {
  @apply rounded-md px-2 text-xs font-medium bg-gradient-to-r border;
}
span.feimi {
  @apply from-green-50 to-green-100 text-green-600 border-green-200;
}
span.neibu {
  @apply from-lime-50 to-lime-100 text-lime-600 border-lime-200;
}
span.putongshangmi {
  @apply from-pink-50 to-pink-100 text-pink-600 border-pink-200;
}
span.mimi {
  @apply from-yellow-50 to-yellow-100 text-yellow-600 border-yellow-200;
}
span.hexinshangmi {
  @apply from-rose-50 to-rose-100 text-rose-400 border-rose-200;
}
span.jimi {
  @apply from-orange-50 to-orange-100 text-orange-600 border-orange-200;
}
.dark {
  span.feimi {
    @apply from-green-900/30 to-green-800/30 text-green-500 border-green-800/50;
  }
  span.mimi {
    @apply from-yellow-900/30 to-yellow-800/30 text-yellow-500 border-yellow-800/50;
  }
  span.jimi {
    @apply from-orange-900/30 to-orange-800/30 text-orange-500 border-orange-800/50;
  }
}
</style>
