<template>
  <div class="widget-container h-full">
    <!-- 悬浮标题和按钮容器 -->
    <div class="absolute top-0 left-4 -translate-y-1/2 flex items-center gap-2 z-10">
      <!-- 小组件标题 -->
      <div
        class="widget-title bg-white dark:bg-gray-800 px-3 py-1.5 rounded-full shadow-md border border-gray-200 dark:border-gray-700 flex items-center gap-2"
      >
        <font-awesome-icon :icon="['fas', 'bell']" class="text-lg text-purple-500" />
        <span class="text-xs font-bold text-gray-700 dark:text-gray-200">智能通知</span>
      </div>

      <!-- 分类选择器 -->
      <div class="category-slider-wrapper !ml-0">
        <div class="slider-tabs">
          <div class="slider-track">
            <div class="slider-indicator" :style="sliderStyle"></div>
          </div>
          <div
            v-for="category in categories"
            :key="category"
            class="slider-tab"
            :class="{ active: activeCategory === category }"
            @click="selectCategory(category)"
          >
            {{ category }}
          </div>
        </div>
      </div>

      <!-- 全屏按钮 -->
      <button
        @click="maximizeWidget"
        class="w-7 h-7 rounded-full bg-purple-500 text-white flex items-center justify-center hover:bg-purple-600 transition-colors shadow-md"
        title="全屏查看"
      >
        <font-awesome-icon :icon="['fas', 'expand']" class="w-4 h-4" />
      </button>
    </div>

    <div class="p-3 pt-6 h-full flex flex-col">
      <!-- 通知列表 -->
      <div class="flex-1 overflow-hidden min-h-0 pt-1">
        <div v-if="isLoading" class="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
          <el-icon class="animate-spin text-xl">
            <Loading />
          </el-icon>
          <span class="ml-2 text-sm">加载中...</span>
        </div>
        
        <!-- 卡片网格布局：3列可滚动布局 -->
        <div v-else class="h-full overflow-y-auto overflow-x-hidden p-1 custom-scrollbar">
          <div class="grid grid-cols-3 gap-3 auto-rows-max">
            <!-- 通知卡片：包含标题、时间、内容、类型的结构化卡片 -->
            <div 
              v-for="notification in filteredNotifications" 
              :key="notification.id"
              class="notification-card bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700 shadow cursor-pointer relative overflow-hidden"
              @click="showNotificationDetails(notification)"
            >
              <!-- 卡片头部 -->
              <div class="flex items-start justify-between mb-3">
                <!-- 类型标签 -->
                <div class="flex items-center gap-2">
                  <font-awesome-icon 
                    v-if="notification.icon"
                    :icon="notification.icon" 
                    class="text-sm flex-shrink-0"
                    :class="{
                      'text-red-500': notification.priority === 'high',
                      'text-blue-500': notification.priority === 'medium',
                      'text-gray-400': notification.priority === 'low'
                    }"
                  />
                  <span class="text-xs px-2 py-1 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 font-medium">
                    {{ getNotificationType(notification.type) }}
                  </span>
                </div>
                <!-- 优先级指示器 -->
                <div 
                  v-if="notification.priority === 'high'"
                  class="w-2 h-2 bg-red-500 rounded-full"
                  title="高优先级"
                ></div>
              </div>
              
              <!-- 标题 -->
              <h3 class="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2 line-clamp-2 leading-tight">
                {{ notification.title || '智能通知' }}
              </h3>
              
              <!-- 内容 -->
              <p class="text-xs text-gray-600 dark:text-gray-400 line-clamp-3 leading-relaxed mb-3">
                {{ notification.content }}
              </p>
              
              <!-- 时间 -->
              <div class="flex items-center justify-between text-xs text-gray-400 dark:text-gray-500">
                <span>{{ notification.time }}</span>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="filteredNotifications.length === 0" class="flex items-center justify-center h-full">
            <div class="text-center text-gray-400 dark:text-gray-500">
              <div class="mb-2">
                <font-awesome-icon :icon="['fas', 'bell-slash']" class="text-3xl text-blue-400 dark:text-blue-300 opacity-70" />
              </div>
              <div class="text-sm">暂无新通知</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 全屏对话框 -->
  <el-dialog
    v-model="isMaximized"
    width="800px"
    top="10vh"
    class="custom-dialog fullscreen-notification-dialog"
    :show-close="false"
    :close-on-click-modal="false"
    append-to-body
  >
    <!-- Header -->
    <template #header>
      <button class="close-btn" @click="closeMaximize">
        <font-awesome-icon :icon="['fas', 'compress']" />
      </button>
      <div class="flex items-center gap-2">
        <font-awesome-icon :icon="['fas', 'bell']" class="text-lg text-purple-500" />
        <span class="title">智能通知</span>
      </div>
    </template>

    <!-- Body -->
    <div class="p-2">
      <div class="flex items-center justify-between mb-4">
        <!-- 分类选择器 -->
        <div class="category-slider-wrapper is-fullscreen">
          <div class="slider-tabs">
            <div class="slider-track">
              <div class="slider-indicator" :style="sliderStyle"></div>
            </div>
            <div 
              v-for="category in categories" 
              :key="category"
              class="slider-tab"
              :class="{ active: activeCategory === category }"
              @click="selectCategory(category)"
            >
              {{ category }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 通知列表 -->
      <div class="overflow-y-auto max-h-96">
        <div v-if="isLoading" class="flex items-center justify-center h-48 text-gray-500 dark:text-gray-400">
          <el-icon class="animate-spin text-2xl">
            <Loading />
          </el-icon>
          <span class="ml-3 text-lg">加载中...</span>
        </div>
        
        <!-- 全屏卡片网格布局 -->
        <div v-else class="p-2">
          <div class="grid grid-cols-4 gap-4 auto-rows-max">
            <!-- 全屏通知卡片 -->
            <div 
              v-for="notification in filteredNotifications" 
              :key="notification.id"
              class="notification-card bg-white dark:bg-gray-800 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow cursor-pointer relative overflow-hidden"
              @click="showNotificationDetails(notification)"
            >
              <!-- 卡片头部 -->
              <div class="flex items-start justify-between mb-4">
                <!-- 类型标签 -->
                <div class="flex items-center gap-2">
                  <font-awesome-icon 
                    v-if="notification.icon"
                    :icon="notification.icon" 
                    class="text-base flex-shrink-0"
                    :class="{
                      'text-red-500': notification.priority === 'high',
                      'text-blue-500': notification.priority === 'medium',
                      'text-gray-400': notification.priority === 'low'
                    }"
                  />
                  <span class="text-sm px-3 py-1.5 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 font-medium">
                    {{ getNotificationType(notification.type) }}
                  </span>
                </div>
                <!-- 优先级指示器 -->
                <div 
                  v-if="notification.priority === 'high'"
                  class="w-2.5 h-2.5 bg-red-500 rounded-full animate-pulse"
                  title="高优先级"
                ></div>
              </div>
              
              <!-- 标题 -->
              <h3 class="text-base font-semibold text-gray-800 dark:text-gray-200 mb-3 line-clamp-2 leading-tight">
                {{ notification.title || '智能通知' }}
              </h3>
              
              <!-- 内容 -->
              <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-3 leading-relaxed mb-4">
                {{ notification.content }}
              </p>
              
              <!-- 时间 -->
              <div class="flex items-center justify-between text-sm text-gray-400 dark:text-gray-500">
                <span>{{ notification.time }}</span>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="filteredNotifications.length === 0" class="flex items-center justify-center h-48">
            <div class="text-center text-gray-500">
              <div class="mb-4">
                <font-awesome-icon :icon="['fas', 'bell-slash']" class="text-6xl text-blue-400 dark:text-blue-300 opacity-70" />
              </div>
              <div class="text-lg">暂无新通知</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>

  <!-- Detail Modal -->
  <Teleport to="body">
    <div v-if="isDetailModalVisible && selectedNotification" class="detail-modal" @click="closeDetailModal">
      <div class="detail-content" @click.stop>
        <button @click="closeDetailModal" class="close-btn">&times;</button>
        <div class="detail-header">
          <font-awesome-icon
            v-if="selectedNotification.icon"
            :icon="selectedNotification.icon"
            class="text-3xl mr-4"
            :class="{
              'text-red-500': selectedNotification.priority === 'high',
              'text-blue-500': selectedNotification.priority === 'medium',
              'text-gray-400': selectedNotification.priority === 'low'
            }"
          />
          <div class="flex-col">
            <h3 class="text-xl font-semibold dark:text-gray-50">通知详情</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">时间: {{ selectedNotification.time }}</p>
          </div>
        </div>
        <div class="detail-body">
          <p>{{ selectedNotification.content }}</p>
        </div>
        <div class="detail-footer">
          <p class="text-xs text-gray-400 dark:text-gray-500">
            优先级: {{ selectedNotification.priority }} | 分类: {{ selectedNotification.category }}
          </p>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { Loading } from "@element-plus/icons-vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { library } from "@fortawesome/fontawesome-svg-core";
import {
  faCalendarAlt,
  faTasks,
  faEnvelope,
  faBell,
  faTriangleExclamation,
  faBellSlash,
  faCompress
} from "@fortawesome/free-solid-svg-icons";
import { useAiNoticeStore } from "@/stores/modules/aiNotice";
import { storeToRefs } from "pinia";
import { AiSummaryNotification } from "@/stores/interface";

// Local FontAwesome icon registration
library.add(faCalendarAlt, faTasks, faEnvelope, faBell, faTriangleExclamation, faBellSlash, faCompress);

const isLoading = ref(false);
const aiNoticeStore = useAiNoticeStore();
const { aiNoticeList: notifications } = storeToRefs(aiNoticeStore);

const categories = ["全部", "会议"];
const activeCategory = ref("全部");

const currentIndex = computed(() => {
  return categories.findIndex(c => c === activeCategory.value);
});

const sliderStyle = computed(() => {
  const tabCount = categories.length;
  const tabWidth = 100 / tabCount;
  return {
    width: `${tabWidth}%`,
    transform: `translateX(${currentIndex.value * 100}%)`
  };
});

const selectCategory = (category: string) => {
  activeCategory.value = category;
};

const filteredNotifications = computed(() => {
  if (activeCategory.value === "全部") {
    return notifications.value;
  }
  return notifications.value.filter(n => n.category === activeCategory.value);
});

const refreshNotifications = async () => {
  isLoading.value = true;
  await aiNoticeStore.fetchAiNoticeList();
  isLoading.value = false;
};

const isMaximized = ref(false)

const selectedNotification = ref<AiSummaryNotification | null>(null);
const isDetailModalVisible = ref(false);

const showNotificationDetails = (notification: AiSummaryNotification) => {
  selectedNotification.value = notification;
  isDetailModalVisible.value = true;
};

const closeDetailModal = () => {
  isDetailModalVisible.value = false;
  setTimeout(() => {
    selectedNotification.value = null;
  }, 300);
};

const maximizeWidget = () => {
  isMaximized.value = true
}

const closeMaximize = () => {
  isMaximized.value = false
}

// 获取通知类型显示文本
const getNotificationType = (type: number): string => {
  const typeMap: { [key: number]: string } = {
    1: '会议',
    2: '任务',
    3: '邮件',
    4: '系统',
    5: '提醒'
  }
  return typeMap[type] || '通知'
}

onMounted(() => {
  refreshNotifications();
});
</script>

<style scoped lang="scss">
.widget-container {
  overflow: visible;
}

.icon-shadow {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.widget-btn {
  @apply bg-gray-200/60 dark:bg-gray-900/30 text-gray-600 dark:text-gray-300;
  @apply hover:bg-gray-300/70 dark:hover:bg-gray-900/50;
  @apply rounded-lg w-8 h-8 flex items-center justify-center transition-colors duration-200 cursor-pointer;
  border-color: rgb(255 255 255 / 0.9);
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;

  &::-webkit-scrollbar {
    width: 4px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }
}

.dark .custom-scrollbar {
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
  }
}

// 卡片样式
.notification-card {
  min-height: 140px;
}

// 文本截断样式
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 全屏对话框样式
.fullscreen-notification-dialog {
  .el-dialog__body {
    max-height: 60vh;
    overflow-y: auto;
  }
}

.category-slider-wrapper {
  @apply w-24 ml-4;
  .slider-tabs {
    @apply relative w-full bg-gray-200/80 dark:bg-gray-800/80 rounded-full p-0.5 flex;
    .slider-track {
      @apply absolute inset-0.5 pointer-events-none;
      .slider-indicator {
        @apply h-full bg-white dark:bg-gray-700 rounded-full shadow-sm transition-transform duration-300 ease-out;
      }
    }
    .slider-tab {
      @apply relative z-10 text-center py-1 px-2 text-xs font-medium cursor-pointer transition-colors duration-200 flex-1 whitespace-nowrap;
      color: #6b7280;
      &:hover {
        color: #374151;
      }
      &.active {
        color: #3b82f6;
        font-weight: 600;
      }
    }
  }

  &.is-fullscreen {
    @apply w-auto flex-1;
    .slider-tabs {
      @apply p-1;
      .slider-indicator {
        @apply rounded-full;
      }
      .slider-tab {
        @apply py-1.5 px-3 text-sm;
      }
    }
  }
}

.dark .category-slider-wrapper {
  .slider-tabs {
    .slider-tab {
      color: #9ca3af;
      &:hover {
        color: #f3f4f6;
      }
      &.active {
        color: #60a5fa;
      }
    }
  }
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;

  &::-webkit-scrollbar {
    width: 4px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }
}

.dark .custom-scrollbar {
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
  }
}

.detail-modal {
  @apply fixed inset-0 bg-black/40 z-[10001] flex items-center justify-center;
  animation: fadeIn 0.3s ease-out;
}

.detail-content {
  @apply bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-6 w-[90vw] max-w-lg relative;
  animation: slideUp 0.35s ease-out;
  will-change: transform, opacity;
  .close-btn {
    @apply absolute top-4 right-4 text-2xl text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors;
  }
  .detail-header {
    @apply flex items-center pb-4 mb-4 border-b border-gray-200 dark:border-gray-700;
  }
  .detail-body {
    @apply mb-4 text-base text-gray-700 dark:text-gray-200 leading-relaxed;
  }
  .detail-footer {
    @apply text-right;
  }
}
</style>
