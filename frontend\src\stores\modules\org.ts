import { defineStore } from "pinia";
import piniaPersistConfig from "../helper/persist";
import * as orgApi from "@/api/modules/org";
import { useContactsStore } from "./contacts";

interface Org {
  orgTree: Array<any>;
  orgTreeByLevel: Array<any>;
  orgUser: Record<string, any>;
}

export const useOrgStore = defineStore("lark-org", {
  state: (): Org => ({
    orgTree: [],
    orgTreeByLevel: [],
    orgUser: {}
  }),
  getters: {},
  actions: {
    // 获取部门内人员列表
    async getOrgUser(params: any) {
      const contactsStore = useContactsStore();
      const res: any = await orgApi.getOrgUser(params);
      if (res.code == 0) {
        res.data.sort((a: any, b: any) => {
          if (a.orderId === b.orderId) {
            // 如果orderId相同，则根据online属性排序：true优先于false
            return b.online - a.online;
          } else {
            // orderId不同，按orderId升序排列
            return a.orderId - b.orderId;
          }
        });
        res.data?.forEach((item: any) => {
          let fileds: any = ["avatar", 'isOnline']
          if (item.online === null) fileds = ["avatar"]
          // 更新用户状态
          contactsStore.updateContact(
            {
              id: item.id,
              name: item.name,
              avatar: item.avatar,
              secretLevel: item.secretLevel,
              isOnline: item.online == "on"
            },
            fileds
          );
        });
        this.orgUser[params.orgCode] = res.data;
      } else {
        this.orgUser[params.orgCode] = [];
      }
    },
    // 组织机构树形数据
    async getOrgData(params: any) {
      const res: any = await orgApi.getOrgData(params);
      if (res.code == 0) {
        if (params.level > 2) {
          this.orgTree = res.data;
        } else {
          this.orgTreeByLevel = res.data;
        }
      }
    }
  },
  persist: piniaPersistConfig("lark-org-session")
});
