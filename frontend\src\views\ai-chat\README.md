# AI聊天组件使用说明

## 概述

AI聊天组件是一个功能完整的AI对话界面，支持动态配置表单和实时聊天功能。组件完全依赖传入的配置数据，不包含任何模拟数据。

## 🚀 新增功能特性 (v2.0.0)

### 1. 流式响应控制
- **停止生成按钮**: 用户可以随时中断AI回复生成
- **重新生成功能**: 支持重新生成AI回复
- **生成状态管理**: 完善的消息状态跟踪（generating, completed, stopped, error）

### 2. 用户体验优化
- **加载状态指示器**: "正在思考..."动画提示
- **智能按钮禁用**: 生成过程中自动禁用发送按钮和输入框
- **消息操作按钮**: 鼠标悬停显示操作选项
- **实时状态反馈**: 清晰的视觉状态指示

### 3. 错误处理增强
- **网络异常处理**: 自动检测连接中断
- **超时处理**: 防止长时间无响应
- **重试机制**: 支持重新生成失败的回复
- **用户友好的错误提示**: 清晰的错误信息展示

## 主要特性

- ✅ 动态配置表单渲染
- ✅ 支持多种输入类型（text、select、number、range、file）
- ✅ 配置模式和聊天模式切换
- ✅ 实时AI对话与流式响应
- ✅ 响应式设计
- ✅ 完全依赖传入数据，无模拟数据
- ✅ **新增**: 停止生成和重新生成功能
- ✅ **新增**: 完善的消息状态管理
- ✅ **新增**: 增强的错误处理机制

## 配置数据结构

### AppConfigData 接口

```typescript
interface AppConfigData {
  appInfo: AppInfo
  inputs: Record<string, any>
  configFields?: FormField[]
}

interface AppInfo {
  id: number
  appId?: string
  name: string
  description: string
  type: number
  status?: number
  icon?: string
  sort?: number
  createTime?: string
}

interface FormField {
  label: string        // 字段显示标签
  name: string         // 字段名称
  class: string        // CSS样式类名
  value: string | number // 默认值
  type?: string        // 输入类型: 'text' | 'select' | 'number' | 'range' | 'file'
  options?: Array<{label: string, value: string}> // select类型的选项
  min?: number         // range类型的最小值
  max?: number         // range类型的最大值
  step?: number        // range类型的步长
  accept?: string      // file类型的接受文件类型
  multiple?: boolean   // file类型是否支持多文件
}

// 新增: 聊天消息接口
interface ChatMessage {
  id: string                    // 消息唯一ID
  role: 'user' | 'ai'          // 消息角色
  content: string              // 消息内容
  timestamp: number            // 时间戳
  isTyping?: boolean           // 是否正在打字
  fullContent?: string         // 完整内容（用于打字效果）
  status?: 'generating' | 'completed' | 'stopped' | 'error' // 消息状态
  canRegenerate?: boolean      // 是否可以重新生成
}
```

## 使用方法

### 1. 通过window对象传递配置（推荐用于弹窗）

```javascript
// 在打开AI聊天窗口前设置配置数据
window.aiAppConfig = {
  appInfo: {
    id: 1,
    appId: 'app_1',
    name: '智能写作助手',
    description: '基于AI的智能写作助手',
    type: 1
  },
  inputs: {
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 2000
  },
  configFields: [
    {
      label: '模型选择',
      name: 'model',
      class: 'form-control',
      value: 'gpt-3.5-turbo',
      type: 'select',
      options: [
        { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },
        { label: 'GPT-4', value: 'gpt-4' }
      ]
    }
    // ... 更多字段
  ]
}
```

### 2. 通过IPC消息传递配置（Electron环境）

```javascript
// 发送配置数据
ipc.send('ai-app-config', configData)

// 或者在渲染进程中监听
ipc.on('ai-app-config', (event, data) => {
  // 组件会自动接收这个数据
})
```

## 支持的表单字段类型

### 1. 文本输入 (text)
```javascript
{
  label: '系统提示词',
  name: 'systemPrompt',
  class: 'form-control',
  value: '你是一个AI助手',
  type: 'text'
}
```

### 2. 选择框 (select)
```javascript
{
  label: '模型选择',
  name: 'model',
  class: 'form-control',
  value: 'gpt-3.5-turbo',
  type: 'select',
  options: [
    { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },
    { label: 'GPT-4', value: 'gpt-4' }
  ]
}
```

### 3. 数字输入 (number)
```javascript
{
  label: '最大输出长度',
  name: 'maxTokens',
  class: 'form-control',
  value: 2000,
  type: 'number'
}
```

### 4. 滑块 (range)
```javascript
{
  label: '创意程度',
  name: 'temperature',
  class: 'form-control',
  value: 0.7,
  type: 'range',
  min: 0,
  max: 1,
  step: 0.1
}
```

## API接口要求

组件会调用 `sendAiChatMessage` API，请求参数包括：

```javascript
{
  message: string,           // 用户消息
  conversation_id: string,   // 对话ID
  app_id?: number,          // 应用ID（如果有配置数据）
  appId?: string,           // 应用标识符（如果有配置数据）
  inputs?: Record<string, any> // 当前配置（如果有配置数据）
}
```

## 状态管理

### 组件状态
- `hasConfigData`: 是否有配置数据
- `isConfigMode`: 是否处于配置模式
- `isAiTyping`: AI是否正在输入
- `messages`: 聊天消息列表
- `currentConfig`: 当前配置值

### 错误处理
- 无配置数据时显示等待提示
- API调用失败时显示错误消息
- 输入框和按钮在无配置时自动禁用

## 样式特性

- 蓝橙渐变色主题
- 响应式设计
- 圆角边框和阴影效果
- 动画过渡效果
- 禁用状态视觉反馈

## 注意事项

1. **必须传入配置数据**：组件不包含任何模拟数据，必须通过上述方法传入真实配置
2. **配置数据格式**：确保配置数据符合 `AppConfigData` 接口规范
3. **API接口**：确保后端API支持接收配置参数
4. **错误处理**：组件会优雅处理各种错误情况，但建议在传入数据前进行验证

## 示例文件

参考 `test-config.js` 文件查看完整的配置数据示例和验证函数。

# Dify 流式数据解析功能

## 概述

本功能为 AI 聊天系统添加了对 Dify 格式流式数据的支持，能够正确解析服务端返回的 `DifyStreamMessageDTO` 对象。

## 功能特性

### 1. 支持的数据格式

#### Dify 格式（新增）
```json
{
  "content": "你好！我是AI助手，很高兴为您服务。",
  "message_id": "msg_123456789",
  "task_id": "task_987654321",
  "conversation_id": "conv_123456789",
  "created_at": 1703123456789
}
```

#### 传统格式（向后兼容）
```json
{
  "type": "text",
  "content": "这是传统格式的测试消息",
  "conversationId": "legacy_conv_123",
  "timestamp": 1703123456789
}
```

### 2. 文件上传功能

- **多文件支持**: 支持同时上传多个文件
- **文件类型验证**: 支持指定接受的文件类型
- **文件大小限制**: 可配置文件大小限制（默认10MB）
- **实时上传进度**: 显示文件上传状态和进度
- **文件预览**: 显示已上传文件的信息（名称、大小、类型）
- **文件管理**: 支持移除单个文件或清空所有文件
- **与消息集成**: 文件信息会随消息一起发送到AI后端

### 3. 核心功能

- **自动格式识别**: 自动识别并解析 Dify 格式的流式数据
- **会话ID管理**: 自动提取和更新会话ID，维持对话上下文
- **实时打字效果**: AI回复消息支持优雅的打字动画效果
- **性能优化**: 使用CSS硬件加速，确保流畅的用户体验
- **向后兼容**: 保持对传统格式数据的支持
- **错误处理**: 完善的错误处理和日志记录

## 使用方法

### 基本使用

```typescript
import { sendAiStreamChatMessage } from '@/api/modules/ai/chat'

const controller = sendAiStreamChatMessage(
  {
    query: "你好，请介绍一下你自己",
    conversationId: "conversation-123",
    app_id: 1,
    inputs: { model: "gpt-3.5-turbo" }
  },
  {
    onData: (chunk) => {
      console.log('接收到数据:', chunk.content)
      console.log('会话ID:', chunk.conversationId)
      console.log('时间戳:', chunk.timestamp)
      // 在这里更新UI，例如追加到聊天界面
    },
    onError: (error) => {
      console.error('流式聊天出错:', error)
      // 处理错误，显示错误信息
    },
    onComplete: () => {
      console.log('流式聊天完成')
      // 聊天完成后的处理
    }
  }
)
```

### 文件上传功能

现在支持在聊天中上传文件并与消息一起发送到后端：

```typescript
import { sendAiStreamChatMessage, type FileInfo } from '@/api/modules/ai/chat'

// 文件信息示例
const files: FileInfo[] = [
  {
    fileId: "file_123",
    fileName: "document.pdf",
    fileUrl: "https://example.com/files/document.pdf",
    fileSize: 1024000,
    fileType: "application/pdf"
  }
]

const controller = sendAiStreamChatMessage(
  {
    query: "请分析这个文档的内容",
    conversationId: "conversation-123",
    app_id: 1,
    inputs: { model: "gpt-3.5-turbo" },
    files: files // 添加文件信息
  },
  {
    onData: (chunk) => {
      console.log('AI正在分析文件:', chunk.content)
    },
    onError: (error) => {
      console.error('文件分析出错:', error)
    },
    onComplete: () => {
      console.log('文件分析完成')
    }
  }
)

// 取消请求
// controller.abort()

// 检查状态
// console.log('是否活跃:', controller.isActive)
```

### 在 AI 聊天组件中的使用

AI 聊天组件已经集成了对 Dify 格式和打字效果的支持：

1. **自动会话ID更新**: 当接收到包含 `conversation_id` 的 Dify 数据时，会自动更新本地会话ID
2. **实时打字效果**: AI回复消息会以打字动画的形式逐步显示，提供更好的用户体验
3. **流式内容累积**: 实时累积 AI 回复内容并更新界面
4. **性能优化**: 使用消息级别的打字状态管理，避免不必要的重渲染
5. **错误处理**: 完善的错误处理和用户提示

## 文件上传功能

### FileUpload 组件使用

```vue
<template>
  <FileUpload
    :accept="'image/*,application/pdf'"
    :multiple="true"
    :max-size="10 * 1024 * 1024"
    :max-files="5"
    placeholder="请选择文件"
    @file-selected="handleFileSelected"
    @error="handleFileError"
  />
</template>

<script setup>
import FileUpload from './components/FileUpload.vue'

const handleFileSelected = (files) => {
  console.log('选择的文件:', files)
  // 文件会自动上传到服务器
}

const handleFileError = (error) => {
  console.error('文件错误:', error)
}
</script>
```

### 文件上传配置

```typescript
interface FileUploadProps {
  accept?: string        // 接受的文件类型，如 'image/*,application/pdf'
  multiple?: boolean     // 是否支持多文件选择
  placeholder?: string   // 占位符文本
  maxSize?: number      // 最大文件大小（字节），默认 10MB
  maxFiles?: number     // 最大文件数量，默认 5
}
```

### 文件信息格式

```typescript
interface FileInfo {
  fileId: string      // 服务器返回的文件ID
  fileName: string    // 文件名
  fileUrl: string     // 文件访问URL
  fileSize: number    // 文件大小（字节）
  fileType: string    // 文件MIME类型
}
```

### 文件上传流程

1. **文件选择**: 用户通过 FileUpload 组件选择文件
2. **文件验证**: 验证文件类型、大小等限制
3. **自动上传**: 文件自动上传到服务器（`/admin-api/infra/file/chunkedUpload`）
4. **存储信息**: 上传成功后，文件信息存储在组件状态中
5. **随消息发送**: 发送消息时，文件信息会包含在请求中
6. **清理状态**: 消息发送成功后，自动清空已上传的文件列表

## 技术实现

### 1. 数据解析流程

```typescript
// 1. 接收 SSE 格式的数据行
const line = "data: {\"content\":\"Hello\",\"message_id\":\"msg_123\",...}"

// 2. 提取 JSON 数据
const data = line.substring(6).trim()

// 3. 解析 JSON
const jsonData = JSON.parse(data)

// 4. 格式识别和处理
if (isDifyStreamMessage(jsonData)) {
  // 处理 Dify 格式
  const chunk = {
    type: 'text',
    content: jsonData.content,
    conversationId: jsonData.conversation_id,
    timestamp: jsonData.created_at
  }
} else {
  // 处理传统格式
  const chunk = {
    type: jsonData.type || 'text',
    content: jsonData.content,
    conversationId: jsonData.conversationId,
    timestamp: jsonData.timestamp
  }
}
```

### 2. 类型定义

```typescript
// Dify 流式消息 DTO 接口
export interface DifyStreamMessageDTO {
  content: string;
  message_id: string;
  task_id: string;
  conversation_id: string;
  created_at: number;
}

// 流式数据块类型
export interface StreamDataChunk {
  type: 'text' | 'error' | 'done';
  content: string;
  conversationId?: string;
  timestamp?: number;
}
```

### 3. 格式识别函数

```typescript
const isDifyStreamMessage = (data: any): data is DifyStreamMessageDTO => {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof data.content === 'string' &&
    typeof data.message_id === 'string' &&
    typeof data.task_id === 'string' &&
    typeof data.conversation_id === 'string' &&
    (typeof data.created_at === 'number' || data.created_at === undefined)
  )
}
```

### 4. 打字效果组件

```typescript
// TypingEffect 组件使用示例
<TypingEffect 
  :text="message.fullContent"
  :speed="25"
  :auto-start="message.isTyping"
  @complete="onTypingComplete"
/>

// 组件属性
interface Props {
  text: string              // 要显示的文本
  speed?: number           // 打字速度（毫秒/字符），默认30
  delay?: number           // 开始前的延迟（毫秒），默认0
  cursorBlink?: boolean    // 是否闪烁光标，默认true
  autoStart?: boolean      // 是否自动开始，默认true
}

// 组件事件
interface Emits {
  complete: []  // 打字完成时触发
  start: []     // 开始打字时触发
}
```

## 测试

### 测试页面

提供了专门的测试页面 `DifyStreamTest.vue` 来验证解析功能：

1. **Dify 格式测试**: 测试 Dify 格式数据的解析
2. **传统格式测试**: 测试传统格式数据的解析
3. **实时日志**: 显示解析过程的详细日志
4. **结果展示**: 展示解析后的数据块

### 运行测试

1. 访问测试页面
2. 点击"测试 Dify 解析"按钮
3. 查看解析结果和日志
4. 验证数据格式和内容是否正确

## 注意事项

1. **会话ID管理**: 确保在接收到新的会话ID时正确更新本地状态
2. **错误处理**: 对解析失败的情况进行适当的错误处理
3. **性能优化**: 大量流式数据时注意内存使用和性能优化
4. **向后兼容**: 保持对现有格式的支持，避免破坏现有功能

## 更新日志

- **v1.1.0**: 新增打字效果功能
  - 添加了高性能的 `TypingEffect` 组件
  - 实现了实时打字效果，只显示 content 内容
  - 优化了性能，使用 CSS 硬件加速
  - 支持可配置的打字速度和光标闪烁
  - 提供了打字效果测试页面

- **v2.1.0**: 代码重构优化，提高可维护性和性能
  - 抽取工具函数，减少代码重复
  - 优化Vue响应式更新机制，解决流式显示问题
  - 简化模板逻辑，提高渲染性能
  - 统一错误处理和状态管理
  - 改进代码结构和注释

- **v2.0.0**: 完善流式响应功能
  - 新增停止生成和重新生成功能
  - 完善消息状态管理
  - 优化用户交互体验
  - 添加消息操作按钮

- **v1.0.0**: 初始版本，支持 Dify 格式流式数据解析
  - 添加了 `DifyStreamMessageDTO` 接口定义
  - 更新了 `processStreamData` 函数以支持 Dify 格式
  - 添加了格式识别函数 `isDifyStreamMessage`
  - 在 AI 聊天组件中集成了会话ID自动更新功能
  - 提供了完整的测试页面和文档

## 🏗️ 代码架构

### 核心工具函数
```typescript
// 消息管理
generateMessageId(): string
createAiMessage(): ChatMessage
createErrorMessage(content: string): ChatMessage
updateMessageContent(messageId: string, content: string): void
updateMessageStatus(messageId: string, status: ChatMessage['status'], content?: string): void

// 状态管理
cleanupStreamingState(): void
stopGeneration(): void
regenerateResponse(messageId: string): Promise<void>
```

### 响应式状态
```typescript
const messages = ref<ChatMessage[]>([])           // 消息列表
const currentStreamController = ref<any>(null)   // 流控制器
const isGenerating = ref(false)                  // 生成状态
const lastUserMessage = ref<string>('')          // 最后用户消息
```
