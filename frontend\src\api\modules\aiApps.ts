import http from "@/api";

/**
 * AI 应用相关 API 接口
 */

// AI 应用数据接口定义
export interface AiAppData {
  id: number;
  appId: string;
  name: string;
  icon: string;
  description?: string;
  category?: string;
  tags?: string[];
  type?: number;
  status?: number;
  createTime?: string;
  isHot?: boolean;
  rating?: number;
  themeColor?: string;
  inputs?: any;
  sort?: number;
}

// 分页请求参数接口
export interface AppPageReqVO {
  pageNo?: number;
  pageSize?: number;
  name?: string;
  category?: string;
  status?: number;
}

// 分页结果接口
export interface PageResult<T> {
  list: T[];
  total: number;
}

// 获取可用的 AI 应用列表
export const getAvailableApps = () => {
  return http.get<AiAppData[]>("/app-api/ai/app/available");
};

// 获取用户已添加的 AI 应用列表
export const getUserApps = () => {
  return http.get<AiAppData[]>("/app-api/ai/app/user");
};

// 添加用户到微应用
export const addUserApp = (appId: number) => {
  return http.post("/app-api/ai/app/add-user", { appId });
};

// 移除用户与微应用的关联
export const removeUserApp = (appId: number) => {
  return http.delete("/app-api/ai/app/remove-user", { 
    params: { appId } 
  });
};

// 获取个人的 AI 微应用列表
export const getMyAppList = () => {
  return http.get<AiAppData[]>("/app-api/ai/app/my-list");
};

// 获取全部微应用分页列表
export const getAppPage = (params: AppPageReqVO) => {
  return http.get<PageResult<AiAppData>>("/app-api/ai/app/page", { params });
};
