// 生成随机Id
export function generateRandomId(length: number) {
  let result = "";
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  const charactersLength = characters.length;

  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }

  return result;
}

// 防抖
export function debounce(this: any, fn: Function, delay: number) {
  let timer: any;
  const _this = this;
  return function (...args: any[]) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(_this, args);
    }, delay);
  };
}

// 节流
export function throttle(this: any, fn: Function, delay: number) {
  let timer: any;
  const _this = this;
  return function (...args: any[]) {
    if (timer) return;
    timer = setTimeout(() => {
      fn.apply(_this, args);
      timer = null;
    }, delay);
  };
}

export interface fileDownloadType {
  url: string;
  name: string;
}

export const fileDownload = async (response: any, item: any) => {
  let blobFile = new Blob([response.data]);
  const url = window.URL.createObjectURL(blobFile);
  const a = document.createElement("a");
  a.href = url;
  a.download = item.fileName;
  document.body.appendChild(a);
  a.click();
  a.remove();
  window.URL.revokeObjectURL(url);
};

/**
 * 判断是否能转换为map对象
 * @param obj 对象
 * @returns Boolean值
 */
export function canConvertToMap(obj: any) {
  try {
    const array = Array.from(obj) as any;
    for (const entry of array) {
      if (!Array.isArray(entry) || entry.length !== 2) {
        return false;
      }
    }
    new Map(array); // 尝试创建 Map 对象
    return true;
  } catch (e) {
    return false;
  }
}
