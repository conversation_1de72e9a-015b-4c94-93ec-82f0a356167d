<template>
  <Teleport to="body">
    <transition name="fade-slide">
      <div
        v-if="visible"
        :class="['floating-chat-window', 'visible', `ai-${type}-dialog`]"
        :style="dialogStyle"
        @mousedown.stop
      >
        <!-- 对话框头部 -->
        <div class="chat-window-header">
          <h3>
            <font-awesome-icon :icon="config.icon" style="margin-right: 8px;" />
            {{ config.title }}
          </h3>
          <div class="header-actions">
            <button 
              @click="clearHistory" 
              class="action-btn clear-btn" 
              title="清空对话" 
              v-if="messages.length > 0"
            >
              <font-awesome-icon icon="trash" />
            </button>
            <button @click="close" class="action-btn close-btn" title="收起">
              <font-awesome-icon icon="times" />
            </button>
          </div>
        </div>

        <!-- 消息体 -->
        <div class="chat-window-body" ref="chatBodyRef">
          <!-- 空状态 -->
          <div v-if="filteredMessages.length === 0" class="empty-state">
            <div class="empty-icon">
              <font-awesome-icon :icon="config.icon" />
            </div>
            <p>{{ config.title }}</p>
            <p class="empty-hint">{{ config.placeholder }}</p>
          </div>

          <!-- 消息列表 -->
          <div
            v-for="(msg, index) in filteredMessages"
            :key="index"
            :class="['message-item', `message-${msg.role}`]"
          >
            <div class="message-avatar" v-if="msg.role !== 'system'">
              <font-awesome-icon 
                v-if="msg.role === 'ai'" 
                :icon="config.icon" 
              />
              <font-awesome-icon v-else icon="user" />
            </div>
            <div class="message-content-wrapper">
              <div class="message-content" v-html="msg.content.replace(/\n/g, '<br>')"></div>
              
              <!-- 打字动画 -->
              <div v-if="msg.role === 'ai' && msg.streaming" class="ai-loading-container">
                <div class="thinking-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <span class="thinking-text">正在思考...</span>
              </div>
              
              <!-- 状态标识 -->
              <div v-if="msg.role === 'ai' && msg.status" class="message-status">
                <font-awesome-icon 
                  v-if="msg.status === 'applied'" 
                  icon="check" 
                  class="status-icon applied"
                />
                <font-awesome-icon 
                  v-if="msg.status === 'rejected'" 
                  icon="times" 
                  class="status-icon rejected"
                />
              </div>
              
              <!-- 操作按钮 -->
              <div
                v-if="showActionButtons(msg)"
                class="message-actions"
              >
                <button @click="applyResult(msg.content, index)" class="apply-btn">
                  <font-awesome-icon icon="check" />
                </button>
                <button @click="rejectResult(index)" class="reject-btn">
                  <font-awesome-icon icon="times" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-window-footer">
          <div class="input-wrapper">
            <div class="input-container">
              <!-- 输入框内容区域 -->
              <div class="input-content-area">
                <!-- 选中文本胶囊 -->
                <div v-if="selectedText" class="selected-text-tag">
                  <span class="tag-text">{{ truncateText(selectedText, 5) }}</span>
                  <button @click="clearSelectedText" class="tag-close">
                    <font-awesome-icon icon="times" />
                  </button>
                </div>

                <!-- 文本输入框 -->
                <textarea
                  v-model="currentPrompt"
                  :placeholder="config.inputPlaceholder"
                  class="prompt-input"
                  rows="2"
                  ref="promptInputRef"
                  @keydown.enter.prevent="sendMessage"
                  @input="autoResize"
                ></textarea>
              </div>

              <!-- 发送按钮 -->
              <button
                @click="sendMessage"
                :disabled="!currentPrompt.trim() || loading"
                class="send-btn"
                :title="loading ? '处理中...' : config.sendTitle"
              >
                <font-awesome-icon v-if="loading" icon="spinner" spin />
                <font-awesome-icon v-else :icon="config.sendIcon" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue';

export interface AiMessage {
  role: 'user' | 'ai' | 'system';
  content: string;
  streaming?: boolean;
  status?: 'applied' | 'rejected';
}

export interface AiChatConfig {
  title: string;
  icon: string;
  placeholder: string;
  inputPlaceholder: string;
  sendTitle: string;
  sendIcon: string;
}

interface Props {
  visible: boolean;
  type: 'qa' | 'polish' | 'draft' | 'check';
  messages: AiMessage[];
  loading: boolean;
  selectedText?: string;
  prompt?: string;
  position?: { top?: number; bottom?: number; right: number };
}

interface Emits {
  (e: 'close'): void;
  (e: 'clear'): void;
  (e: 'send', prompt: string): void;
  (e: 'apply', content: string, messageIndex: number): void;
  (e: 'reject', messageIndex: number): void;
  (e: 'update:prompt', value: string): void;
  (e: 'clear-selected'): void;
}

const props = withDefaults(defineProps<Props>(), {
  selectedText: '',
  prompt: '',
  position: () => ({ top: 80, right: 80 })
});

const emit = defineEmits<Emits>();

// 引用
const chatBodyRef = ref<HTMLDivElement>();
const promptInputRef = ref<HTMLTextAreaElement>();

// 响应式数据
const currentPrompt = ref(props.prompt);

// 计算对话框样式
const dialogStyle = computed(() => {
  if (props.position) {
    const style: any = {
      right: `${props.position.right}px`
    };

    if (props.position.bottom !== undefined) {
      style.bottom = `${props.position.bottom}px`;
    } else if (props.position.top !== undefined) {
      style.top = `${props.position.top}px`;
    }

    return style;
  }
  return {
    top: '80px',
    right: '80px'
  };
});

// 配置映射
const configs: Record<string, AiChatConfig> = {
  qa: {
    title: 'AI问答',
    icon: 'robot',
    placeholder: '提出问题，AI将基于文档内容或选中文本进行回答',
    inputPlaceholder: '请输入问题...',
    sendTitle: '提问',
    sendIcon: 'paper-plane'
  },
  polish: {
    title: '文本润色',
    icon: 'magic-wand-sparkles',
    placeholder: '选择文本后，描述润色要求，AI将优化您的文本',
    inputPlaceholder: '请输入润色要求...',
    sendTitle: '开始润色',
    sendIcon: 'magic-wand-sparkles'
  },
  draft: {
    title: '文章起稿',
    icon: 'pen-to-square',
    placeholder: '基于现有内容或想法，扩展成完整文章',
    inputPlaceholder: '请输入起稿要求...',
    sendTitle: '开始起稿',
    sendIcon: 'pen-to-square'
  },
  check: {
    title: '错误检查',
    icon: 'magnifying-glass',
    placeholder: '检查文本中的语法、拼写、逻辑等错误',
    inputPlaceholder: '请输入检查要求...',
    sendTitle: '开始检查',
    sendIcon: 'search'
  }
};

// 计算属性
const config = computed(() => configs[props.type] || configs.qa);

// 过滤掉系统消息，只显示用户和AI消息
const filteredMessages = computed(() => {
  return props.messages.filter(msg => msg.role !== 'system');
});

// 方法
const close = () => {
  emit('close');
};

const clearHistory = () => {
  emit('clear');
};

const sendMessage = () => {
  if (!currentPrompt.value.trim() || props.loading) return;
  emit('send', currentPrompt.value.trim());
  currentPrompt.value = '';
};

const applyResult = (content: string, messageIndex: number) => {
  emit('apply', content, messageIndex);

  // 滚动到底部
  nextTick(() => {
    if (chatBodyRef.value) {
      chatBodyRef.value.scrollTop = chatBodyRef.value.scrollHeight;
    }
  });
};

const rejectResult = (messageIndex: number) => {
  emit('reject', messageIndex);

  // 滚动到底部
  nextTick(() => {
    if (chatBodyRef.value) {
      chatBodyRef.value.scrollTop = chatBodyRef.value.scrollHeight;
    }
  });
};

const showActionButtons = (msg: AiMessage): boolean => {
  // 只有AI消息、非流式传输、没有状态、且是润色或起稿类型才显示按钮
  const shouldShow = msg.role === 'ai' &&
         !msg.streaming &&
         !msg.status &&
         msg.content.trim() !== '' &&
         (props.type === 'polish' || props.type === 'draft');

  // 添加调试信息
  console.log('showActionButtons 检查:', {
    role: msg.role,
    streaming: msg.streaming,
    status: msg.status,
    hasContent: msg.content.trim() !== '',
    type: props.type,
    shouldShow
  });

  return shouldShow;
};

const autoResize = (event: Event) => {
  const textarea = event.target as HTMLTextAreaElement;
  textarea.style.height = 'auto';
  textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
};

// 截断文本显示
const truncateText = (text: string, maxLength: number): string => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// 清除选中文本
const clearSelectedText = () => {
  emit('clear-selected');
};

// 监听器
watch(() => props.prompt, (newPrompt) => {
  currentPrompt.value = newPrompt;
});

watch(currentPrompt, (newValue) => {
  emit('update:prompt', newValue);
});

watch(() => props.messages.length, async () => {
  await nextTick();
  if (chatBodyRef.value) {
    chatBodyRef.value.scrollTop = chatBodyRef.value.scrollHeight;
  }
});

watch(() => props.visible, async (newVisible) => {
  if (newVisible) {
    await nextTick();
    setTimeout(() => {
      promptInputRef.value?.focus();
    }, 200);
  }
});

// 监听选中文本变化，提供视觉反馈
watch(() => props.selectedText, (newSelectedText, oldSelectedText) => {
  if (newSelectedText !== oldSelectedText && props.visible) {
    console.log('选中文本已更新:', newSelectedText);

    // 触发标签更新动画
    nextTick(() => {
      const tag = document.querySelector('.selected-text-tag') as HTMLElement;
      if (tag && newSelectedText && oldSelectedText) {
        // 重新触发出现动画
        tag.style.animation = 'none';
        void tag.offsetHeight; // 强制重排
        tag.style.animation = 'tag-appear 0.3s ease-out';
      }
    });
  }
});
</script>

<style scoped lang="scss">
.floating-chat-window {
  position: fixed;
  // top 和 right 由动态样式控制
  width: 380px;
  height: calc(100vh - 120px);
  max-height: 600px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  transform: translateX(calc(100% + 20px));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 150;
  overflow: hidden;
  pointer-events: auto;

  &.visible {
    transform: translateX(0);
  }
}

// 动画
.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.fade-slide-enter-from, .fade-slide-leave-to {
  opacity: 0;
  transform: translateX(40px);
}

.chat-window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  flex-shrink: 0;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
  }

  .header-actions {
    display: flex;
    gap: 4px;
  }

  .action-btn {
    background: none;
    border: none;
    font-size: 16px;
    color: #64748b;
    cursor: pointer;
    line-height: 1;
    padding: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      background: rgba(59, 130, 246, 0.1);
      color: #3b82f6;
    }

    &.clear-btn {
      color: #ef4444;
      &:hover {
        background: rgba(239, 68, 68, 0.1);
        color: #dc2626;
      }
      
      svg {
        font-size: 14px;
      }
    }

    &.close-btn {
      color: #64748b;
      &:hover {
        background: rgba(100, 116, 139, 0.1);
        color: #475569;
      }
      
      svg {
        font-size: 16px;
      }
    }
  }
}

.chat-window-body {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: #ffffff;
  position: relative;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    
    &:hover {
      background: #94a3b8;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 300px;
    text-align: center;
    padding: 40px 20px;

    .empty-icon {
      font-size: 64px;
      margin-bottom: 24px;
      opacity: 0.8;
      color: #6b7280;
      
      svg {
        font-size: 64px;
      }
    }

    p {
      margin: 8px 0;
      color: #374151;
      font-size: 16px;
      font-weight: 500;
      max-width: 280px;
      line-height: 1.5;

      &.empty-hint {
        font-size: 14px;
        opacity: 0.8;
        color: #6b7280;
        font-weight: 400;
        margin-top: 12px;
      }
    }
  }


  
  .message-item {
    display: flex;
    margin-bottom: 16px;
    max-width: 90%;
    animation: fadeInUp 0.3s ease;

    .message-avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #f1f5f9;
      color: #475569;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-top: 4px;
      border: 1px solid #e2e8f0;
    }
    
    .message-content-wrapper {
      margin-left: 8px;
      display: flex;
      align-items: flex-start;
      flex-direction: column;
    }

    .message-content {
      padding: 10px 14px;
      border-radius: 12px;
      background: #f8fafc;
      color: #334155;
      font-size: 13px;
      line-height: 1.5;
      word-break: break-word;
      max-width: 100%;
      border: 1px solid #e2e8f0;
    }

    &.message-user {
      margin-left: auto;
      flex-direction: row-reverse;

      .message-content-wrapper {
        margin-left: 0;
        margin-right: 8px;
        align-items: flex-end;
      }

      .message-content {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border-color: #3b82f6;
      }

      .message-avatar {
        background: #dbeafe;
        border-color: #93c5fd;
      }
    }

    &.message-system {
      max-width: 100%;
      
      .message-content {
        width: 100%;
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        color: #92400e;
        font-size: 12px;
        text-align: center;
        border-radius: 8px;
        padding: 8px 12px;
        border-color: #f3d55e;
      }
    }
    
    // AI加载状态样式 - 与青年交友保持一致
    .ai-loading-container {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;

      .thinking-dots {
        display: flex;
        gap: 4px;

        span {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: var(--theme-color);
          opacity: 0.7;
          animation: simple-bounce 1.4s infinite ease-in-out both;

          &:nth-child(1) {
            animation-delay: -0.32s;
          }

          &:nth-child(2) {
            animation-delay: -0.16s;
          }

          &:nth-child(3) {
            animation-delay: 0s;
          }
        }
      }

      .thinking-text {
        font-size: 13px;
        color: #909399;
      }
    }

    .message-actions {
      margin-top: 12px;
      display: flex;
      gap: 8px;

      .apply-btn, .reject-btn {
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &:active {
          transform: scale(0.95);
        }
      }

      .apply-btn {
        background: #10b981;
        color: white;

        &:hover {
          background: #059669;
        }
      }

      .reject-btn {
        background: #ef4444;
        color: white;

        &:hover {
          background: #dc2626;
        }
      }
    }
    
    .message-status {
      margin-top: 8px;
      display: flex;
      align-items: center;
      gap: 6px;

      .status-icon {
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;

        &.applied {
          background: rgba(16, 185, 129, 0.1);
          color: #10b981;

          &::after {
            content: "已应用";
          }
        }

        &.rejected {
          background: rgba(239, 68, 68, 0.1);
          color: #ef4444;

          &::after {
            content: "已拒绝";
          }
        }
      }
    }
  }

  @keyframes fadeInUp {
    from { 
      opacity: 0; 
      transform: translateY(20px); 
    }
    to { 
      opacity: 1; 
      transform: translateY(0); 
    }
  }

  // 统一的动画定义 - 与青年交友保持一致
  @keyframes simple-bounce {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }


}

.chat-window-footer {
  padding: 0;
  border-top: none;
  background: transparent;
  flex-shrink: 0;

  .input-wrapper {
    padding: 0;
    margin: 0;
  }

  .input-container {
    position: relative;
    background: white;
    border-top: 1px solid #e5e7eb;
    display: flex;
    align-items: flex-end;
    gap: 8px;
    padding: 12px;
  }

  .input-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-height: 48px;
  }

  .selected-text-tag {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 1px solid #f3d55e;
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 12px;
    color: #92400e;
    max-width: 200px;
    align-self: flex-start;
    animation: tag-appear 0.3s ease-out;

    .tag-text {
      font-weight: 400;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
      min-width: 0;
      margin-right: 4px;
    }

    .tag-close {
      background: none;
      border: none;
      color: #b45309;
      cursor: pointer;
      padding: 0;
      width: 14px;
      height: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 2px;
      transition: all 0.2s;
      flex-shrink: 0;

      &:hover {
        background: rgba(180, 83, 9, 0.1);
        color: #92400e;
      }

      svg {
        font-size: 10px;
      }
    }
  }

  .prompt-input {
    width: 100%;
    border: none;
    border-radius: 0;
    padding: 0;
    font-size: 14px;
    color: #1e293b;
    resize: none;
    min-height: 32px;
    max-height: 120px;
    line-height: 1.5;
    outline: none;
    transition: all 0.2s;
    font-family: inherit;
    background: transparent;
    box-shadow: none;

    &:focus {
      box-shadow: none;
      border: none;
    }

    &::placeholder {
      color: #94a3b8;
    }
  }

  .send-btn {
    width: 32px;
    height: 32px;
    padding: 0;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
    flex-shrink: 0;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
      box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
    }
  }
}

// 标签动画
@keyframes tag-appear {
  0% {
    opacity: 0;
    transform: translateY(-5px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .floating-chat-window {
    top: 70px;
    right: 16px;
    left: 16px;
    width: auto;
    height: calc(100vh - 110px);
  }
}

// 为不同类型的对话框添加特定样式
.ai-qa-dialog {
  .chat-window-header h3 {
    color: #3b82f6;
  }

  .chat-window-body .empty-state .empty-icon {
    color: #3b82f6 !important;
  }

  .selected-text-tag {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-color: #93c5fd;
    color: #1e40af;

    .tag-close {
      color: #2563eb;

      &:hover {
        background: rgba(37, 99, 235, 0.1);
        color: #1e40af;
      }
    }
  }
}

.ai-polish-dialog {
  .chat-window-header h3 {
    color: #8b5cf6;
  }

  .chat-window-body .empty-state .empty-icon {
    color: #8b5cf6 !important;
  }

  .message-actions .apply-btn {
    background: #8b5cf6;

    &:hover {
      background: #7c3aed;
      box-shadow: 0 4px 8px rgba(139, 92, 246, 0.3);
    }
  }

  .selected-text-tag {
    background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
    border-color: #c4b5fd;
    color: #6d28d9;

    .tag-close {
      color: #8b5cf6;

      &:hover {
        background: rgba(139, 92, 246, 0.1);
        color: #6d28d9;
      }
    }
  }
}

.ai-draft-dialog {
  .chat-window-header h3 {
    color: #10b981;
  }

  .chat-window-body .empty-state .empty-icon {
    color: #10b981 !important;
  }

  .selected-text-tag {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border-color: #6ee7b7;
    color: #047857;

    .tag-close {
      color: #10b981;

      &:hover {
        background: rgba(16, 185, 129, 0.1);
        color: #047857;
      }
    }
  }
}

.ai-check-dialog {
  .chat-window-header h3 {
    color: #f59e0b;
  }

  .chat-window-body .empty-state .empty-icon {
    color: #f59e0b !important;
  }

  .selected-text-tag {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-color: #f3d55e;
    color: #92400e;

    .tag-close {
      color: #d97706;

      &:hover {
        background: rgba(217, 119, 6, 0.1);
        color: #92400e;
      }
    }
  }
}
</style> 