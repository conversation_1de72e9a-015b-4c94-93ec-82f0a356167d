<template>
  <div class="note-editor-wrapper">
    <div class="note-editor-container">
      <!-- 头部区域 -->
      <div class="editor-header">
        <div class="header-content">
          <div class="header-left">
            <div class="app-icon">
              <Icon :icon="fountainPenIcon" width="50" height="50" class="icon-float" />
            </div>
            <h3 class="title">闪记</h3>
          </div>
          <div class="header-actions">
            <button @click="minimizeWindow" class="control-btn minimize-btn" title="最小化">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </button>
            <button @click="closeWindow" class="control-btn close-btn" title="关闭">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="editor-content">
        <div class="textarea-container">
          <textarea
            v-model="content"
            placeholder="输入内容..."
            class="note-textarea"
            ref="textareaRef"
            @keydown.ctrl.enter="saveNote"
            @input="handleInput"
            maxlength="500"
          ></textarea>
        </div>
      </div>

      <!-- 底部区域 -->
      <div class="editor-footer">
        <div class="footer-left">
          <div class="char-count" :class="{ 'char-warning': content.length > 400, 'char-danger': content.length >= 500 }">
            <span class="count-number">{{ content.length }}</span>
            <span class="count-separator">/</span>
            <span class="count-total">500</span>
          </div>
        </div>
        <div class="button-group">
          <button @click="generateWithAI" class="ai-btn" :disabled="!content.trim()" :class="{ 'ai-btn-active': content.trim() }">
            <span>AI生成</span>
          </button>
          <button @click="saveToLocal" class="local-btn" :disabled="!content.trim()" :class="{ 'local-btn-active': content.trim() }">
            <span>保存到本地</span>
          </button>
          <button @click="saveNote" class="save-btn" :disabled="!content.trim()" :class="{ 'save-btn-active': content.trim() }">
            <span>保存 (Ctrl+Enter)</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, onUnmounted } from "vue";
import { ipc } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { Icon } from '@iconify/vue';
import fountainPenIcon from '@iconify-icons/fluent-emoji/fountain-pen';

const content = ref("");
const textareaRef = ref<HTMLTextAreaElement>();
const originalContent = ref(""); // 记录原始内容，用于判断是否有修改
const isGenerating = ref(false); // AI生成状态

// 当前笔记对象，用于缓存ID和其他信息
const currentNote = ref<{
  id?: number;
  title?: string;
  content: string;
  isNew: boolean; // 标记是否为新笔记
}>({
  content: "",
  isNew: true
});

// 处理输入
const handleInput = () => {
  // 同步内容到当前笔记对象
  currentNote.value.content = content.value;
  // 可以在这里添加实时保存逻辑
};

// 重置笔记状态（用于创建新笔记）
const resetNoteState = () => {
  currentNote.value = {
    content: "",
    isNew: true
  };
  content.value = "";
  originalContent.value = "";
};

// 检查是否有未保存的修改
const hasUnsavedChanges = () => {
  return content.value.trim() !== originalContent.value.trim();
};

// AI生成内容
const generateWithAI = async () => {
  if (!content.value.trim() || isGenerating.value) return;

  const loadingInstance = ElLoading.service({
    text: "本地AI处理中...",
    background: "rgba(0, 0, 0, 0.7)"
  });

  try {
    isGenerating.value = true;

    // 使用简单的本地处理逻辑
    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟处理时间
    
    // 简单的文本优化：添加分段和标点
    const processed = content.value.trim()
      .replace(/(.{30,50}[，。！？])/g, '$1\n')
      .replace(/([。！？])\s*([^\n])/g, '$1\n$2')
      .trim();
    
    content.value = processed;
    
    // 同步到当前笔记对象
    currentNote.value.content = content.value;

    ElMessage.success("文本优化完成");
    
  } catch (error) {
    console.error("本地处理失败:", error);
    ElMessage.error("处理失败，请重试");
  } finally {
    isGenerating.value = false;
    loadingInstance.close();
  }
};

// 保存到本地SQLite数据库
const saveToLocal = async () => {
  if (!content.value.trim()) return;

  try {
    // 生成标题
    const title = currentNote.value.title || content.value.trim().substring(0, 20) + (content.value.trim().length > 20 ? "..." : "");
    
    // 构造纯数据对象，避免循环引用
    const noteData = {
      content: content.value.trim(),
      title: title,
      id: currentNote.value.id
    };
    
    // 使用IPC保存到本地SQLite数据库
    const result = await ipc.invoke(ipcApiRoute.saveNote, noteData);
    
    console.log("本地保存结果:", result);

    // 更新当前笔记信息
    if (result && result.id) {
      currentNote.value.id = result.id;
      currentNote.value.isNew = false;

      // 使用ipc.send广播更新事件
      ipc.send('note-updated', result);
    }

    // 更新本地缓存
    currentNote.value.content = content.value.trim();
    currentNote.value.title = title;

    // 更新原始内容，标记为已保存
    originalContent.value = content.value.trim();

    ElMessage.success("笔记已保存到本地数据库");

  } catch (error) {
    console.error("本地保存失败:", error);
    ElMessage.error("本地保存失败，请重试");
  }
};

// 保存笔记
const saveNote = async () => {
  if (!content.value.trim()) return;

  try {
    // 生成标题
    const title = currentNote.value.title || content.value.trim().substring(0, 20) + (content.value.trim().length > 20 ? "..." : "");
    
    // 只保存到本地数据库
    const noteData = {
      content: content.value.trim(),
      title: title,
      id: currentNote.value.id
    };
    
    const result = await ipc.invoke(ipcApiRoute.saveNote, noteData);
    
    if (result && result.id) {
      // 更新当前笔记
      currentNote.value.id = result.id;
      currentNote.value.isNew = false;
      currentNote.value.content = content.value.trim();
      currentNote.value.title = title;

      // 更新原始内容，标记为已保存
      originalContent.value = content.value.trim();

      ElMessage.success("笔记已保存到本地");
    } else {
      throw new Error("本地保存失败");
    }

  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

// 最小化窗口
const minimizeWindow = () => {
  ipc.invoke(ipcApiRoute.minimizeQuickNoteWindow);
};

// 关闭窗口
const closeWindow = async () => {
  // 检查是否有未保存的修改
  if (hasUnsavedChanges()) {
    try {
      await ElMessageBox.confirm("您有未保存的内容，是否保存后关闭？", "确认关闭", {
        confirmButtonText: "保存并关闭",
        cancelButtonText: "直接关闭",
        distinguishCancelAndClose: true,
        type: "warning",
        customClass: "note-close-dialog"
      });

      // 用户选择保存并关闭
      if (content.value.trim()) {
        await saveNote();
        // 保存成功后关闭窗口
        ipc.invoke(ipcApiRoute.closeQuickNoteWindow);
        return;
      }
    } catch (action) {
      if (action === "cancel") {
        // 用户选择直接关闭，不保存
        ipc.invoke(ipcApiRoute.closeQuickNoteWindow);
        return;
      } else if (action === "close") {
        // 用户点击了对话框的关闭按钮，取消关闭操作
        return;
      }
    }
  }

  // 没有未保存的修改，直接关闭
  ipc.invoke(ipcApiRoute.closeQuickNoteWindow);
};

// 监听IPC消息，接收笔记数据
onMounted(async () => {
  console.log('[Editor] 组件开始挂载');
  await nextTick();
  textareaRef.value?.focus();

  // 初始化为新笔记状态
  currentNote.value = {
    content: "",
    isNew: true
  };
  content.value = "";
  originalContent.value = "";
  console.log('[Editor] 初始化完成，等待数据...');

  // 监听来自主进程的笔记数据
  ipc.on('edit-note-data', (event: any, noteData: any) => {
    console.log('[Editor] 收到编辑笔记数据:', noteData);
    if (noteData) {
      // 更新当前笔记对象
      currentNote.value = {
        id: noteData.id,
        title: noteData.title,
        content: noteData.content,
        isNew: false
      };
      console.log('[Editor] 更新currentNote:', currentNote.value);
      
      // 更新编辑器内容
      content.value = noteData.content;
      console.log('[Editor] 更新content:', content.value);
      
      // 更新原始内容，用于检测变更
      originalContent.value = noteData.content;
      console.log('[Editor] 更新完成');
    } else {
      console.warn('[Editor] 收到的数据为空');
    }
  });

  console.log('[Editor] 事件监听器已设置');
});

// 在组件卸载时移除事件监听
onUnmounted(() => {
  console.log('[Editor] 组件准备卸载，移除事件监听器');
  ipc.removeAllListeners('edit-note-data');
});
</script>

<style scoped>
/* 基础容器样式 */
.note-editor-wrapper {
  width: 100vw;
  height: 100vh;
  background: transparent;
  padding: 8px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
}

.note-editor-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 16px;
  overflow: hidden;
  position: relative;

  /* 亮色主题 */
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  color: #1f2937;
}

/* 暗色主题 */
:global(.dark) .note-editor-container {
  background: #111827;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #f9fafb;
}

/* 头部样式 */
.editor-header {
  position: relative;
  background: #f8fafc;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  -webkit-app-region: drag;
  user-select: none;
}

:global(.dark) .editor-header {
  background: #1f2937;
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:global(.dark) .app-icon {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.4);
}

.title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: -0.025em;
  pointer-events: none;
}

.header-actions {
  display: flex;
  gap: 8px;
  -webkit-app-region: no-drag;
}

/* 控制按钮样式 */
.control-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.6);
}

.control-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.8);
  transform: translateY(-1px);
}

.control-btn:active {
  transform: translateY(0);
  transition-duration: 0.1s;
}

.close-btn:hover {
  background: #ef4444;
  color: white;
}

:global(.dark) .control-btn {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

:global(.dark) .control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

:global(.dark) .close-btn:hover {
  background: #dc2626;
  color: white;
}

/* 内容区域样式 */
.editor-content {
  flex: 1;
  padding: 12px;
  display: flex;
}

.textarea-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.note-textarea {
  width: 100%;
  height: 100%;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  font-size: 18px;
  line-height: 1.6;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 亮色主题 */
  background: #ffffff;
  color: #374151;
  border: none;
}

.note-textarea::placeholder {
  color: rgba(107, 114, 128, 0.7);
  font-style: italic;
}

.note-textarea:focus {
  background: #ffffff;
}

/* 暗色主题文本域 */
:global(.dark) .note-textarea {
  background: #1f2937;
  color: #e5e7eb;
  border: none;
}

:global(.dark) .note-textarea::placeholder {
  color: rgba(156, 163, 175, 0.7);
}

:global(.dark) .note-textarea:focus {
  background: #1f2937;
}

/* 底部区域样式 */
.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #f8fafc;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

:global(.dark) .editor-footer {
  background: #1f2937;
  border-top: 1px solid rgba(75, 85, 99, 0.2);
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.char-count {
  display: flex;
  align-items: center;
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
  font-size: 13px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 6px;
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  transition: all 0.2s ease;
}

.char-count.char-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

.char-count.char-danger {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

:global(.dark) .char-count {
  background: rgba(156, 163, 175, 0.1);
  color: #9ca3af;
}

:global(.dark) .char-count.char-warning {
  background: rgba(245, 158, 11, 0.15);
  color: #fbbf24;
}

:global(.dark) .char-count.char-danger {
  background: rgba(239, 68, 68, 0.15);
  color: #f87171;
}

.count-number {
  font-weight: 600;
}

.count-separator {
  margin: 0 2px;
  opacity: 0.5;
}

.count-total {
  opacity: 0.7;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 通用按钮基础样式 */
.save-btn,
.ai-btn,
.local-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;

  /* 默认禁用状态 */
  background: rgba(107, 114, 128, 0.1);
  color: rgba(107, 114, 128, 0.5);
  cursor: not-allowed;
}

/* AI按钮激活状态 */
.ai-btn.ai-btn-active {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

/* 本地保存按钮激活状态 */
.local-btn.local-btn-active {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

/* 保存按钮激活状态 */
.save-btn.save-btn-active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

/* 暗色主题按钮 */
:global(.dark) .save-btn,
:global(.dark) .ai-btn,
:global(.dark) .local-btn {
  background: rgba(156, 163, 175, 0.1);
  color: rgba(156, 163, 175, 0.5);
}

:global(.dark) .ai-btn.ai-btn-active {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.4);
}

:global(.dark) .local-btn.local-btn-active {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  box-shadow: 0 2px 8px rgba(217, 119, 6, 0.4);
}

:global(.dark) .save-btn.save-btn-active {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow: 0 2px 8px rgba(5, 150, 105, 0.4);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .note-editor-wrapper {
    padding: 4px;
  }

  .header-content {
    padding: 12px 16px;
  }

  .editor-content {
    padding: 16px;
  }

  .editor-footer {
    padding: 12px 16px;
  }

  .footer-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .button-group {
    flex-direction: column;
    width: 100%;
    gap: 8px;
  }

  .save-btn,
  .ai-btn,
  .local-btn {
    width: 100%;
    padding: 12px 16px;
  }
}

/* 滚动条样式 */
.note-textarea::-webkit-scrollbar {
  width: 6px;
}

.note-textarea::-webkit-scrollbar-track {
  background: transparent;
}

.note-textarea::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
}

.note-textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

:global(.dark) .note-textarea::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

:global(.dark) .note-textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.7);
}

/* 自定义确认对话框样式 */
:global(.note-close-dialog) {
  border-radius: 12px !important;
  --el-messagebox-border-radius: 12px;
}

:global(.note-close-dialog .el-message-box__header) {
  padding: 20px 20px 10px 20px !important;
}

:global(.note-close-dialog .el-message-box__title) {
  font-size: 16px !important;
  font-weight: 600 !important;
}

:global(.note-close-dialog .el-message-box__content) {
  padding: 10px 20px 20px 20px !important;
}

:global(.note-close-dialog .el-message-box__btns) {
  padding: 10px 20px 20px 20px !important;
  gap: 12px !important;
}

:global(.note-close-dialog .el-button) {
  border-radius: 8px !important;
  font-weight: 500 !important;
}
</style>
