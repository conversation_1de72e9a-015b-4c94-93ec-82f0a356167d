/**
 * Windows 7 兼容性检测工具
 * 检测系统是否支持透明效果和现代CSS特性
 */

export interface Windows7CompatibilityInfo {
  supportsTransparency: boolean;
  supportsBackdropFilter: boolean;
  isWindows7Basic: boolean;
  recommendedBackground: string;
  recommendedBorderColor: string;
}

/**
 * 检测浏览器是否支持透明效果
 */
export function supportsTransparency(): boolean {
  // 检测backdrop-filter支持
  const supportsBackdropFilter = 
    'backdropFilter' in document.documentElement.style ||
    'webkitBackdropFilter' in document.documentElement.style;
  
  // 检测CSS透明度支持
  const supportsOpacity = 'opacity' in document.documentElement.style;
  
  // 检测RGBA支持
  const supportsRGBA = (() => {
    try {
      const testElement = document.createElement('div');
      testElement.style.backgroundColor = 'rgba(0,0,0,0.5)';
      return testElement.style.backgroundColor.includes('rgba');
    } catch {
      return false;
    }
  })();
  
  return supportsBackdropFilter && supportsOpacity && supportsRGBA;
}

/**
 * 检测是否为Windows 7 Basic模式
 */
export function isWindows7Basic(): boolean {
  const userAgent = navigator.userAgent;
  
  // 检测Windows 7
  const isWindows7 = userAgent.includes('Windows NT 6.1');
  
  // 检测是否支持现代CSS特性
  const supportsModernCSS = supportsTransparency();
  
  // Windows 7 + 不支持现代CSS = Basic模式
  return isWindows7 && !supportsModernCSS;
}

/**
 * 获取兼容性信息
 */
export function getCompatibilityInfo(): Windows7CompatibilityInfo {
  const supportsTransparencyFlag = supportsTransparency();
  const isBasicMode = isWindows7Basic();
  
  return {
    supportsTransparency: supportsTransparencyFlag,
    supportsBackdropFilter: 'backdropFilter' in document.documentElement.style ||
                           'webkitBackdropFilter' in document.documentElement.style,
    isWindows7Basic: isBasicMode,
    recommendedBackground: isBasicMode ? '#ffffff' : 'transparent',
    recommendedBorderColor: isBasicMode ? '#d1d5db' : '#d1d5db'
  };
}

/**
 * 应用兼容性样式
 */
export function applyCompatibilityStyles(): void {
  const info = getCompatibilityInfo();
  
  if (info.isWindows7Basic) {
    // 为Windows 7 Basic模式添加特殊类名
    document.body.classList.add('windows7-basic');
    
    // 动态添加兼容性样式
    const style = document.createElement('style');
    style.textContent = `
      /* Windows 7 Basic模式：优雅的圆角和边框设计 */
      .windows7-basic #app {
        background: #f8f9fa !important;
        border-radius: 8px !important;
        -webkit-border-radius: 8px !important;
        -moz-border-radius: 8px !important;
        padding: 4px !important;
        border: 2px solid #d1d5db;
        box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.15),
          0 0 0 1px rgba(255, 255, 255, 0.5);
        -webkit-box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.15),
          0 0 0 1px rgba(255, 255, 255, 0.5);
        -moz-box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.15),
          0 0 0 1px rgba(255, 255, 255, 0.5);
      }

      .windows7-basic .app-container {
        background: #ffffff !important;
        border-radius: 6px !important;
        -webkit-border-radius: 6px !important;
        -moz-border-radius: 6px !important;
        border: 1px solid #e5e7eb;
        box-shadow:
          0 1px 3px rgba(0, 0, 0, 0.12),
          0 1px 2px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
        -webkit-box-shadow:
          0 1px 3px rgba(0, 0, 0, 0.12),
          0 1px 2px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
        -moz-box-shadow:
          0 1px 3px rgba(0, 0, 0, 0.12),
          0 1px 2px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
      }

      /* Windows 7 Basic暗色主题 */
      .windows7-basic.dark #app {
        background: #1f2937 !important;
        border-color: #4b5563;
        box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.25),
          0 0 0 1px rgba(75, 85, 99, 0.5);
        -webkit-box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.25),
          0 0 0 1px rgba(75, 85, 99, 0.5);
        -moz-box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.25),
          0 0 0 1px rgba(75, 85, 99, 0.5);
      }

      .windows7-basic.dark .app-container {
        background: #1f2937 !important;
        border-color: #374151 !important;
        box-shadow:
          0 1px 3px rgba(0, 0, 0, 0.2),
          0 1px 2px rgba(0, 0, 0, 0.15),
          inset 0 1px 0 rgba(55, 65, 81, 0.8) !important;
        -webkit-box-shadow:
          0 1px 3px rgba(0, 0, 0, 0.2),
          0 1px 2px rgba(0, 0, 0, 0.15),
          inset 0 1px 0 rgba(55, 65, 81, 0.8) !important;
        -moz-box-shadow:
          0 1px 3px rgba(0, 0, 0, 0.2),
          0 1px 2px rgba(0, 0, 0, 0.15),
          inset 0 1px 0 rgba(55, 65, 81, 0.8) !important;
      }

      /* Header在Basic模式下的优雅调整 */
      .windows7-basic .el-header {
        border-radius: 6px 6px 0 0 !important;
        -webkit-border-radius: 6px 6px 0 0 !important;
        -moz-border-radius: 6px 6px 0 0 !important;
        left: 6px !important;  /* 4px app padding + 2px app border = 6px */
        right: 6px !important;
        border-bottom: 1px solid #e5e7eb !important;
        background: rgba(255, 255, 255, 0.95) !important;
      }

      .windows7-basic.dark .el-header {
        border-bottom-color: #374151 !important;
        background: rgba(31, 41, 59, 0.95) !important;
      }

      /* 禁用可能导致问题的CSS特性，但保留圆角 */
      .windows7-basic * {
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
      }

      /* 确保UI组件保持优雅的圆角 */
      .windows7-basic .el-button {
        border-radius: 6px !important;
        -webkit-border-radius: 6px !important;
        -moz-border-radius: 6px !important;
      }

      .windows7-basic .el-input {
        border-radius: 4px !important;
        -webkit-border-radius: 4px !important;
        -moz-border-radius: 4px !important;
      }

      .windows7-basic .el-card {
        border-radius: 8px !important;
        -webkit-border-radius: 8px !important;
        -moz-border-radius: 8px !important;
      }
    `;
    document.head.appendChild(style);
    
    console.log('Windows 7 Basic模式兼容性样式已应用');
  } else {
    console.log('检测到现代浏览器环境，使用透明效果');
  }
}

/**
 * 初始化兼容性检测
 */
export function initCompatibility(): void {
  // 等待DOM加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyCompatibilityStyles);
  } else {
    applyCompatibilityStyles();
  }
}
