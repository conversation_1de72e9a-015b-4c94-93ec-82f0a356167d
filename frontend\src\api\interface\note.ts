// AI智能笔记相关接口类型定义
export namespace AiNote {
  // AI笔记数据结构 - 对应后台AiNoteRespVO
  export interface AiNoteItem {
    id: number;
    title: string;
    content: string;
    summary?: string;
    noteType: string;
    category?: string;
    tags?: string[];
    status: number;
    isPublic?: boolean;
    userId: number;
    userName?: string;
    viewCount?: number;
    wordCount?: number;
    charCount?: number;
    originalContent?: string;
    aiPrompt?: string;
    aiModel?: string;
    qualityScore?: number;
    aiGeneratedAt?: string;
    processingTime?: number;
    conversationId?: string;
    config?: string;
    remark?: string;
    createTime: string;
    updateTime?: string;
  }

  // AI笔记保存请求参数 - 对应后台AiNoteSaveReqVO
  export interface AiNoteSaveRequest {
    id?: number;
    title: string;
    content: string;
    summary?: string;
    noteType: string;
    category?: string;
    tags?: string[];
    status: number;
    isPublic?: boolean;
    originalContent?: string;
    aiPrompt?: string;
    aiModel?: string;
    qualityScore?: number;
    conversationId?: string;
    config?: string;
    remark?: string;
  }

  // AI笔记分页请求参数 - 对应后台AiNotePageReqVO
  export interface AiNotePageRequest {
    pageNo?: number;
    pageSize?: number;
    title?: string;
    content?: string;
    noteType?: string;
    category?: string;
    status?: number;
    isPublic?: boolean;
    userId?: number;
    startTime?: string;
    endTime?: string;
  }

  // AI笔记生成请求参数 - 对应后台AiNoteGenerateReqVO
  export interface AiNoteGenerateRequest {
    originalContent: string;
    noteType: string;
    aiPrompt?: string;
    aiModel?: string;
    config?: string;
    title?: string;
    category?: string;
    tags?: string[];
  }

  // 分页响应结果
  export interface PageResult<T> {
    list: T[];
    total: number;
    pageNo: number;
    pageSize: number;
  }

  // 通用响应结果
  export interface CommonResult<T> {
    code: number;
    msg: string;
    data: T;
  }

  // 笔记统计信息
  export interface NoteStats {
    totalCount: number;
    todayCount: number;
    weekCount: number;
    monthCount: number;
    categoryStats: Array<{
      category: string;
      count: number;
    }>;
    typeStats: Array<{
      noteType: string;
      count: number;
    }>;
  }
}

// 笔记相关接口类型定义 (保持兼容)
export namespace Note {
  // 笔记数据结构
  export interface NoteItem {
    id: number;
    content: string;
    created_at: string;
    updated_at?: string;
    user_id?: number;
    title?: string;
  }

  // 保存笔记请求参数
  export interface SaveNoteRequest {
    content: string;
    title?: string;
  }

  // 保存笔记响应
  export interface SaveNoteResponse {
    id: number;
    content: string;
    created_at: string;
    title?: string;
  }

  // 获取笔记列表请求参数
  export interface GetNotesRequest {
    page?: number;
    size?: number;
    keyword?: string;
    start_date?: string;
    end_date?: string;
  }

  // 获取笔记列表响应
  export interface GetNotesResponse {
    list: NoteItem[];
    total: number;
    page: number;
    size: number;
  }

  // 删除笔记响应
  export interface DeleteNoteResponse {
    success: boolean;
    message?: string;
  }

  // 批量删除请求参数
  export interface BatchDeleteRequest {
    ids: number[];
  }

  // 更新笔记请求参数
  export interface UpdateNoteRequest {
    id: number;
    content?: string;
    title?: string;
  }

  // 更新笔记响应
  export interface UpdateNoteResponse {
    id: number;
    content: string;
    title?: string;
    updated_at: string;
  }
} 