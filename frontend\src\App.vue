<script setup lang="ts">
import { computed, reactive, onMounted, watch } from "vue";
import { useGlobalStore } from "./stores/modules/global";
import { useIpcEvent } from "./hooks/useIpcEvent";
import { ipc } from "@/utils/ipcRenderer";

const globalStore = useGlobalStore();

const assemblySize = computed(() => globalStore.assemblySize);
const buttonConfig = reactive({ autoInsertSpace: false });

// 监听窗口最大化状态
const isMaximized = computed(() => globalStore.maximize);

// 监听最大化状态变化，动态切换body class
watch(isMaximized, (newValue) => {
  if (newValue) {
    document.body.classList.add('window-maximized');
  } else {
    document.body.classList.remove('window-maximized');
  }
}, { immediate: true });

onMounted(() => {
  useIpcEvent();
  
  // 监听主窗口最大化状态变化
  if (ipc) {
    ipc.on('window-maximize-state-changed', (_event: any, newState: boolean) => {
      globalStore.setGlobalState('maximize', newState);
    });
    
    // 初始化时获取窗口状态
    ipc.invoke('controller.os.getMaximizeState').then((state: boolean) => {
      globalStore.setGlobalState('maximize', state);
    }).catch(() => {
      // 如果获取失败，使用默认值
      globalStore.setGlobalState('maximize', false);
    });
  }
});
</script>

<template>
  <el-config-provider :size="assemblySize" :button="buttonConfig">
    <router-view></router-view>
  </el-config-provider>
</template>

<style>
img, a{
  -webkit-user-drag: none;
  -o-user-drag: none;
  -moz-user-drag: none;
}
/* 确保没有默认边距和填充 */
html, body {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

body #app {
  margin: 0;
  padding: 0px;
  width: 100vw;
  height: 100vh;
  /* Windows 7 Basic模式兼容性：提供fallback背景 */
  background: #f8f9fa;
  border-radius: 9px;
  box-sizing: border-box;
}

/* 支持透明度的环境使用透明背景 */
@supports (backdrop-filter: blur(1px)) or (-webkit-backdrop-filter: blur(1px)) {
  body #app {
    background: transparent !important;
  }
}

/* 暗色主题的fallback背景 */
.dark body #app {
  background: #1f2937;
}

@supports (backdrop-filter: blur(1px)) or (-webkit-backdrop-filter: blur(1px)) {
  .dark body #app {
    background: transparent !important;
  }
}

body .app-container {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  /* Windows 7 Basic模式兼容性：提供fallback背景 */
  background: #ffffff;
  border-radius: 0;
  border: none;
  box-sizing: border-box;
  /* 移除阴影效果 */
}

/* 支持透明度的环境使用透明背景 */
@supports (backdrop-filter: blur(1px)) or (-webkit-backdrop-filter: blur(1px)) {
  body .app-container {
    background: transparent !important;
  }
}

/* 暗色主题的fallback背景 */
.dark body .app-container {
  background: #1f2937;
  border: none;
  /* 移除暗色主题下的阴影效果 */
}

@supports (backdrop-filter: blur(1px)) or (-webkit-backdrop-filter: blur(1px)) {
  .dark body .app-container {
    background: transparent !important;
  }
}

/* 窗口最大化状态样式 - 提高优先级 */
body.window-maximized #app {
  padding: 0 !important;
}

body.window-maximized .app-container {
  border-radius: 0 !important;
  border: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
}

/* Windows 7 Basic模式静态样式（备用方案） - 无边距设计 */
@media screen and (-ms-high-contrast: none) {
  /* 检测IE/Edge，可能是Windows 7 */
  body #app {
    background: #f8f9fa !important;
    border-radius: 0 !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    padding: 0 !important;
    border: none;
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.5);
  }

  body .app-container {
    background: #ffffff !important;
    border-radius: 0 !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    border: none;
    box-shadow: none !important;
  }

  .dark body #app {
    background: #1f2937 !important;
    border: none;
    box-shadow: none;
  }

  .dark body .app-container {
    background: #1f2937 !important;
    border: none !important;
    box-shadow: none !important;
  }
}

.app-container .el-overlay,
.el-overlay {
  border-radius: 0;
}

/* Element Plus 弹窗样式 */
.el-popover.el-popper {
  min-width: 20px !important;
}

/* 修改整个应用的滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  border-radius: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* 暗色主题滚动条 */
.dark ::-webkit-scrollbar-track {
  background: #2a2a2a;
}

.dark ::-webkit-scrollbar-thumb {
  background: #555;
}

/* 暗色主题边框和阴影 */
.dark .app-container {
  border-color: rgba(75, 85, 99, 0.6);
  /* 暗色主题下增强的阴影效果，适配Windows 7 */
  box-shadow: var(--app-shadow,
    0 0 2px rgba(0, 0, 0, 0.20),
    0 0 6px rgba(0, 0, 0, 0.16),
    0 0 12px rgba(0, 0, 0, 0.12),
    0 0 24px rgba(0, 0, 0, 0.08),
    0 0 48px rgba(0, 0, 0, 0.04)
  );
  /* 添加浏览器前缀以确保Windows 7兼容性 */
  -webkit-box-shadow: var(--app-shadow,
    0 0 2px rgba(0, 0, 0, 0.20),
    0 0 6px rgba(0, 0, 0, 0.16),
    0 0 12px rgba(0, 0, 0, 0.12),
    0 0 24px rgba(0, 0, 0, 0.08),
    0 0 48px rgba(0, 0, 0, 0.04)
  );
  -moz-box-shadow: var(--app-shadow,
    0 0 2px rgba(0, 0, 0, 0.20),
    0 0 6px rgba(0, 0, 0, 0.16),
    0 0 12px rgba(0, 0, 0, 0.12),
    0 0 24px rgba(0, 0, 0, 0.08),
    0 0 48px rgba(0, 0, 0, 0.04)
  );
}

/* 最大化状态下暗色主题也要移除阴影 */
body.dark.window-maximized .app-container {
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  border: none !important;
}
</style>
