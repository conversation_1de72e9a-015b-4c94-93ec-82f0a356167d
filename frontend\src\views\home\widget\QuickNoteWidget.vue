<template>
  <div class="widget-container h-full relative">
    <!-- 悬浮标题和按钮容器 -->
    <div class="absolute top-0 left-4 -translate-y-1/2 flex items-center gap-2 z-10">
      <!-- 小组件标题 -->
      <div
        class="widget-title bg-white dark:bg-gray-800 px-3 py-1.5 rounded-full shadow-md border border-gray-200 dark:border-gray-700 flex items-center gap-2"
      >
        <Icon :icon="penNewSquareBoldDuotone" class="text-lg text-yellow-500" />
        <span class="text-xs font-bold text-gray-700 dark:text-gray-200">闪记</span>
      </div>
      <!-- 查看全部笔记按钮 -->
      <button
        @click="goToNoteManager"
        class="w-7 h-7 rounded-full bg-yellow-500 text-white flex items-center justify-center hover:bg-yellow-600 transition-colors shadow-md"
        title="查看全部笔记"
      >
        <Icon :icon="notebookBoldDuotone" />
      </button>
    </div>

    <!-- 原始卡片内容 -->
    <div class="p-3 pt-6 h-full flex flex-col">
      <!-- 笔记列表 -->
      <div class="flex-1 overflow-y-auto overflow-x-visible space-y-1 relative z-30 p-1" ref="noteListRef">
        <!-- 空状态显示：居中显示图标和文字 -->
        <div v-if="notes.length === 0" class="flex flex-col items-center justify-center h-full text-gray-400 dark:text-gray-500">
          <Icon :icon="penNewSquareBoldDuotone" class="text-4xl text-yellow-500 mb-2" />
          <span class="text-sm">暂无闪记</span>
        </div>
        <div
          v-for="note in displayNotes"
          :key="note.id"
          class="relative bg-white dark:bg-gray-800 rounded-lg p-2.5 border border-gray-200 dark:border-gray-700 shadow text-gray-800 dark:text-gray-200"
        >
          <div class="text-base leading-relaxed break-words">{{ note.title }}</div>
        </div>

        <!-- 查看更多提示 -->
        <div v-if="notes.length > 3" class="text-center pt-2">
          <button
            @click="goToNoteManager"
            class="text-sm text-blue-500 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-300 transition-colors"
          >
            查看全部 {{ notes.length }} 条笔记 →
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, shallowRef, onMounted, onUnmounted } from "vue";
import { Icon } from "@iconify/vue";

// 导入离线图标
import notebookBoldDuotone from "@iconify-icons/solar/notebook-bold-duotone";
import penNewSquareBoldDuotone from "@iconify-icons/solar/pen-new-square-bold-duotone";
import dayjs from "dayjs";
import { ipc } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";
// 移除后端API导入，只使用本地数据源
import { ElMessage } from "element-plus";

interface Note {
  id: number;
  content: string;
  time: string;
  title?: string;
}

interface DbNote {
  id: number;
  content: string;
  created_at: string;
  title?: string;
}

const noteListRef = ref<HTMLDivElement>();
const notes = shallowRef<Note[]>([]);

const displayNotes = computed(() => notes.value.slice(0, 3));

const goToNoteManager = () => {
  ipc
    .invoke(ipcApiRoute.openNoteManagerWindow)
    .then(() => {
      console.log("笔记管理窗口已打开");
    })
    .catch((error: any) => {
      console.error("打开笔记管理窗口失败:", error);
      ElMessage.error("打开笔记管理窗口失败");
    });
};

const loadNotes = async () => {
  try {
    // 只从本地SQLite获取笔记
    const result: DbNote[] = await ipc.invoke(ipcApiRoute.getAllNotes);
    notes.value = result.map(n => ({
      id: n.id,
      content: n.content,
      title: n.title,
      time: dayjs(n.created_at).format("HH:mm DD/MM")
    }));

    console.log("从本地加载笔记成功:", notes.value.length, "条");
  } catch (error) {
    console.error("获取笔记失败:", error);
    notes.value = [];
    ElMessage.error("加载笔记失败");
  }
};

onMounted(() => {
  loadNotes();

  // 监听笔记更新事件
  ipc?.on('note-updated', (_event: any, savedNote: DbNote) => {
    console.log('收到笔记更新事件:', savedNote)

    // 直接重新加载笔记列表以确保数据同步
    loadNotes();
  });
})

onUnmounted(() => {
  // 移除所有事件监听
  ipc.removeAllListeners("note-updated");
});
</script>

<style scoped lang="scss">
.widget-container {
  overflow: visible;
}

// 移除所有样式，使用父级样式

// 移除暗色模式样式

// 图标静态效果
.icon-shadow {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.widget-btn {
  // Light theme (default)
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease-out;
  color: rgba(0, 0, 0, 0.7);
  position: relative;
  z-index: 60;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);

  &:hover {
    background: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0.12);
    color: rgba(0, 0, 0, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  }

  &:active {
    background: rgba(0, 0, 0, 0.1);
    transform: translateY(0);
    transition-duration: 0.05s;
    box-shadow: none;
  }

  // Dark theme
  :global(.dark) & {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.8);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &:active {
      background: rgba(255, 255, 255, 0.15);
      box-shadow: none;
    }
  }
}
</style>
