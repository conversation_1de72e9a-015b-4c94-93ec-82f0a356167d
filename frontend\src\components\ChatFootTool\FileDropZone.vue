<template>
  <div @dragenter.prevent @dragover.prevent @dragleave.prevent @drop.prevent="handleDrop">
    <slot />
  </div>
  
  <el-dialog v-model="state.visible" :show-close="false" :close-on-click-modal="false" class="custom-dialog">
    <template #header>
      <button class="close-btn" @click="handleCancel">
        <font-awesome-icon :icon="['fas', 'times']" />
      </button>
      <div class="flex items-center gap-2">
        <font-awesome-icon :icon="['fas', 'file']" class="icon" />
        <span class="title">选择密级</span>
      </div>
    </template>
    <template #default>
      <ul class="mb-5 p-2 border-b border-dotted">
        <li class="flex items-center" v-for="(file, index) in dragFilesList" :key="index">
          <span class="inline-flex items-center" v-if="getFileType(file.type) == 'image'"><el-icon :size="18" color="#7c3aed"><PictureFilled /></el-icon></span>
          <span class="inline-flex items-center" v-else><el-icon :size="18" color="#7c3aed"><Files /></el-icon></span>
          <span class="pl-1 text-[12px]">{{ file.name }}</span>
        </li>
      </ul>
      <div class="pl-2">
        <el-radio-group v-model="state.secret">
          <el-radio v-for="(item, index) in levelList" :key="item.value" :value="item.value">
            <span class="ml-2">{{ item.label }}</span>
          </el-radio>
        </el-radio-group>
      </div>
      <div class="mt-10 algin-center">
        <el-button type="primary" @click="handleSure(dragFilesList, state.secret)">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { updateFile } from '@/api/infra/file';
import { useHistoryStore } from '@/stores/modules/history';
import { useTalkStore } from '@/stores/modules/talk';
import { useUserStore } from '@/stores/modules/user';
import { SecretLevelConverter } from '@/utils/secretLevelConverter';
import WebSocketClient from '@/utils/websocket/websocketClient';
import { PictureFilled, Files } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { computed, reactive, ref, watchEffect } from 'vue';
import * as historyApi from "@/api/modules/history";
import { useRecentStore } from '@/stores/modules/recent';
import { MessageCode, MessageSendInfo } from '@/utils/websocket/messageInfo';
import { ipc, isEE } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/ipc/ipcApi';
import { AxiosProgressEvent } from 'axios';

const userStore = useUserStore();
const talkStore = useTalkStore();
const historyStore = useHistoryStore()
const recentStore = useRecentStore()
const state = reactive({
  visible: false,
  secret: 0
})

watchEffect(() => {
});

const levelList = computed(() => {
  const contactSecret = Math.min(talkStore.activeContact.secret, userStore.secretLevel);
  return SecretLevelConverter.getOptions("obj").filter((item: any) => item.value <= contactSecret);
})

type FileType = File & { fileId: number } 

const dragFilesList = ref<FileList| null>(null)

const isFolder = (items: DataTransferItemList) => {
  for (let item of items) {
    if (item.webkitGetAsEntry) {
      const entry = item.webkitGetAsEntry();
      if (entry?.isDirectory) return true
    }
  }
  return false
}

const handleDrop = ({ dataTransfer }: DragEvent) => {
  if (!dataTransfer) return

  if (isFolder(dataTransfer.items)) {
    ElMessage.error('无法发送文件夹')
    return
  }
  const files = dataTransfer.files;

  if (files?.length == 0) return 
  else if (files?.length > 1) {
    ElMessage.error('只能发送一个文件')
  }
  console.log(files)
  const typesToBlock = ['.c', '.exe', '.dll', '.bat', '.iso', '.cmd', '.sh'];
  for(let file of files as unknown as FileType[]) {
    const fileName = file.name.toLowerCase();
    if (file.size > 500 * 1024 * 1024) {
      ElMessage.error('文件大小不能超过500MB');
      return false;
    } else if (typesToBlock.some(type => fileName.endsWith(type))) {
      ElMessage.error('该类型文件可能存在风险，请打包后上传');
      return false;
    }
    file.fileId = (new Date()).getTime()
  }
  
  dragFilesList.value = files
  state.visible = true
}

const getFileType = (type: string) => {
  if (type && type.includes('image')) return 'image'

  return 'other'
}

const handleCancel = () => {
  state.visible = false
}


const setShortHistory = (file: any, info: any) => {
  historyStore.addMsgBuffer(file.fileId, info);
  let newMsg: any = {
    avatar: userStore.avatar,
    cancel: 0,
    createTime: new Date().getTime(),
    deleteFlag: 1,
    id: file.fileId.toString(),
    msg: file.name,
    contentId: '',
    msgType: info.msgType,
    receiver: info.receiver,
    secret: info.secret,
    sender: info.sender,
    senderName: info.senderName,
    isTop: "0",
    online: "on",
    isRead: "1",
    progress: 0,
    proStatus: 0
  };
  historyStore.setMsgHistory(info.receiver, file.fileId, newMsg);
};

const handleSure = async (filesList: FileList | null, secret: number) => {
  if (!secret) {
    ElMessage.error('请选择密级！');
    return false;
  }
  if (filesList && filesList[0]) {
    let file = filesList[0] as FileType
    const isImg = file.type.includes('image')
    let fInfo = {
      chatType: talkStore.activeContact?.chatType,
      sender: userStore.userId,
      senderName: userStore.name,
      receiver: talkStore.activeChatId,
      receiverName: talkStore.activeContact.contactName,
      secret: secret,
      msg: file.name,
      msgType: isImg ? 2 : 1,
      quoteId: "",
      atUserIds: "",
      contentId: ""
    };
    setShortHistory(file, fInfo);
  
    const formData = new FormData();
    formData.append("file", file);
    formData.append("fileId", file.fileId.toString());
    
    state.visible = false
    updateFile(formData, {
      onUploadProgress (event: AxiosProgressEvent){
        let percentCompleted = Math.round((event.loaded * 100) / (event.total ?? 1));
        let dataInfo = historyStore.msgBuffer[file.fileId];
        let index = historyStore.msgHistory[dataInfo.receiver].findIndex((item: any) => item.id == file.fileId);
        if (percentCompleted < 99) {
          historyStore.msgHistory[dataInfo.receiver][index].progress = percentCompleted;
        }
        if (percentCompleted == 100) {
          historyStore.msgHistory[dataInfo.receiver][index].progress = 96;
        }
      }
    }).then(res => {
      state.secret = 0
      dragFilesList.value = null
      
      if (isImg) {
        uploadImageSuccess(res, file)
      } else {
        uploadFileSuccess(res, file)
      }
    })
  }
}

const webSocketManager = WebSocketClient.getInstance(); // 无需参数

const uploadImageSuccess = async (resMsg: any, file: any) => {
  if (resMsg.code == 0) {
    if (resMsg.data.id == 0) {
      let dataInfo = historyStore.msgBuffer[file.fileId];
      let index = historyStore.msgHistory[dataInfo.receiver].findIndex((item: any) => item.id == file.fileId);
      historyStore.msgHistory[dataInfo.receiver][index].proStatus = 1;
      ElMessage.error("文件上传失败");
      return;
    } else {
      let dataInfo = historyStore.msgBuffer[resMsg.data.fileId];
      if (dataInfo) {
        dataInfo.contentId = resMsg.data.id;
        let index = historyStore.msgHistory[dataInfo.receiver].findIndex((item: any) => item.id == file.fileId);
        historyStore.msgHistory[dataInfo.receiver][index].progress = 100;
        historyStore.deleteMsgBuffer(resMsg.data.fileId);
        let res: any = {};
        if (dataInfo.chatType != 0) {
          res = await historyApi.saveGroupMessage(dataInfo);
        } else {
          res = await historyApi.saveUserMessage(dataInfo);
        }
        if (res.code == 0) {
          let addMsg: any = res.data;
          addMsg.fileId = resMsg.data.fileId;
          addMsg.chatType = dataInfo.chatType;

          if (dataInfo.chatType == 0) {
            historyStore.updateHistory(addMsg.receiver, addMsg.fileId, addMsg);
            recentStore.updateLastMsg(addMsg.receiver, addMsg.msg, addMsg.id, addMsg.msgType, addMsg.updateTime);
          }
          recentStore.updateOrder(addMsg.receiver);

          let msg: MessageSendInfo = {
            code: dataInfo.chatType ? MessageCode.GROUP : MessageCode.PRAIVTE,
            data: {
              id: addMsg.id,
              fromId: addMsg.sender,
              toId: addMsg.receiver,
              atId: [],
              isGroup: dataInfo.chatType ? true : false,
              contentId: addMsg.contentId,
              chatType: dataInfo.chatType,
              content: {
                msg: addMsg.msg,
                time: addMsg.createTime,
                secret: addMsg.secret,
                fileId: addMsg.fileId,
                quoteSenderName: addMsg.quoteSenderName,
                quoteContent: addMsg.quoteContent,
                quoteMessageFile: addMsg.quoteMessageFile
              },
              contentType: addMsg.msgType
            }
          };

          saveLocalCache(msg, addMsg, dataInfo.chatType)
        }
      }
    }
  } else {
    let dataInfo = historyStore.msgBuffer[file.fileId];
    let index = historyStore.msgHistory[dataInfo.receiver].findIndex((item: any) => item.id == file.fileId);
    historyStore.msgHistory[dataInfo.receiver][index].proStatus = 1;
    historyStore.deleteMsgBuffer(file.fileId);
    ElMessage.error("文件上传失败");
  }
};

const uploadFileSuccess = async (resMsg: any, file: any) => {
  if (resMsg.code == 0) {
    if (resMsg.data.id == 0) {
      let dataInfo = historyStore.msgBuffer[file.fileId];
      let index = historyStore.msgHistory[dataInfo.receiver].findIndex((item: any) => item.id == file.fileId);
      historyStore.msgHistory[dataInfo.receiver][index].proStatus = 1;
      historyStore.deleteMsgBuffer(file.fileId);
      ElMessage.error("文件上传失败");
      return;
    } else {
      let dataInfo = historyStore.msgBuffer[resMsg.data.fileId];
      if (dataInfo) {
        let index = historyStore.msgHistory[dataInfo.receiver].findIndex((item: any) => item.id == file.fileId);
        historyStore.msgHistory[dataInfo.receiver][index].progress = 100;
        historyStore.msgHistory[dataInfo.receiver][index].proStatus = 0;
        historyStore.deleteMsgBuffer(resMsg.data.fileId);
        dataInfo.contentId = resMsg.data.id;
        let res: any = {};
        if (dataInfo.chatType != 0) {
          res = await historyApi.saveGroupMessage(dataInfo);
        } else {
          res = await historyApi.saveUserMessage(dataInfo);
        }
        if (res.code == 0) {
          let addMsg: any = res.data;
          addMsg.fileId = resMsg.data.fileId;
          addMsg.chatType = dataInfo.chatType;
          if (dataInfo.chatType == 0) {
            historyStore.updateHistory(addMsg.receiver, addMsg.fileId, addMsg);
            recentStore.updateLastMsg(addMsg.receiver, addMsg.msg, addMsg.id, addMsg.msgType, addMsg.updateTime);
          }
          recentStore.updateOrder(addMsg.receiver);
          let msg: MessageSendInfo = {
            code: dataInfo.chatType ? MessageCode.GROUP : MessageCode.PRAIVTE,
            data: {
              id: addMsg.id,
              fromId: addMsg.sender,
              toId: addMsg.receiver,
              atId: [],
              isGroup: dataInfo.chatType ? true : false,
              contentId: addMsg.contentId,
              chatType: dataInfo.chatType,
              content: {
                msg: addMsg.msg,
                time: addMsg.createTime,
                secret: addMsg.secret,
                fileId: addMsg.fileId,
                quoteSenderName: addMsg.quoteSenderName,
                quoteContent: addMsg.quoteContent,
                quoteMessageFile: addMsg.quoteMessageFile
              },
              contentType: addMsg.msgType
            }
          };
          saveLocalCache(msg, addMsg, dataInfo.chatType)
        }
      }
    }
  } else {
    let dataInfo = historyStore.msgBuffer[file.fileId];
    let index = historyStore.msgHistory[dataInfo.receiver].findIndex((item: any) => item.id == file.fileId);
    historyStore.msgHistory[dataInfo.receiver][index].proStatus = 1;
    historyStore.deleteMsgBuffer(file.fileId);
    ElMessage.error("文件上传失败!");
  }
};

const saveLocalCache = (msg: any, addMsg: any, chatType: any) => {
  try {
    webSocketManager.send(msg);
  } catch (error) {
    
  }
  talkStore.setStatus(true)
  // 存储消息数据到客户端
  if (isEE) {
    let ipcMsg = JSON.stringify(addMsg);
    if (chatType) {
      ipc.invoke(ipcApiRoute.addGroupMessage, ipcMsg);
    } else {
      ipc.invoke(ipcApiRoute.addUserMessage, ipcMsg);
    }
  }
}

</script>

<style lang="sass" scoped></style>