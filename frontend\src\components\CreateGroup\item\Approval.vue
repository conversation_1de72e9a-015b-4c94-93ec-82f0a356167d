<template>
  <div>
    <div class="flex justify-between member">
      <el-tabs v-model="activeTab" class="custom-tabs">
        <el-tab-pane label="组织架构" name="1">
          <el-scrollbar height="240">
            <org-tree :group-scope="groupInfo.groupScope" @node-click="handleClick"></org-tree>
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
      <el-transfer
        class="custom-transfer"
        v-model="transferValue"
        filterable
        filter-placeholder="搜索成员"
        :titles="['可选择人员', '已选择人员']"
        :button-texts="['移除', '添加']"
        :props="transferProps"
        :data="transferData"
      >
        <template #default="{ option }">
          <span>{{ option.name }}</span>
        </template>
      </el-transfer>
    </div>
    <div class="absolute bottom-0 right-0">
      <el-button type="default" @click="prev">上一步</el-button>
      <el-button type="primary" @click="chooseMember">下一步</el-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="Approval">
import { ref } from "vue";
import { useTalkStore } from "@/stores/modules/talk";
import { ElMessage } from "element-plus";
import { getOrgUser } from "@/api/modules/org";
import OrgTree from "@/components/tree/org-tree.vue";
import { useContactsStore } from "@/stores/modules/contacts";

const talkStore = useTalkStore();
const contactsStore = useContactsStore()

const groupInfo: any = {
  ...talkStore.createGroup,
  approval: []
};

const activeTab = ref("1");
const transferData = ref<any>([]);
const transferValue = ref<any>([]);
const transferProps = {
  key: "id",
  label: "name"
};

const handleClick = async (orgCode: string) => {
  let newList: any[] = [];
  const params = {
    orgCode: orgCode,
    roleCode: groupInfo.groupScope
  };
  const res: any = await getOrgUser(params);
  if (res.code == 0) {
    newList = res.data;

    newList?.forEach((item: any) => {
      let fileds: any = ["avatar", 'isOnline']
      if (item.online === null) fileds = ["avatar"]
      // 更新用户状态
      contactsStore.updateContact(
        {
          id: item.id,
          name: item.name,
          avatar: item.avatar,
          secretLevel: item.secretLevel,
          isOnline: item.online == "on"
        },
        fileds
      );
    })
    // 找出 transferValue 中的选中项，不在 newList 中的（来自旧数据）
    const selectedSet = new Set(transferValue.value);
    const preservedItems = transferData.value.filter(
      (item: any) => selectedSet.has(item.id) && !newList.some(n => n.id === item.id)
    );
    // 合并新数据和已选中的旧数据
    transferData.value = [...newList, ...preservedItems];
    transferData.value.sort((a: any, b: any) => {
      if (a.orderId === b.orderId) {
        // 如果orderId相同，则根据online属性排序：true优先于false
        return b.online - a.online;
      } else {
        // orderId不同，按orderId升序排列
        return a.orderId - b.orderId;
      }
    });
  }
};

const emit = defineEmits(["prev-step", "next-step"]);

const prev = () => {
  emit("prev-step");
};

const chooseMember = () => {
  if (transferValue.value && transferValue.value.length > 0) {
    const arr = transferData.value.filter((item: any) => transferValue.value.includes(item.id));
    groupInfo.approval = arr;
    talkStore.setNewGroup(groupInfo);
    emit("next-step");
  } else {
    ElMessage.error("请选择审批人");
  }
};
</script>

<style lang="scss" scoped></style>
