<template>
  <ul class="common-emoji">
    <li v-for="(item, index) in talkStore.commonEmojiList" :key="index">
      <img :alt="item" :src="facesMap[item]" :title="item" @click="onSelect(item)" />
    </li>
  </ul>
</template>
<script setup lang="ts">
import { computed } from "vue";
import { useTalkStore } from "@/stores/modules/talk";

const talkStore = useTalkStore();

const facesMap = computed(() => talkStore.commonEmoji);

const emit = defineEmits(["insertEmoji"]);
const onSelect = (item: any) => {
  emit("insertEmoji", "face" + item);
};
</script>

<style scoped lang="less">
.common-emoji {
  // 覆盖ul默认样式
  list-style: none;
  display: block;
  padding: 0;
  margin: 0;
  height: 400px;
  li {
    width: 32px;
    height: 32px;
    display: inline-block;
    margin: 10px;
    img {
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
  }
}
</style>
