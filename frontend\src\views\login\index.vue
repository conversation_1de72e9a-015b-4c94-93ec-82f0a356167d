<template>
  <div class="login-container" :class="[isActive ? 'loginRadius' : '']">
    <div class="login-box">
      <div class="login-left">
        <img class="login-left-img" src="@/assets/images/login_left.png" alt="login" />
      </div>
      <div class="login-form">
        <div class="login-logo">
          <img class="login-icon" src="@/assets/images/logo.svg" alt="" />
          <h2 class="logo-text">云雀</h2>
        </div>
        <LoginForm />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="login">
import { onMounted, ref } from "vue";
import LoginForm from "./components/LoginForm.vue";
import { ipc, isEE } from "@/utils/ipcRenderer";
const isActive = ref(true)
onMounted(() => {
  if (isEE) {
    isActive.value = true
  } else {
    isActive.value = false
  }
})
</script>

<style scoped lang="scss">
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  background-color: #eeeeee;
  background-image: url("@/assets/images/login_bg.svg");
  background-size: cover;

  .login-box {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0 30px;
    background-color: rgb(255 255 255 / 80%);
    border-radius: 0;
    border: none;

    .login-left {
      width: 800px;
      margin-right: 10px;
    }

    .login-form {
      width: 420px;
      padding: 40px 35px 40px;
      background-color: var(--el-bg-color);
      border-radius: 8px;
      border: 1px solid #e0e0e0;

      .login-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 45px;

        .login-icon {
          width: 60px;
        }

        .logo-text {
          padding: 0 0 0 25px;
          margin: 0;
          font-size: 42px;
          font-weight: bold;
          color: #34495e;
          white-space: nowrap;
        }
      }

      .el-form-item {
        margin-bottom: 40px;
      }

      .login-btn {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: 40px;
        white-space: nowrap;

        .el-button {
          width: 185px;
        }
      }
    }
  }
}
.loginRadius{
  border-radius: 12px;
}
@media screen and (width <=1250px) {
  .login-left {
    display: none;
  }
  .login-box {
    padding: 0 20px;
  }
}

@media screen and (width <=600px) {
  .login-form {
    width: calc(100% - 20px) !important;
    padding: 30px 25px !important;
  }
  .login-box {
    padding: 0 10px;
  }
}
</style>
