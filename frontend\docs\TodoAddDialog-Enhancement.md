# TodoAddDialog 智能规划功能增强

## 功能概述

为 `TodoAddDialog` 组件添加了智能规划功能，支持流式数据处理、状态显示和待办事项预览。

## 主要功能

### 1. 智能规划按钮样式优化

- **蓝紫相间彩色渐变**：使用 `linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)`
- **动态渐变动画**：背景色持续流动变化，吸引用户注意
- **悬停效果**：鼠标悬停时按钮上升并添加阴影效果
- **魔法图标**：使用 `wand-magic-sparkles` 图标，生成时旋转动画
- **点击波纹**：点击时产生白色波纹扩散效果

### 2. 流式数据处理

#### API 增强
- 在 `chat.ts` 中扩展了 `StreamDataChunk` 类型，支持 `'node_started'` 事件
- 添加了 `pendingEventType` 机制，处理 SSE 事件与数据的关联
- 支持后端发送的 `node_started` 事件，用于显示处理状态

#### 状态显示
- **Shimmer 效果**：输入框下方显示流动的 shimmer 动画
- **状态文本**：显示当前 AI 处理阶段的描述信息
- **多行 shimmer**：3条不同长度的动画线条，营造加载感

### 3. 待办事项预览

#### 解析机制
- 检测流式返回中的 `##TODOList` 标记
- 自动解析多种格式的待办事项：
  - 破折号格式：`- 待办内容`
  - 星号格式：`* 待办内容`
  - 加号格式：`+ 待办内容`
  - 数字格式：`1. 待办内容`

#### 预览卡片
- **实时显示**：待办事项逐条显示预览卡片
- **生成状态**：最后一项显示三点跳动动画表示正在生成
- **优雅样式**：浅色卡片背景，绿色勾选图标
- **渐变效果**：生成中的卡片使用紫色渐变背景

## 技术实现

### 流式数据处理流程

```typescript
// 1. 发起流式请求
sendAiStreamChatMessage(request, {
  onData: chunk => {
    // 2. 处理状态更新
    if (chunk.type === 'node_started' && chunk.content) {
      currentStatus.value = chunk.content
      return
    }
    
    // 3. 处理文本内容
    resultText += chunk.content
    
    // 4. 检查待办事项标记
    if (chunk.content.includes('##TODOList') || resultText.includes('##TODOList')) {
      parseTodoItems(resultText)
    } else {
      newTodo.value = resultText
    }
  }
})
```

### 待办事项解析算法

```typescript
const parseTodoItems = (text: string) => {
  const lines = text.split('\n')
  const newTodos: PreviewTodo[] = []
  
  let inTodoSection = false
  for (const line of lines) {
    if (line.includes('##TODOList')) {
      inTodoSection = true
      continue
    }
    
    if (inTodoSection && line.trim()) {
      // 匹配多种待办事项格式
      const todoMatch = line.match(/^[-*+]\s+(.+)$/) || 
                       line.match(/^\d+\.\s+(.+)$/) ||
                       line.match(/^(.+)$/)
      
      if (todoMatch && todoMatch[1]) {
        const content = todoMatch[1].trim()
        if (content && !content.startsWith('#')) {
          newTodos.push({
            content: content,
            isGenerating: false
          })
        }
      }
    }
  }
  
  // 添加生成中状态
  if (newTodos.length > 0) {
    const lastTodo = newTodos[newTodos.length - 1]
    if (lastTodo && !lastTodo.content.endsWith('。') && !lastTodo.content.endsWith('.')) {
      lastTodo.isGenerating = true
    }
  }
  
  previewTodos.value = newTodos
}
```

## 样式特色

### 智能规划按钮
```scss
.ai-magic-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-size: 300% 300%;
  animation: gradientShift 3s ease-in-out infinite;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  }
  
  // 点击波纹效果
  &::before {
    content: '';
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: width 0.6s, height 0.6s;
  }
  
  &:active::before {
    width: 300px;
    height: 300px;
  }
}
```

### Shimmer 动画
```scss
.shimmer-animation {
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
```

### 跳动点动画
```scss
@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
```

## 暗色模式支持

组件完全支持暗色模式，包括：
- 状态容器背景自动适配
- 预览卡片颜色调整
- 文本颜色智能切换
- 边框颜色相应变化

## 用户交互流程

1. **输入想法**：用户在输入框中输入待办事项的想法
2. **点击智能规划**：点击彩色的"AI智能规划"按钮
3. **显示状态**：输入框下方出现 shimmer 动画和状态文本
4. **实时预览**：AI 返回待办事项时，逐条显示预览卡片
5. **完成生成**：最后一项的跳动动画消失，生成完成

## 性能优化

- 使用 `shallowRef` 优化待办事项数组的响应性
- 合理使用 `nextTick` 确保 DOM 更新时机
- 流式数据处理避免大量内存占用
- CSS 动画使用 `transform` 和 `opacity` 确保硬件加速

## 兼容性

- 支持所有现代浏览器
- 自动降级处理不支持的 CSS 特性
- 响应式设计适配不同屏幕尺寸
- 完整的键盘导航支持

## 扩展性

组件设计具有良好的扩展性：
- 易于添加新的流式事件类型
- 待办事项解析算法可扩展新格式
- 样式系统支持主题定制
- API 接口预留扩展字段 