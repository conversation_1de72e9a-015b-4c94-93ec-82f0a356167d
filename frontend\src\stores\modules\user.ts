import { defineStore } from "pinia";
import { UserState } from "../interface";
import piniaPersistConfig from "../helper/persist";
import { getPersonInfo, getUserSecretDict, updateUserInfo } from "@/api/modules/contact";

import { isEE, ipc } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { useContactsStore } from "./contacts";
import { ElMessage } from "element-plus";

export const useUserStore = defineStore("lark-user", {
  state: (): UserState => ({
    token: "",
    name: "",
    avatar: "",
    userId: "",
    userInfo: {},
    secretLevel: 30,
    contactId: ""
  }),
  getters: {},
  actions: {
    // 存储token
    setToken(token: any) {
      this.token = token.accessToken;
      this.userId = token.userId;
    },
    // 获取用户信息
    async getUserInfo() {
      try {
        const res: any = await getPersonInfo(this.userId);
        if (res.code == 0) {
          this.name = res.data.name;
          this.avatar = res.data.avatar;
          this.secretLevel = Number(res.data.secretLevel);
          this.setUserInfo(res.data);
          return true;
        }
      } catch (error: any) {
        ElMessage.error(error.message);
        this.token = "";
        this.userId = "";
        return false;
      }
    },
    // 修改用户信息
    async updateUserInfoFc(params: any, num: number) {
      const res: any = await updateUserInfo(params);
      if (res.code == 0) {
        if (num == 1) this.userInfo.otel = params.otel;
        if (num == 2) this.userInfo.oemail = params.oemail;
        if (num == 3) this.userInfo.description = params.description;
      }
    },
    // 设置用户信息
    setUserInfo(userInfo: any) {
      const contactsStore = useContactsStore();
      this.userInfo = userInfo;

      const addUserInfo = {
        id: userInfo.id,
        name: userInfo.name,
        avatar: userInfo.avatar,
        secretLevel: userInfo.secretLevel,
        isOnline: userInfo.online == "on"
      };
      let fileds: any = ["avatar", 'isOnline']
      if (userInfo.online === null) fileds = ["avatar"]

      contactsStore.updateContact(addUserInfo, fileds);

      if (isEE) {
        ipc.send(ipcApiRoute.setCache, ["userInfo", JSON.stringify(userInfo)]);
      }
    },
    // 清除token
    clearLocalStorage() {
      localStorage.clear();
      sessionStorage.clear();
      if (isEE) {
        ipc.send(ipcApiRoute.removeCache, "userInfo");
      }
    },
    // 获取用户密级
    async getUserSecret() {
      const { data } = await getUserSecretDict();
      this.secretLevel = data.list;
    }
  },
  persist: piniaPersistConfig("lark-user")
});
