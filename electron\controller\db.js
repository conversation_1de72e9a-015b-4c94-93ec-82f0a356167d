"use strict";

const { Controller } = require("ee-core");
const Services = require("ee-core/services");
const CoreWindow = require("ee-core/electron/window");

const MessageService = require("../service/database/message");
const UserMessageService = new MessageService("user_message");
const GroupMessageService = new MessageService("group_message");

/**
 * 数据缓存操作
 * @class
 */
class DbController extends Controller {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 所有数据库操作
   */
  async all(args) {
    const { service, method, args: params } = args;
    const result = await Services.get(service)[method](...params);
    return result;
  }

  /**
   * JSON缓存-自定义key
   */
  setItem([name, value]) {
    Services.get("database.jsondb").setItem(name, value);
  }
  getItem(name) {
    return Services.get("database.jsondb").getItem(name);
  }
  removeItem(name) {
    this.setItem([name, null]);
  }

  /**
   * JSON缓存-系统配置字段
   */
  getAllConfigField() {
    return Services.get("database.jsondb").getAllConfigField();
  }
  async saveConfigField(data) {
    await Services.get("database.jsondb").saveConfigField(data);
    // 通知主窗口 更新设置项
    const win = CoreWindow.getMainWindow();
    win.webContents.send("when-config-update");
  }

  /**
   * 最近联系人相关
   */
  getContactList() {
    return Services.get("database.contact").getContactList();
  }
  addContact(content) {
    return Services.get("database.contact").addContact(content);
  }
  updateContact(content) {
    return Services.get("database.contact").updateContact(content);
  }
  removeContact(id) {
    return Services.get("database.contact").removeContact(id);
  }
  removeAllContact() {
    return Services.get("database.contact").removeAllContact();
  }
  syncContactData(contactData) {
    return Services.get("database.contact").syncContactData(contactData);
  }

  /**
   * 个人消息相关
   */
  getUserMessageList(params) {
    return UserMessageService.getMessageList(params);
  }
  addUserMessage(content) {
    return UserMessageService.addMessage(content);
  }
  updateUserMessageStatus(params) {
    return UserMessageService.updateStatus(params);
  }
  updateUserMessage(content) {
    return UserMessageService.updateMessage(content);
  }
  removeUserMessage(id) {
    return UserMessageService.removeMessage(id);
  }
  /**
   * 群组消息相关
   */
  getGroupMessageList(params) {
    return GroupMessageService.getMessageList2(params);
  }
  addGroupMessage(content) {
    return GroupMessageService.addMessage(content);
  }
  updateGroupMessageStatus(params) {
    return GroupMessageService.updateStatus(params);
  }
  updateGroupMessage(content) {
    return GroupMessageService.updateMessage(content);
  }
  removeGroupMessage(id) {
    return GroupMessageService.removeMessage(id);
  }

  /**
   * 持久化state相关
   */
  getPiniaState(key) {
    return Services.get("database.piniastate").getPiniaState(key);
  }
  setPiniaState([key, value]) {
    return Services.get("database.piniastate").setPiniaState([key, value]);
  }
  removeAllState() {
    return Services.get("database.piniastate").removeAllState();
  }

  /**
   * 清空个人数据
   */
  async clear() {
    await this.removeAllContact();
    await this.removeAllState();
    await UserMessageService.clearAllMessage()
    await GroupMessageService.clearAllMessage()
    await Services.get("database.jsondb").removeItem("userInfo");
    // 通知主窗口 清空缓存
    const win = CoreWindow.getMainWindow();
    if (!win.isVisible()) {
      win.show();
    } else {
      win.focus();
    }
    win.webContents.send("clear-cache");
  }
}

DbController.toString = () => "[class DbController]";
module.exports = DbController;
