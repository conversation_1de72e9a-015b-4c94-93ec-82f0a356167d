import request from "@/api";
import { useUserStore } from "@/stores/modules/user";

// AI聊天接口相关类型定义
export interface AiChatRequest {
  message: string;
  conversation_id?: string;
  model?: string;
  // 新增：支持AI应用配置
  app_id?: string | number;
  inputs?: Record<string, any>;
}

export interface AiChatResponse {
  reply: string;
  conversation_id: string;
  timestamp: number;
}

// 文件信息接口 - 与后端FileInfo类对应
export interface FileInfo {
  /**
   * 文件类型：image, document, audio, video
   */
  type: string;

  /**
   * 文件传输方式：remote_url, local_file
   */
  transferMethod: string;

  /**
   * 文件URL（当transferMethod为remote_url时）
   */
  url?: string;

  /**
   * 上传文件ID（当transferMethod为local_file时）
   */
  uploadFileId?: string;
}

// 扩展的文件信息接口 - 包含前端显示需要的字段
export interface ExtendedFileInfo extends FileInfo {
  fileId: string | number;
  fileName: string;
  fileSize: number;
  fileType: string;
}

// 流式聊天请求参数
export interface AiStreamChatRequest {
  query: string; // 用户输入的聊天内容
  conversationId?: string; // 对话ID，用于维持会话上下文
  app_id?: string | number;
  inputs?: Record<string, any>;
  files?: FileInfo[]; // 附加的文件信息
  shouldProcessNodeStarted?: boolean; // 是否处理node_started类型数据，默认false
}

// 流式数据块类型
export interface StreamDataChunk {
  type: 'text' | 'error' | 'done' | 'node_started';
  content: string;
  conversationId?: string;
  timestamp?: number;
}

// Dify 流式消息 DTO 接口 - 对应服务端返回的数据结构
export interface DifyStreamMessageDTO {
  content: string;
  message_id: string;
  task_id: string;
  conversation_id: string;
  created_at: number;
}

// 流式响应处理器类型
export interface StreamResponseHandler {
  onData?: (chunk: StreamDataChunk) => void; // 接收到数据块时的回调
  onError?: (error: Error) => void; // 发生错误时的回调
  onComplete?: () => void; // 流结束时的回调
}

// 流控制器接口
export interface StreamController {
  abort: () => void; // 取消请求
  isActive: boolean; // 是否正在活跃状态
}

/**
 * 发送AI聊天消息
 * @param params 聊天请求参数
 * @returns Promise<AiChatResponse>
 */
export const sendAiChatMessage = async (params: AiChatRequest): Promise<AiChatResponse> => {
  try {
    // 调用AI聊天接口 - 修正接口路径
    const response = await request.post<AiChatResponse>("/app-api/ai/chat", params);

    // 验证响应数据
    if (!response.data || !response.data.reply) {
      throw new Error('AI接口返回数据格式错误');
    }

    return response.data;
  } catch (error) {
    console.error('AI聊天接口调用失败:', error);
    // 直接抛出错误，让调用方处理
    throw error;
  }
};

/**
 * 获取AI聊天历史
 * @param conversationId 会话ID
 * @returns Promise<AiChatResponse[]>
 */
export const getAiChatHistory = async (conversationId: string) => {
  try {
    const response = await request.get<AiChatResponse[]>(`/api/ai/chat/history/${conversationId}`);
    return response.data;
  } catch (error) {
    console.error('获取AI聊天历史失败:', error);
    return [];
  }
};

/**
 * 清除AI聊天历史
 * @param conversationId 会话ID
 * @returns Promise<boolean>
 */
export const clearAiChatHistory = async (conversationId: string) => {
  try {
    await request.delete(`/api/ai/chat/history/${conversationId}`);
    return true;
  } catch (error) {
    console.error('清除AI聊天历史失败:', error);
    return false;
  }
};

/**
 * 流式AI聊天 - 使用fetch处理SSE
 * @param params 流式聊天请求参数
 * @param handler 流式响应处理器
 * @returns StreamController 流控制器
 */
export const sendAiStreamChatMessage = (
  params: AiStreamChatRequest,
  handler: StreamResponseHandler
): StreamController => {
  const abortController = new AbortController();
  let isActive = true;

  const controller: StreamController = {
    abort: () => {
      abortController.abort();
      isActive = false;
    },
    isActive: false
  };

  // 异步执行流式请求
  (async () => {
    try {
      controller.isActive = true;

      // 构建请求URL - 使用环境变量
      const baseURL = import.meta.env.VITE_API_URL || '';
      const streamUrl = baseURL.replace(/\/$/, '') + '/app-api/ai/chat/stream';
      
      console.log('[Stream API] 使用URL:', streamUrl);

      // 构建POST请求体
      const requestBody = {
        query: params.query,
        conversationId: params.conversationId,
        app_id: params.app_id,
        inputs: params.inputs,
        files: params.files || [] // 添加文件信息
      };

      // 获取用户token - 与通用请求接口保持一致
      const headers: Record<string, string> = {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
      };

      // 使用统一的token获取方式
      const userStore = useUserStore();
      if (userStore.token) {
        headers['Authorization'] = `Bearer ${userStore.token}`;
      }

      // 发起SSE POST请求
      const response = await fetch(streamUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: abortController.signal,
      });

      console.log('[Stream API] 响应状态:', response.status, response.statusText);
      console.log('[Stream API] 响应头:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('响应体为空');
      }

      // 创建流读取器
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (isActive) {
          const { done, value } = await reader.read();

          if (done) {
            // 处理缓冲区中剩余的数据
            if (buffer.trim()) {
              console.log('[Stream API] 处理缓冲区剩余数据:', buffer);
              processStreamData(buffer, handler, params.shouldProcessNodeStarted);
            }
            console.log('[Stream API] 流式请求完成');
            handler.onComplete?.();
            break;
          }

          // 解码数据并添加到缓冲区
          buffer += decoder.decode(value, { stream: true });

          // 按行处理数据
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // 保留最后一行（可能不完整）

          for (const line of lines) {
            if (line.trim()) {
              console.log('[Stream API] 接收到原始数据行:', line);
              processStreamData(line, handler, params.shouldProcessNodeStarted);
            }
          }
        }
      } finally {
        reader.releaseLock();
        controller.isActive = false;
      }

    } catch (error) {
      controller.isActive = false;

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.log('[Stream API] 流式请求已取消');
        } else {
          console.error('[Stream API] 流式聊天请求失败:', error);
          handler.onError?.(error);
        }
      } else {
        const unknownError = new Error('未知错误');
        console.error('[Stream API] 流式聊天请求失败:', unknownError);
        handler.onError?.(unknownError);
      }
    }
  })();

  return controller;
};

/**
 * 处理流式数据 - 支持 Dify 格式
 * @param line 数据行
 * @param handler 响应处理器
 * @param shouldProcessNodeStarted 是否处理node_started类型数据
 */
let pendingEventType: string | null = null;

const processStreamData = (line: string, handler: StreamResponseHandler, shouldProcessNodeStarted: boolean = false) => {
  try {
    // 处理SSE格式的数据 - 支持 'data:' 和 'data: ' 两种格式
    if (line.startsWith('data:')) {

      // 提取数据部分，处理有无空格的情况
      let data = '';
      if (line.startsWith('data: ')) {
        data = line.substring(6).trim();
      } else if (line.startsWith('data:')) {
        data = line.substring(5).trim();
      }

      // 检查是否为结束标记
      if (data === '[DONE]' || data === '') {
        // 空数据或结束标记，跳过处理
        if (data === '[DONE]') {
          handler.onComplete?.();
        }
        return;
      }

      // 尝试解析JSON数据
      try {
        const jsonData = JSON.parse(data);

        // 检查是否为 Dify 格式的数据
        if (isDifyStreamMessage(jsonData)) {
          // 处理 Dify 格式的流式消息
          const difyMessage = jsonData as DifyStreamMessageDTO;

          // 只有当content不为空时才发送数据
          if (difyMessage.content) {
            const chunk: StreamDataChunk = {
              type: 'text',
              content: difyMessage.content,
              conversationId: difyMessage.conversation_id,
              timestamp: difyMessage.created_at || Date.now()
            };

            console.log('[Stream API] 解析 Dify 数据块:', {
              content: difyMessage.content,
              messageId: difyMessage.message_id,
              taskId: difyMessage.task_id,
              conversationId: difyMessage.conversation_id,
              timestamp: difyMessage.created_at
            });

            handler.onData?.(chunk);
          }
        } else {
          // 处理其他格式的JSON数据（向后兼容）
          const content = jsonData.content || jsonData.text || '';
          if (content) {
            // 检查是否有待处理的事件类型
            const chunkType = (pendingEventType === 'node_started' && shouldProcessNodeStarted) ? 'node_started' : (jsonData.type || 'text');

            // 如果是node_started类型但不需要处理，则跳过
            if (pendingEventType === 'node_started' && !shouldProcessNodeStarted) {
              console.log('[Stream API] 跳过node_started类型数据:', content);
              pendingEventType = null;
              return;
            }

            const chunk: StreamDataChunk = {
              type: chunkType as any,
              content: content,
              conversationId: jsonData.conversationId || jsonData.conversation_id,
              timestamp: jsonData.timestamp || Date.now()
            };

            console.log('[Stream API] 解析后的数据块:', chunk);
            handler.onData?.(chunk);

            // 清除待处理的事件类型
            pendingEventType = null;
          }
        }
      } catch (parseError) {
        // 如果不是JSON格式，直接作为文本内容处理
        if (data) {
          // 检查是否有待处理的事件类型
          const chunkType = (pendingEventType === 'node_started' && shouldProcessNodeStarted) ? 'node_started' : 'text';

          // 如果是node_started类型但不需要处理，则跳过
          if (pendingEventType === 'node_started' && !shouldProcessNodeStarted) {
            console.log('[Stream API] 跳过node_started类型数据:', data);
            pendingEventType = null;
            return;
          }

          const chunk: StreamDataChunk = {
            type: chunkType as any,
            content: data,
            timestamp: Date.now()
          };
          console.log('[Stream API] 非JSON格式数据块:', chunk);
          handler.onData?.(chunk);

          // 清除待处理的事件类型
          pendingEventType = null;
        }
      }
    } else if (line.startsWith('event:')) {
      // 处理事件类型（如果需要）
      const eventType = line.substring(6).trim();
      console.log('[Stream API] SSE事件类型:', eventType);

      // 如果是done事件，触发完成
      if (eventType === 'done') {
        console.log('[Stream API] 接收到done事件，流式响应完成');
        handler.onComplete?.();
      } else if (eventType === 'node_started') {
        // 无论是否需要处理，都设置pendingEventType，在data处理时再判断
        pendingEventType = 'node_started';
        console.log('[Stream API] 接收到node_started事件');
      }
    } else if (line.trim()) {
      // 忽略其他格式的数据，避免干扰
      console.log('[Stream API] 忽略的数据行:', line.trim());
    }
  } catch (error) {
    console.error('[Stream API] 处理流式数据失败:', error);
    handler.onError?.(new Error(`数据处理失败: ${error}`));
  }
};

/**
 * 判断是否为 Dify 流式消息格式
 * @param data 待检查的数据对象
 * @returns 是否为 Dify 格式
 */
const isDifyStreamMessage = (data: any): data is DifyStreamMessageDTO => {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof data.content === 'string' &&
    typeof data.message_id === 'string' &&
    typeof data.task_id === 'string' &&
    typeof data.conversation_id === 'string' &&
    (typeof data.created_at === 'number' || data.created_at === undefined)
  );
};
