'use strict'

const { Controller } = require('ee-core')
const { screen } = require('electron')
const Addon = require('ee-core/addon')
const Ps = require('ee-core/ps')
const Conf = require('ee-core/config')
const path = require('path')

// AI聊天窗口实例
let aiChatWin = null

/**
 * AI聊天窗口管理控制器
 */
class AiController extends Controller {
  constructor(ctx) {
    super(ctx)
    console.log('AiController loaded successfully')
  }

  /**
   * 打开AI聊天窗口
   * @param {Object} configData - AI应用配置数据
   */
  openAiChatWindow(configData = null) {
    // 如果窗口已存在，直接显示并更新配置
    if (aiChatWin) {
      aiChatWin.show()
      aiChatWin.focus()

      // 如果有新的配置数据，发送给窗口
      if (configData) {
        aiChatWin.webContents.send('ai-app-config', configData)
        console.log('[Main] 向现有AI聊天窗口发送配置数据:', configData)
      }

      return 'Window already exists'
    }

    console.log('[Main] 创建AI聊天窗口');
    console.log('[Main] 配置数据:', configData);
    const windowTitle = "AI智能对话"
    const windowName = "AI智能对话"
    const content = "#/ai-chat"

    // 构建完整的 URL
    let contentUrl = null
    let addr = "http://localhost:8080/"
    if (Ps.isProd()) {
      const mainServer = Conf.getValue("mainServer");
      console.log('[Main] 生产环境主服务器配置:', mainServer);
      if (Conf.isFileProtocol(mainServer)) {
        addr = mainServer.protocol + path.join(Ps.getHomeDir(), mainServer.indexPath);
      } else {
        addr = mainServer.protocol + mainServer.host + ":" + mainServer.port;
      }
    }
    // 在生产环境中，这里应该使用正确的文件路径
    contentUrl = addr + content
    console.log('[Main] AI聊天窗口目标URL:', contentUrl);

    const options = {
      title: windowTitle,
      width: 480,
      height: 640,
      frame: false,
      resizable: false,
      show: false,
      maximizable: false,
      minimizable: true,
      fullscreenable: false,
      acceptFirstMouse: true,
      transparent: true,
      hasShadow: false,
      alwaysOnTop: false,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        devTools: true, // 启用开发者工具以便调试
        webSecurity: false, // 在生产环境下可能需要禁用web安全以加载本地文件
      }
    }

    // 使用 ee-core 的 Addon 创建窗口
    console.log('[Main] 开始创建AI聊天窗口');
    aiChatWin = Addon.get("window").create(windowName, options)

    // 设置窗口位置（居中显示）
    const [screenWidth, screenHeight] = [
      screen.getPrimaryDisplay().workAreaSize.width,
      screen.getPrimaryDisplay().workAreaSize.height,
    ]
    aiChatWin.setPosition(
      Math.floor((screenWidth - 480) / 2),
      Math.floor((screenHeight - 640) / 2)
    )

    // 加载页面
    console.log('[Main] 开始加载AI聊天页面');
    console.log('[Main] 当前环境:', Ps.isProd() ? '生产环境' : '开发环境');
    console.log('[Main] 内容URL:', contentUrl);
    
    aiChatWin.loadURL(contentUrl)

    // 监听页面加载错误
    aiChatWin.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      console.error('[Main] AI聊天页面加载失败:', {
        errorCode,
        errorDescription, 
        validatedURL,
        targetURL: contentUrl
      });
    });

    // 监听页面加载完成
    aiChatWin.webContents.on('did-finish-load', () => {
      console.log('[Main] AI聊天页面加载完成');
    });

    // 监听控制台消息（用于调试）
    aiChatWin.webContents.on('console-message', (event, level, message, line, sourceId) => {
      console.log('[Renderer Console]', level, message);
    });

    aiChatWin.on("ready-to-show", () => {
      aiChatWin.show()
      aiChatWin.focus()
      // 在生产环境下也打开开发者工具以便调试
      aiChatWin.webContents.openDevTools()

      // 如果有配置数据，在窗口显示后发送
      if (configData) {
        setTimeout(() => {
          aiChatWin.webContents.send('ai-app-config', configData)
          console.log('[Main] 向新创建的AI聊天窗口发送配置数据:', configData)
        }, 1000) // 等待1秒确保页面完全加载
      }
    })

    aiChatWin.on("close", () => {
      aiChatWin = null
    })

    return 'AI Chat Window created successfully'
  }

  /**
   * 关闭AI聊天窗口
   */
  closeAiChatWindow() {
    if (aiChatWin) {
      aiChatWin.close()
      aiChatWin = null
    }
    return 'AI Chat Window closed'
  }

  /**
   * 最小化AI聊天窗口
   */
  minimizeAiChatWindow() {
    if (aiChatWin) {
      aiChatWin.minimize()
    }
    return 'AI Chat Window minimized'
  }

  /**
   * 获取AI聊天窗口状态
   */
  getAiChatWindowStatus() {
    return {
      exists: !!aiChatWin,
      isVisible: aiChatWin ? aiChatWin.isVisible() : false,
      isMinimized: aiChatWin ? aiChatWin.isMinimized() : false,
      isFocused: aiChatWin ? aiChatWin.isFocused() : false
    }
  }

  /**
   * 聚焦AI聊天窗口
   */
  focusAiChatWindow() {
    if (aiChatWin) {
      aiChatWin.focus()
      return 'AI Chat Window focused'
    }
    return 'AI Chat Window not found'
  }
}

AiController.toString = () => "[class AiController]"
module.exports = AiController;
