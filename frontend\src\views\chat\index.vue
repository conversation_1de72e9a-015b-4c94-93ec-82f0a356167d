<template>
  <div class="h-full flex">
    <div class="chat-tab">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane name="1">
          <template #label>
            <div class="tab-item" :class="{ active: activeTab == '1' }">
              <Icon :icon="dialog2BoldDuotone" width="24" height="24" class="mr-1.5" />
              <span>消息坞</span>
            </div>
          </template>
          <ChatRecent v-if="activeTab == '1'" />
        </el-tab-pane>
        <el-tab-pane name="2">
          <template #label>
            <div class="tab-item" :class="{ active: activeTab == '2' }">
              <Icon :icon="notebookBoldDuotone" width="24" height="24" class="mr-1.5" />
              <span>通讯录</span>
            </div>
          </template>
          <ChatContact v-if="activeTab == '2'" @node-click="handleNodeClick" @info-click="handleGroupClick" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="flex-1 overflow-hidden">
      <div class="flex h-full flex-col relative" v-if="talkStore.ifChat" style="padding-top: 61px">
        <ChatHead />
        
        <FileDropZone>
          <ChatHistory />
          <ChatFoot />
        </FileDropZone>
      </div>
      <div class="h-full p-4" v-else-if="talkStore.ifContact">
        <el-scrollbar>
          <SingleContact :loading="userLoading" :user="user" v-if="userType == 1" />
          <FriendContact :user="friends" v-if="userType == 2" />
          <TeamContact :user="user" v-if="userType == 3" />
          <GroupInfo :group-id="groupId" v-if="userType == 4" />
        </el-scrollbar>
      </div>
      <div class="flex h-full flex-col items-center justify-center" v-else>
        <Icon :icon="dialog2BoldDuotone" width="180" height="180" style="color: #eaa800" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="chat">
import { ref, onMounted, onBeforeUnmount, watchEffect, nextTick } from "vue";
import { useTalkStore } from "@/stores/modules/talk";
import { useFriendStore } from "@/stores/modules/friends";
import { useTeamStore } from "@/stores/modules/team";
import ChatHead from "./components/ChatHead.vue";
import ChatHistory from "./components/ChatContent/ChatHistory.vue";
import ChatFoot from "./components/ChatFoot/index.vue";
import SingleContact from "@/components/ContactComp/SingleContact.vue";
import FriendContact from "@/components/ContactComp/FriendContact.vue";
import TeamContact from "@/components/ContactComp/TeamContact.vue";
import GroupInfo from "@/components/ContactComp/GroupInfo.vue";
import ChatRecent from "./components/ChatRecent.vue";
import ChatContact from "./components/ChatContact.vue";
import { useOrgStore } from "@/stores/modules/org";
import { ElMessage } from "element-plus";

import { Icon } from "@iconify/vue";

// 导入离线图标
import dialog2BoldDuotone from "@iconify-icons/solar/dialog-2-bold-duotone";
import notebookBoldDuotone from "@iconify-icons/solar/notebook-bold-duotone";
import FileDropZone from "@/components/ChatFootTool/FileDropZone.vue";

const talkStore = useTalkStore();
const friendStore = useFriendStore();
const teamStore = useTeamStore();
const orgStore = useOrgStore();
const userLoading = ref(false); //组织架构人员添加加载状态
const activeTab = ref("1");
const handleTabClick = () => {
  talkStore.ifChat = false;
  talkStore.ifContact = false;
};

watchEffect(() => {
  if (talkStore.ifChat) activeTab.value = "1";
});

onMounted(async () => {
  try {
    // 并行执行初始化操作
    const promises = [];

    if (talkStore.filterWords.length == 0) {
      promises.push(talkStore.getFilterWords());
    }

    // 等待所有初始化操作完成
    await Promise.allSettled(promises);

    // 如果有活跃的聊天ID，设置聊天状态
    if (talkStore.activeChatId) {
      talkStore.ifChat = true;
    }
  } catch (error: any) {
    ElMessage.error(error.message);
  }
});

// 通讯录类型
const userType = ref(1);
const user: any = ref([]);
const friends: any = ref([]);
const handleNodeClick = async (code: string, type: number) => {
  talkStore.ifChat = false;
  talkStore.ifContact = true;
  userType.value = type;
  user.value = [];
  friends.value = [];
  if (type == 1) {
    userLoading.value = true;
    if (!orgStore.orgUser[code] || orgStore.orgUser[code].length == 0) {
      await orgStore.getOrgUser({ orgCode: code });
    }
    user.value = orgStore.orgUser[code];
    userLoading.value = false;
  } else if (type == 2) {
    if (code === "newFriend") {
      // friends.value = friendStore.friendList.filter((item: any) => item.status == "0");
      friends.value = friendStore.friendList;
    } else {
      friends.value = friendStore.friendList.filter((item: any) => item.status == "1" && item.groupId == code);
    }
  } else if (type == 3) {
    if (!teamStore.teamMember[code] || teamStore.teamMember[code].length == 0) {
      await teamStore.getTeamMember(code);
    }
    user.value = teamStore.teamMember[code];
  }
};

const groupId = ref("");
const handleGroupClick = async (id: string) => {
  talkStore.ifChat = false;
  talkStore.ifContact = true;
  userType.value = 4;
  groupId.value = id;
};
// 组件卸载前的清理工作
onBeforeUnmount(async () => {
  // 等待所有异步操作完成
  await nextTick();

  // 清理状态
  try {
    talkStore.ifChat = false;
    talkStore.ifContact = false;
  } catch (error: any) {
    ElMessage.error(error.message);
  }
});
</script>

<style lang="scss" scoped>
.chat-tab {
  @apply flex h-full w-64 flex-none flex-col border-r border-gray-200;

  :deep(.el-tabs) {
    // --el-tabs-header-height: 61px;
    @apply bg-white;

    .el-tabs__header {
      @apply m-0;
    }

    .el-tabs__content {
      @apply order-1;
    }

    .el-tabs__nav-wrap {
      @apply mb-0;

      &::after {
        height: 1px;
        @apply bg-gray-200;
      }
    }

    .el-tabs__nav {
      @apply w-full;

      .el-tabs__active-bar {
        height: 1px;
        @apply bg-blue-600;
      }
    }

    .el-tabs__item {
      @apply px-0 w-1/2;
    }

    .tab-item {
      @apply flex w-full h-full items-center justify-center p-3 text-gray-600 hover:text-gray-900;
    }

    .tab-item.active {
      @apply text-blue-600;
    }
  }
}

.dark {
  .chat-tab {
    @apply border-gray-700;

    :deep(.el-tabs) {
      @apply bg-gray-800;

      .el-tabs__nav-wrap::after {
        @apply bg-gray-700;
      }

      .el-tabs__active-bar {
        @apply bg-blue-500;
      }

      .tab-item {
        @apply text-gray-400 hover:text-white;
      }

      .tab-item.active {
        @apply text-blue-500;
      }
    }
  }
}
</style>
