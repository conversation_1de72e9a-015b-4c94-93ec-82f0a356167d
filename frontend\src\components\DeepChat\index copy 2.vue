<template>
  <div class="chat-container">
    <div ref="chatRef"></div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import "deep-chat";
// import "deep-chat/style.css";

const chatRef = ref<HTMLDivElement | null>(null);

onMounted(() => {
  if (chatRef.value) {
    const deepChat = document.createElement("deep-chat") as any;

    // 🔧 配置 API 请求
    // deepChat.request = {
    //   url: "https://api.openai.com/v1/chat/completions",
    //   method: "POST",
    //   headers: {
    //     Authorization: "Bearer YOUR_OPENAI_API_KEY",
    //     "Content-Type": "application/json"
    //   },
    //   body: {
    //     model: "gpt-3.5-turbo",
    //     messages: (messages: any) => messages
    //   }
    // };

    deepChat.request = {
      handler: (body: any) => {
        const lastUserMsg = body.messages.at(-1)?.content || "（空消息）";
        return new Promise(resolve => {
          // setTimeout(() => {
          resolve({
            role: "ai",
            text: `模拟响应：你说的是「${lastUserMsg}」`
          });
          // }, 800);
        });
      }
    };

    // 🎨 自定义消息样式
    deepChat.messageStyles = {
      user: {
        bubble: {
          backgroundColor: "#007bff",
          color: "#fff",
          borderRadius: "12px 12px 0 12px",
          fontSize: "14px",
          maxWidth: "80%"
        },
        avatar: {
          image: "https://i.pravatar.cc/40?img=3"
        }
      },
      ai: {
        bubble: {
          backgroundColor: "#f1f0f0",
          color: "#333",
          borderRadius: "12px 12px 12px 0",
          fontSize: "14px",
          maxWidth: "80%"
        },
        avatar: {
          image: "https://i.imgur.com/7k12EPD.png"
        }
      }
    };

    // ✅ 添加组件到页面
    chatRef.value.appendChild(deepChat);
  }
});
</script>

<style scoped>
.chat-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}
</style>
