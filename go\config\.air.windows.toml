root = "."
tmp_dir = "tmp"

[build]
# 编译使用的shell命令
cmd = "go build -tags=fts5 -o ./tmp/goapp.exe ./main.go"

# 由`cmd`命令得到的二进制文件名
bin = "./tmp/goapp.exe --basedir=../ --env=dev --port=7003"
# 在运行二进制文件时添加额外的参数 (bin/full_bin)。将运行“./tmp/main hello world”
# args_bin = ["hello", "world"]

# 如果文件更改过于频繁，则没有必要在每次更改时都触发构建。可以设置触发构建的延迟时间/毫秒
delay = 1000

# 忽略（不监听）文件的扩展名或目录
exclude_dir = ["tmp", "public"]

# 监听指定目录的文件
# include_dir = []

# 监听扩展名的文件
include_ext = ["go", "mod", "sum", "json", "tpl", "tmpl", "html"]

# 忽略（不监听）指定文件
exclude_file = []

# 忽略符合通过正则匹配到的文件
exclude_regex = []

# 忽略未进行修改的文件
exclude_unchanged = true

# 按照目录的符号链接
follow_symlink = false

# 杀死进程前发送中断信号(Windows不支持)
send_interrupt = true
# 发送中断信号后延迟时间/毫秒
kill_delay = 2000

# 发生构建错误时，停止运行旧的二进制文件
stop_on_error = true

# 这个日志文件放在你的`tmp_dir`中
log = "air.log"

[log]
# 显示日志时间
time = true

[color]
# 自定义每个部分的颜色。如果未找到颜色，请使用原始应用程序日志。
main = "magenta"
watcher = "cyan"
build = "yellow"
runner = "green"

[misc]
# 退出时删除 tmp 目录
clean_on_exit = true