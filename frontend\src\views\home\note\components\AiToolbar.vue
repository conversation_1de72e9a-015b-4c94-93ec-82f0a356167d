<template>
  <div class="ai-toolbar" v-if="visible" :class="{ 'loading': loading }">
    <div class="ai-toolbar-header">
      <span class="toolbar-title">AI助手</span>
      <div class="toolbar-actions">
        <span class="selected-text-info" v-if="selectedText">
          已选中 {{ selectedText.length }} 个字符
        </span>
        <button @click="$emit('close')" class="close-btn" title="关闭">
          <font-awesome-icon icon="times" />
        </button>
      </div>
    </div>

    <div class="ai-toolbar-content">
      <!-- AI工具按钮组 -->
      <div class="ai-tools">
        <button
          @click="handleAiAction('polish')"
          class="ai-tool-btn primary"
          title="润色文本 - 让文字更流畅专业"
          :disabled="loading"
        >
          <font-awesome-icon icon="magic-wand-sparkles" />
          <span>润色</span>
        </button>

        <button
          @click="handleAiAction('translate')"
          class="ai-tool-btn"
          title="翻译文本 - 中英文互译"
          :disabled="loading"
        >
          <font-awesome-icon icon="language" />
          <span>翻译</span>
        </button>

        <button
          @click="handleAiAction('summarize')"
          class="ai-tool-btn"
          title="总结文本 - 提取关键信息"
          :disabled="loading"
        >
          <font-awesome-icon icon="compress" />
          <span>总结</span>
        </button>

        <button
          @click="handleAiAction('expand')"
          class="ai-tool-btn"
          title="扩展文本 - 添加更多细节"
          :disabled="loading"
        >
          <font-awesome-icon icon="expand" />
          <span>扩展</span>
        </button>

        <button
          @click="handleAiAction('check')"
          class="ai-tool-btn"
          title="检查错误 - 语法和拼写检查"
          :disabled="loading"
        >
          <font-awesome-icon icon="magnifying-glass" />
          <span>检查</span>
        </button>

        <button
          @click="handleAiAction('custom')"
          class="ai-tool-btn secondary"
          title="自定义处理 - 输入自定义指令"
          :disabled="loading"
        >
          <font-awesome-icon icon="robot" />
          <span>自定义</span>
        </button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="ai-loading">
        <font-awesome-icon icon="spinner" spin />
        <span>AI正在处理中，请稍候...</span>
      </div>
    </div>
    
    <!-- 自定义提示输入框 -->
    <div v-if="showCustomInput" class="custom-input-container">
      <div class="custom-input-header">
        <span>自定义AI处理</span>
        <button @click="closeCustomInput" class="close-btn">
          <font-awesome-icon icon="times" />
        </button>
      </div>
      <textarea
        v-model="customPrompt"
        placeholder="请输入您希望AI如何处理选中的文本..."
        class="custom-input"
        rows="3"
        ref="customInputRef"
      ></textarea>
      <div class="custom-input-actions">
        <button @click="closeCustomInput" class="cancel-btn">取消</button>
        <button @click="executeCustomAction" class="execute-btn" :disabled="!customPrompt.trim()">
          执行
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';

// Props
interface Props {
  visible: boolean;
  selectedText: string;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// Emits
interface Emits {
  (e: 'ai-action', action: string, prompt?: string): void;
  (e: 'close'): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const showCustomInput = ref(false);
const customPrompt = ref('');
const customInputRef = ref<HTMLTextAreaElement>();

// AI动作映射
const aiActionPrompts: Record<string, string> = {
  polish: '请润色以下文本，使其更加流畅、专业和易读：',
  translate: '请将以下文本翻译成中文（如果是中文则翻译成英文）：',
  summarize: '请总结以下文本的主要内容：',
  expand: '请扩展以下文本，添加更多细节和说明：',
  check: '请检查以下文本中的语法、拼写和逻辑错误：'
};

// 处理AI动作
const handleAiAction = async (action: string) => {
  if (action === 'custom') {
    showCustomInput.value = true;
    await nextTick();
    customInputRef.value?.focus();
    return;
  }
  
  const prompt = aiActionPrompts[action];
  if (prompt) {
    emit('ai-action', action, prompt);
  }
};

// 执行自定义动作
const executeCustomAction = () => {
  if (customPrompt.value.trim()) {
    emit('ai-action', 'custom', customPrompt.value.trim());
    closeCustomInput();
  }
};

// 关闭自定义输入
const closeCustomInput = () => {
  showCustomInput.value = false;
  customPrompt.value = '';
};

// 监听ESC键关闭
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    if (showCustomInput.value) {
      closeCustomInput();
    } else {
      emit('close');
    }
  }
};

// 挂载时添加键盘监听
import { onMounted, onUnmounted } from 'vue';

onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped>
.ai-toolbar {
  position: fixed;
  z-index: 1000;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  min-width: 320px;
  max-width: 400px;
  overflow: hidden;
  transition: all 0.3s ease;

  &.loading {
    pointer-events: none;
    opacity: 0.8;
  }
}

.ai-toolbar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e5e7eb;

  .toolbar-title {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
  }

  .toolbar-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .selected-text-info {
    font-size: 12px;
    color: #64748b;
    background: rgba(59, 130, 246, 0.1);
    padding: 2px 8px;
    border-radius: 4px;
  }

  .close-btn {
    padding: 4px;
    border: none;
    background: none;
    color: #64748b;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      background: rgba(239, 68, 68, 0.1);
      color: #ef4444;
    }
  }
}

.ai-toolbar-content {
  padding: 16px;
}

.ai-tools {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.ai-tool-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #ffffff;
  color: #374151;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-height: 44px;

  &:hover:not(:disabled) {
    background: #f8fafc;
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.1);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  &.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border-color: #3b82f6;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      border-color: #2563eb;
      color: white;
    }
  }

  &.secondary {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    border-color: #8b5cf6;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
      border-color: #7c3aed;
      color: white;
    }
  }

  svg {
    font-size: 14px;
    flex-shrink: 0;
  }

  span {
    font-size: 12px;
  }
}

.ai-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  margin-top: 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
  color: #0369a1;
  font-size: 13px;
  font-weight: 500;

  svg {
    color: #3b82f6;
    font-size: 16px;
  }
}

.custom-input-container {
  border-top: 1px solid #e5e7eb;
  padding: 16px;
  margin-top: 12px;
  background: #f8fafc;
}

.custom-input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  span {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
  }
}

.close-btn {
  padding: 4px;
  border: none;
  background: none;
  color: #64748b;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }
}

.custom-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 13px;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
  background: white;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
  }
}

.custom-input-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
}

.cancel-btn, .execute-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #e5e7eb;

  &:hover {
    background: #e5e7eb;
    border-color: #d1d5db;
  }
}

.execute-btn {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: 1px solid #8b5cf6;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    border-color: #7c3aed;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(139, 92, 246, 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}
</style>
