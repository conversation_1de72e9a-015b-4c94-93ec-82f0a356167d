<template>
  <div class="relative group bg-gray-700 text-white px-4 py-2 rounded-lg max-w-md">
    <!-- Markdown 渲染内容 -->
    <div v-html="rendered" />

    <!-- 复制按钮（悬浮显示）-->
    <button
      class="absolute top-1 right-1 text-sm text-gray-400 hover:text-white hidden group-hover:block"
      @click="copyToClipboard"
    >
      📋
    </button>
  </div>
</template>
<script setup lang="ts">
import { computed } from "vue";
// import MarkdownIt from "markdown-it";
import { ElMessage } from "element-plus";

const props = defineProps<{ content: string }>();
// const md = new MarkdownIt();
// const rendered = computed(() => md.render(props.content));

function copyToClipboard() {
  navigator.clipboard
    .writeText(props.content)
    .then(() => {
      ElMessage.success("复制成功！");
    })
    .catch(() => {
      ElMessage.error("复制失败");
    });
}
</script>
