<template>
  <div class="widget-container h-full">
    <!-- 悬浮标题和按钮容器 -->
    <div class="absolute top-0 left-4 -translate-y-1/2 flex items-center gap-2 z-10">
      <!-- 小组件标题 -->
      <div
        class="widget-title bg-white dark:bg-gray-800 px-3 py-1.5 rounded-full shadow-md border border-gray-200 dark:border-gray-700 flex items-center gap-2"
      >
        <Icon :icon="clockCircleBoldDuotone" class="text-lg text-blue-500" />
        <span class="text-xs font-bold text-gray-700 dark:text-gray-200">倒数日</span>
      </div>
      <!-- 添加倒数日按钮 -->
      <button
        @click="showAddForm = !showAddForm"
        class="w-7 h-7 rounded-full bg-blue-500 text-white flex items-center justify-center hover:bg-blue-600 transition-colors shadow-md"
        title="添加倒数日"
      >
        <font-awesome-icon :icon="['fas', showAddForm ? 'times' : 'plus']" class="w-5 h-5" />
      </button>
    </div>

    <div class="flex flex-col h-full relative p-4 pt-8">
      <!-- 时钟区域 -->
      <div class="flex-shrink-0 text-center relative z-50">
        <!-- 数字时钟 -->
        <div class="flex justify-center items-end">
          <div ref="hoursRef" class="tick">
            <div data-repeat="true" aria-hidden="true">
              <span data-view="flip"></span>
            </div>
          </div>
          <div class="tick-colon-container">
            <span class="tick-colon">:</span>
          </div>
          <div ref="minutesRef" class="tick">
            <div data-repeat="true" aria-hidden="true">
              <span data-view="flip"></span>
            </div>
          </div>
        </div>

        <!-- 日期信息 -->
        <div class="text-center mt-3">
          <div class="text-base opacity-90 mb-1 font-bold">{{ currentDate }} {{ currentDay }}</div>
        </div>
      </div>

      <!-- 装饰性分割线 -->
      <div class="h-px bg-gradient-to-r from-transparent via-white via-50% to-transparent opacity-30 mb-3"></div>

      <!-- 倒数日区域 -->
      <div class="flex-1 relative z-50 flex flex-col overflow-hidden">
        <!-- 添加倒数日表单 -->
        <div
          v-if="showAddForm"
          class="bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm shadow-lg rounded-lg border border-gray-200/60 dark:border-gray-700/60 p-3 mb-3"
        >
          <!-- 表单内容 -->
          <div>
            <form @submit.prevent="addCountdown" class="space-y-3">
              <!-- 事件名称 -->
              <div>
                <input
                  v-model="newCountdown.name"
                  placeholder="请输入事件名称"
                  class="w-full bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded px-2 py-1.5 text-xs text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  maxlength="10"
                />
              </div>

              <!-- 目标日期 -->
              <div>
                <input
                  ref="dateInput"
                  v-model="newCountdown.date"
                  type="date"
                  class="w-full bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded px-2 py-1.5 text-xs text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:color-scheme-dark"
                  @click="openDatePicker"
                />
              </div>
            </form>
          </div>

          <!-- 按钮组 -->
          <div class="flex gap-2 flex-shrink-0 mt-3">
            <button
              @click="addCountdown"
              :disabled="!newCountdown.name || !newCountdown.date"
              class="flex-1 bg-blue-600 hover:bg-blue-700 text-white rounded px-3 py-1.5 text-xs font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              添加
            </button>
            <button
              @click="cancelAdd"
              class="flex-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 rounded px-3 py-1.5 text-xs font-medium transition-colors"
            >
              关闭
            </button>
          </div>
        </div>

        <!-- 倒数日列表容器 -->
        <div v-if="!showAddForm" class="flex-1 flex flex-col min-h-0">
          <!-- 倒数日列表 -->
          <div
            v-if="countdowns.length > 0"
            class="space-y-2 overflow-y-auto flex-1 stable-scrollbar relative bg-white/40 dark:bg-gray-900/40 backdrop-blur-sm rounded-lg border border-gray-200/60 dark:border-gray-700/60 p-3"
          >
            <!-- 倒数日列表项 -->
            <div
              v-for="countdown in countdowns"
              :key="countdown.id"
              class="group flex items-center justify-between bg-white/60 dark:bg-slate-800/70 rounded-lg p-1.5 border border-gray-200 dark:border-slate-700 transition-all hover:shadow shadow-sm"
            >
              <!-- 左侧：事件名称 -->
              <span class="text-sm font-medium text-gray-700 dark:text-gray-200 truncate pr-2">
                {{ countdown.name }}
              </span>

              <!-- 右侧：时间和删除按钮 -->
              <div class="flex items-center gap-2 flex-shrink-0">
                <!-- 时间徽章 -->
                <div
                  class="font-bold rounded-md px-2.5 py-1 flex items-baseline gap-1"
                  :class="[
                    countdown.days >= 0
                      ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-200'
                      : 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-200'
                  ]"
                >
                  <span class="text-base leading-none font-bold">{{ Math.abs(countdown.days) }}</span>
                  <span class="text-xs leading-none">{{ countdown.days >= 0 ? "天后" : "天前" }}</span>
                </div>

                <!-- 删除按钮 (hover显示) -->
                <button
                  @click="removeCountdown(countdown.id)"
                  class="w-5 h-5 rounded-md bg-gray-200 hover:bg-red-500 dark:bg-slate-700 dark:hover:bg-red-500 flex items-center justify-center text-gray-500 hover:text-white dark:text-gray-400 dark:hover:text-white text-xs opacity-0 group-hover:opacity-100 transition-all"
                >
                  ×
                </button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div
            v-else
            class="flex-1 flex items-center justify-center bg-white/40 dark:bg-gray-900/40 backdrop-blur-sm rounded-lg border border-gray-200/60 dark:border-gray-700/60 p-3"
          >
            <div class="text-center text-gray-400 dark:text-gray-500">
              <div class="text-sm opacity-60">暂无倒数日</div>
              <div class="text-sm opacity-40 mt-1">点击 + 添加重要日期</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, shallowRef, watch } from "vue";
import { Icon } from "@iconify/vue";
import Tick from "@pqina/flip";
import "@pqina/flip/dist/flip.min.css";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn"; // 导入中文语言包
import { ElMessage } from "element-plus";
import {
  getCountdownList,
  addCountdown as apiAddCountdown,
  deleteCountdown as apiDeleteCountdown,
  type CountdownItem
} from "@/api/modules/countdown";
import clockCircleBoldDuotone from "@iconify-icons/solar/clock-circle-bold-duotone";
// Font Awesome icons are globally registered

dayjs.locale("zh-cn"); // 全局设置 dayjs 语言为中文

interface Countdown {
  id: string;
  name: string;
  date: string;
  days: number;
}

const hoursRef = ref<HTMLDivElement | null>(null);
const minutesRef = ref<HTMLDivElement | null>(null);
let hoursTick: any = null;
let minutesTick: any = null;

const currentDate = ref("");
const currentDay = ref("");
const showAddForm = ref(false);
const newCountdown = ref({
  name: "",
  date: ""
});
const dateInput = ref<HTMLInputElement | null>(null);

// 监听事件名称长度
watch(
  () => newCountdown.value.name,
  newValue => {
    if (newValue && newValue.length > 10) {
      newCountdown.value.name = newValue.substring(0, 10);
      ElMessage.warning("事件名称最长为 10 个字符");
    }
  }
);

// 使用shallowRef优化性能，减少深度响应式开销
const countdowns = shallowRef<Countdown[]>([]);

let animationFrameId: number | null = null;
let lastUpdateMinute = -1;
let isPageVisible = true;

const updateTime = () => {
  // 页面不可见时，由requestAnimationFrame自动处理，无需手动检查
  const now = dayjs(); // 应用中文语言包

  const currentMinute = now.minute();
  if (lastUpdateMinute !== currentMinute) {
    // 分钟变化时，更新所有其他信息
    currentDate.value = now.format("YYYY年MM月DD日");
    currentDay.value = now.format("dddd");
    lastUpdateMinute = currentMinute;

    // 每天零点更新倒数日
    if (now.hour() === 0 && now.minute() === 0) {
      updateCountdownDays();
    }
  }

  // 请求下一帧
  animationFrameId = requestAnimationFrame(updateTime);
};

const initClock = () => {
  if (!hoursRef.value || !minutesRef.value) return;

  hoursTick = Tick.DOM.create(hoursRef.value, {
    value: dayjs().format("HH"),
    credits: false
  });
  minutesTick = Tick.DOM.create(minutesRef.value, {
    value: dayjs().format("mm"),
    credits: false
  });

  // 每秒更新一次时钟
  const interval = setInterval(() => {
    if (hoursTick) {
      hoursTick.value = dayjs().format("HH");
    }
    if (minutesTick) {
      minutesTick.value = dayjs().format("mm");
    }
  }, 1000);

  onUnmounted(() => {
    clearInterval(interval);
    if (hoursTick) {
      Tick.DOM.destroy(hoursRef.value);
    }
    if (minutesTick) {
      Tick.DOM.destroy(minutesRef.value);
    }
  });
};

// 页面可见性检测
const handleVisibilityChange = () => {
  isPageVisible = !document.hidden;
  if (isPageVisible) {
    // 页面重新可见时，如果动画帧停止了，则重新启动
    if (!animationFrameId) {
      updateTime();
    }
  } else {
    // 页面不可见时，取消动画帧以节省资源
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
  }
};

const updateCountdownDays = () => {
  const now = dayjs();
  const newCountdowns = countdowns.value.map(countdown => {
    const targetDate = dayjs(countdown.date);
    const diffDays = targetDate.diff(now, "day");
    return { ...countdown, days: diffDays };
  });

  // 使用shallowRef需要重新赋值来触发更新
  countdowns.value = newCountdowns;
};

const addCountdown = async () => {
  if (countdowns.value.length >= 6) {
    ElMessage.warning("倒数日已满，请删除后添加");
    return;
  }

  if (newCountdown.value.name && newCountdown.value.date) {
    try {
      await apiAddCountdown({
        name: newCountdown.value.name,
        date: newCountdown.value.date
      });
      ElMessage.success("添加成功");
      // 重新加载列表以获取新数据
      loadCountdowns();

      // 重置表单
      newCountdown.value = { name: "", date: "" };
      showAddForm.value = false;
    } catch (error) {
      console.error("添加倒计时失败:", error);
      ElMessage.error("添加失败，请稍后重试");
    }
  }
};

const removeCountdown = async (id: string) => {
  try {
    await apiDeleteCountdown(id);
    // 直接从本地数组中移除，避免重新请求，优化体验
    countdowns.value = countdowns.value.filter(countdown => countdown.id !== id);
    ElMessage.success("删除成功");
  } catch (error) {
    console.error("删除倒计时失败:", error);
    ElMessage.error("删除失败，请稍后重试");
  }
};

const cancelAdd = () => {
  showAddForm.value = false;
  newCountdown.value = { name: "", date: "" };
};

const openDatePicker = () => {
  // 触发原生日期选择器
  dateInput.value?.showPicker();
};

const loadCountdowns = async () => {
  try {
    const res = await getCountdownList();
    if (res && res.list) {
      const now = dayjs();
      const formattedCountdowns = res.list.map(item => ({
        ...item,
        days: dayjs(item.date).diff(now, "day")
      }));
      countdowns.value = formattedCountdowns;
    }
  } catch (error) {
    console.error("加载倒计时列表失败:", error);
    ElMessage.error("加载倒计时列表失败");
  }
};

onMounted(() => {
  // 初始化时间显示
  const now = dayjs();
  currentDate.value = now.format("YYYY年MM月DD日");
  currentDay.value = now.format("dddd");
  lastUpdateMinute = now.minute();

  loadCountdowns();
  initClock();

  // 启动动画循环
  animationFrameId = requestAnimationFrame(updateTime);

  // 监听页面可见性变化
  document.addEventListener("visibilitychange", handleVisibilityChange);
});

onUnmounted(() => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
  document.removeEventListener("visibilitychange", handleVisibilityChange);
});
</script>

<style scoped lang="scss">
.widget-container {
  overflow: visible;
}

:global(.dark) .dark\:color-scheme-dark {
  color-scheme: dark;
}

/* Flip Clock Styles */
.tick {
  font-size: 2.8rem; /* 调整字体大小以适应容器 */
  line-height: 1;
  font-family: "Arial", sans-serif;
  text-align: center;
  white-space: nowrap;
}

.tick-colon-container {
  font-size: 1.8rem;
  line-height: 1;
  font-family: "Arial", sans-serif;
  padding: 0 0.1em;
  color: #334155; /* slate-700 */
}

.tick-colon {
  animation: tick-blink 1s step-end infinite;
}

@keyframes tick-blink {
  50% {
    opacity: 0;
  }
}

:global(.dark) .tick-colon-container {
  color: #f1f5f9;
}

:global(.dark) .tick {
  .tick-flip-panel {
    color: #f1f5f9;
    background-color: #1e293b;
  }
  .tick-flip-card {
    box-shadow: 0 1px 0 #1e293b;
  }
}

/* 移除自定义滚动条样式 */

/* 过渡动画 */
.slide-fade-enter-active {
  transition: all 0.2s ease-out;
}
.slide-fade-leave-active {
  transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}

/* Custom scrollbar that prevents layout shift and appears on hover */
.stable-scrollbar {
  scrollbar-gutter: stable;
}

.stable-scrollbar::-webkit-scrollbar {
  width: 6px;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 10;
}

.stable-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.stable-scrollbar::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 3px;
  transition: background-color 0.2s ease-in-out;
}

.stable-scrollbar:hover::-webkit-scrollbar-thumb {
  background-color: rgba(209, 213, 219, 0.8); /* gray-300 with opacity */
}

:global(.dark) .stable-scrollbar:hover::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.8); /* gray-600 with opacity */
}

.list-fade-enter-active,
.list-fade-leave-active {
  transition: opacity 0.2s ease;
}

.list-fade-enter-from,
.list-fade-leave-to {
  opacity: 0;
}
</style>
