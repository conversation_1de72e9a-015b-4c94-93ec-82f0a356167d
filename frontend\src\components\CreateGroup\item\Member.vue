<template>
  <div>
    <div class="flex justify-between member">
      <el-tabs v-model="activeTab" class="custom-tabs">
        <el-tab-pane label="组织架构" name="1">
          <el-scrollbar height="240">
            <OrgTree :group-scope="groupInfo.groupScope" @node-click="handleClick" />
          </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="我的好友" name="2">
          <el-scrollbar height="240">
            <FriendTree @node-click="handleClick" />
          </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="我的团队" name="3">
          <el-scrollbar height="240">
            <TeamTree @node-click="handleClick" />
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
      <el-transfer
        class="custom-transfer"
        v-model="transferValue"
        filterable
        filter-placeholder="搜索成员"
        :titles="['可选择人员', '已选择人员']"
        :button-texts="['移除', '添加']"
        :props="transferProps"
        :data="transferData"
      >
        <template #default="{ option }">
          <span>{{ option.name }}</span>
        </template>
      </el-transfer>
    </div>
    <div class="absolute bottom-0 right-0">
      <el-button type="default" @click="prev">上一步</el-button>
      <el-button type="primary" @click="chooseMember">下一步</el-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="Member">
import { ref } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import { useFriendStore } from "@/stores/modules/friends";
import { ElMessage } from "element-plus";
import OrgTree from "@/components/tree/org-tree.vue";
import FriendTree from "@/components/tree/friend-tree.vue";
import TeamTree from "@/components/tree/team-tree.vue";
import { getTeamMember } from "@/api/modules/team";
import { useOrgStore } from "@/stores/modules/org";

const userStore = useUserStore();
const talkStore = useTalkStore();
const friendStore = useFriendStore();
const orgStore = useOrgStore();

const groupInfo: any = {
  ...talkStore.createGroup,
  members: []
};

const activeTab = ref("1");

const transferData = ref<any>([]);
const transferValue = ref<any>([]);
// const transferData = ref<any>([userStore.userInfo]);
// const transferValue = ref<any>([userStore.userId]);
const transferProps = {
  key: "id",
  label: "name"
};

const handleClick = async (code: string) => {
  let newList: any[] = [];

  if (activeTab.value == "1") {
    if (!orgStore.orgUser[code] || orgStore.orgUser[code].length == 0) {
      await orgStore.getOrgUser({ orgCode: code });
    }
    newList = orgStore.orgUser[code];
  } else if (activeTab.value == "2") {
    newList = friendStore.friendList.filter((item: any) => item.status == "1" && item.groupId == code);
  } else {
    const res: any = await getTeamMember(code);
    if (res.code == 0) newList = res.data;
  }

  // 过滤当前 tab 的数据
  newList = newList.filter((item: any) => item.secretLevel >= groupInfo.secret && item.id != userStore.userId);

  // 找出 transferValue 中的选中项，不在 newList 中的（来自旧数据）
  const selectedSet = new Set(transferValue.value);
  const preservedItems = transferData.value.filter(
    (item: any) => selectedSet.has(item.id) && !newList.some(n => n.id === item.id)
  );

  // 合并新数据和已选中的旧数据
  transferData.value = [...newList, ...preservedItems];
};

const emit = defineEmits(["prev-step", "next-step"]);

const prev = () => {
  emit("prev-step");
};

const chooseMember = () => {
  if (transferValue.value && transferValue.value.length >= 2) {
    const arr = transferData.value.filter((item: any) => transferValue.value.includes(item.id));
    groupInfo.members = arr;
    talkStore.setNewGroup(groupInfo);
    emit("next-step");
  } else {
    ElMessage.error("群组成员数量不能小于2");
  }
};
</script>

<style lang="scss" scoped></style>
