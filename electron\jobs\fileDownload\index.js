const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const http = require('http');
const https = require('https');
const Ps = require('ee-core/ps');
const Log = require("ee-core/log");

const tempDir = path.join(Ps.getExecDir, 'temp');

class FileDownloader {
    /**
     * 构造函数，初始化文件下载器实例。
     * 创建临时目录用于存储下载的文件，如果目录不存在则递归创建。
     * Ps.getExecDir - 软件的安装路径，用于确定临时目录的位置和其他文件操作的基准路径。
     */
    constructor() {
        // 将传入的安装路径赋值给实例属性
        this.installPath = Ps.getExecDir;

        // 检查临时目录是否存在，如果不存在则递归创建该目录
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
    }

    // 从 URL 中获得文件名
    getFileNameFromUrl(url) {
        return path.basename(url);
    }

    // 计算已存在文件的 MD5 值
    computeFileMd5(filePath) {
        return new Promise((resolve, reject) => {
            const hash = crypto.createHash('md5');
            const stream = fs.createReadStream(filePath);
            stream.on('error', err => reject(err));
            stream.on('data', chunk => hash.update(chunk));
            stream.on('end', () => resolve(hash.digest('hex')));
        });
    }

    // 根据 URL 下载文件到目标路径
    download(url) {
        return new Promise((resolve, reject) => {
            const file = fs.createWriteStream(tempDir);
            const protocol = url.startsWith('https') ? https : http;
            protocol.get(url, response => {
                if (response.statusCode !== 200) {
                     // 下载失败时删除文件（如果存在）
                    if (Ps.existsSync(tempDir)) {
                        Ps.removeSync(tempDir);
                    }               
                    return reject(new Error(`下载失败，状态码：${response.statusCode}`));
                }
                response.pipe(file);
                file.on('finish', () => {
                    file.close(() => resolve(tempDir)); // 完成下载后关闭文件流
                });
            }).on('error', err => {
                fs.unlink(tempDir, () => {}); // 异步删除文件
                reject(err);
            });
        });
    }

    // 对比现有文件与目标 MD5，不匹配则下载并替换
    async updateFile(url, expectedMd5) {
        const fileName = this.getFileNameFromUrl(url);
        const filePath = path.join(this.tempDir, fileName);
        let needUpdate = true;

        if (fs.existsSync(filePath)) {
            try {
                const currentMd5 = await this.computeFileMd5(filePath);
                if (currentMd5 === expectedMd5) {
                    needUpdate = false;
                } else {
                    
                }
            } catch (err) {
                Log.error('计算 MD5 时出错，准备更新文件：', err);
            }
        }

        if (needUpdate) {
            try {
                await this.download(url, filePath);
                const newMd5 = await this.computeFileMd5(filePath);
                if (newMd5 !== expectedMd5) {
                    throw new Error('下载后文件的 MD5 校验失败');
                }
            } catch (err) {
                Log.error('文件更新失败：', err);
                throw err;
            }
        }
    }

     /**
     * 替换软件安装目录下的 resouse/asar.zip 文件：
     * 1. 备份原 asar.zip 文件（若存在）；
     * 2. 尝试用 sourceFile 覆盖 asar.zip；
     * 3. 如果替换失败，则用备份文件回退；
     * 4. 替换成功后，删除 temp 目录。
     * @param {string} sourceFile - 下载成功后的新文件路径
     */
     async replaceAsar(sourceFile) {
        return new Promise((resolve, reject) => {
            const asarPath = path.join(this.installPath, 'resouse', 'asar.zip');
            const backupPath = asarPath + '.bak';
            try {
                // 备份原 asar.zip 文件（如果存在）
                if (fs.existsSync(asarPath)) {
                    fs.copyFileSync(asarPath, backupPath);
                }
                // 尝试替换 asar.zip 文件
                fs.copyFileSync(sourceFile, asarPath);

                // 删除备份文件
                if (fs.existsSync(backupPath)) {
                    fs.unlinkSync(backupPath);
                }

                // 删除 temp 目录
                fs.rmdirSync(this.tempDir, { recursive: true });
                resolve();
            } catch (err) {
                Log.error('asar 文件替换失败，开始回退：', err);
                // 尝试回退：恢复备份文件
                try {
                    if (fs.existsSync(backupPath)) {
                        fs.copyFileSync(backupPath, asarPath);
                        fs.unlinkSync(backupPath);
                    }
                } catch (rollbackErr) {
                    Log.error('asar 文件回退失败：', rollbackErr);
                }
                reject(err);
            }
        });
    }

     /**
     * 利用 electron-updater 的 autoUpdater 检查更新及获取更新内容，
     * 若检测到更新则下载更新文件并调用更新流程：
     * 1. 检查更新；
     * 2. 在 update-available 事件中获取更新信息（例如 download URL 和 checksum）；
     * 3. 调用 updateFile 下载文件，然后调用 replaceAsar 进行替换。
     * 注意：updateInfo 中的字段名需与更新服务器返回的数据匹配。
     */
     async checkAndUpdateWithAutoUpdater() {
        return new Promise((resolve, reject) => {
            // 主动检查更新
            autoUpdater.checkForUpdates();

            autoUpdater.on('update-available', async (updateInfo) => {
                // 假设 updateInfo 中有 updateFileUrl 与 checksum 字段
                const updateUrl = updateInfo.updateFileUrl;
                const expectedMd5 = updateInfo.checksum;
                if (!updateUrl || !expectedMd5) {
                    return reject(new Error('Update information is incomplete.'));
                }

                try {
                    // 下载更新文件并校验
                    await this.updateFile(updateUrl, expectedMd5);
                    // 替换 asar.zip 文件
                    const sourceFile = Ps.join(this.tempDir, this.getFileNameFromUrl(updateUrl));
                    await this.replaceAsar(sourceFile);
                    resolve();
                } catch (err) {
                    reject(err);
                }
            });

            autoUpdater.on('error', (err) => {
                reject(err);
            });
        });
    }
}

module.exports = FileDownloader;