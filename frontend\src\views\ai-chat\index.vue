<template>
  <div class="ai-chat-container" :style="themeStyles">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="header-content">
        <Icon :icon="sparklesIcon" width="24" height="24" class="header-icon" />
        <h2 class="header-title">AI微应用</h2>
      </div>
      <button @click="closeWindow" class="close-btn">
        <Icon :icon="closeIcon" width="16" height="16" />
      </button>
    </div>

    <!-- 消息显示区域 -->
    <div class="messages-container" ref="messagesContainer">
      <div class="messages-list">
        <div v-if="messages.length === 0" class="welcome-message">
          <Icon :icon="sparklesIcon" width="48" height="48" class="welcome-icon" />
          <p class="welcome-text" v-if="hasConfigData">
            您好！我是{{ appConfigData?.appInfo.name || 'AI助手' }}，有什么可以帮助您的吗？
          </p>
          <p class="welcome-text" v-else>
            正在加载微应用配置...
          </p>
        </div>
        
        <div
          v-for="message in messages"
          :key="message.id"
          class="message-item"
          :class="{ 'user-message': message.role === 'user', 'ai-message': message.role === 'ai' }"
        >
          <div class="message-avatar">
            <Icon
              :icon="message.role === 'user' ? userIcon : sparklesIcon"
              width="20"
              height="20"
            />
          </div>
          <div class="message-content">
            <div class="message-bubble">
              <p class="message-text">
                <!-- 用户消息使用MessageRenderer渲染 -->
                <template v-if="message.role === 'user'">
                  <MessageRenderer
                    :content="message.content"
                    :message-type="message.messageType || 'text'"
                    :files="message.files || []"
                    :is-streaming="false"
                  />
                </template>
                <!-- AI消息显示逻辑 -->
                <template v-else>
                  <!-- 正在生成中的状态 -->
                  <div v-if="message.status === 'generating'">
                    <!-- 等待响应时显示加载动画 -->
                    <div v-if="!message.content" class="ai-loading-container">
                      <div class="thinking-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                      <span class="thinking-text">正在思考...</span>
                    </div>
                    <!-- 流式响应时显示内容，无光标，平滑过渡 -->
                    <div v-else class="ai-streaming-content">
                      <MessageRenderer
                        :content="message.content"
                        :is-streaming="true"
                      />
                    </div>
                  </div>
                  <!-- 已完成状态显示最终内容 -->
                  <div v-else class="ai-final-content">
                    <MessageRenderer 
                      :content="message.content" 
                      :is-streaming="false"
                    />
                  </div>
                </template>
              </p>
            </div>

            <!-- AI消息操作按钮 -->
            <div v-if="message.role === 'ai'" class="message-actions">
              <!-- 停止生成按钮 -->
              <button
                v-if="message.status === 'generating' && isGenerating"
                @click="stopGeneration"
                class="action-btn stop-btn"
                title="停止生成"
              >
                <Icon icon="mdi:stop" width="14" height="14" />
                <span>停止</span>
              </button>

              <!-- 重新生成按钮 -->
              <button
                v-if="message.canRegenerate && !isGenerating"
                @click="regenerateResponse(message.id)"
                class="action-btn regenerate-btn"
                title="重新生成"
              >
                <Icon icon="mdi:refresh" width="14" height="14" />
                <span>重新生成</span>
              </button>
            </div>

            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>


      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-container">
      <!-- 无配置数据提示 -->
      <div v-if="!hasConfigData" class="no-config-notice">
        <Icon :icon="sparklesIcon" width="20" height="20" class="notice-icon" />
        <span class="notice-text">加载AI微应用中...</span>
      </div>

      <!-- 一体化输入区域 -->
      <div class="unified-input-area" :class="{ 'has-config': hasConfigData && isConfigMode, 'disabled': !hasConfigData }">
        <!-- 配置标签区域 -->
        <div v-if="hasConfigData && isConfigMode" class="config-tags-area">
          <div class="config-header">
            <Icon :icon="sparklesIcon" width="16" height="16" class="config-icon" />
            <span class="config-title">{{ appConfigData?.appInfo.name }}</span>
            <button @click="resetConfig" class="reset-btn">
              <Icon :icon="refreshIcon" width="14" height="14" />
            </button>
          </div>

          <!-- 配置标签 -->
          <div class="config-tags">
            <!-- 选择类型字段显示为标签 -->
            <div
              v-for="field in selectFields"
              :key="field.name"
              class="config-tag-group"
            >
              <span class="tag-label">{{ field.label }}</span>
              <div class="tag-options">
                <button
                  v-for="option in field.options"
                  :key="option.value"
                  @click="updateConfigField(field.name, option.value)"
                  class="tag-option"
                  :class="{ active: field.value === option.value }"
                >
                  {{ option.label }}
                </button>
              </div>
            </div>

            <!-- 数值类型字段显示为滑块 -->
            <div
              v-for="field in rangeFields"
              :key="field.name"
              class="config-range-group"
            >
              <span class="range-label">{{ field.label }}</span>
              <div class="range-control">
                <input
                  v-model.number="field.value"
                  type="range"
                  class="range-slider"
                  :min="field.min || 0"
                  :max="field.max || 1"
                  :step="field.step || 0.1"
                  @input="updateConfigField(field.name, Number(($event.target as HTMLInputElement)?.value || 0))"
                />
                <span class="range-value">{{ field.value }}</span>
              </div>
            </div>

            <!-- 文件上传字段 -->
            <div
              v-for="field in fileFields"
              :key="field.name"
              class="config-file-group"
            >
              <label class="file-label">{{ field.label }}</label>
              <FileUpload
                :ref="(el) => setFileUploadRef(field.name, el)"
                :accept="field.accept"
                :multiple="field.multiple"
                @file-selected="(files) => handleFileSelected(field.name, files)"
                @error="(error) => handleFileError(error)"
              />
            </div>

            <!-- 其他类型字段显示为紧凑输入框 -->
            <div
              v-for="field in otherFields"
              :key="field.name"
              class="config-input-group"
            >
              <label class="input-label">{{ field.label }}</label>
              <input
                v-if="field.type === 'number'"
                v-model.number="field.value"
                type="number"
                class="compact-input"
                @input="updateConfigField(field.name, Number(($event.target as HTMLInputElement)?.value || 0))"
              />
              <input
                v-else
                v-model="field.value"
                type="text"
                class="compact-input"
                @input="updateConfigField(field.name, (($event.target as HTMLInputElement)?.value || ''))"
              />
            </div>
          </div>
        </div>

        <!-- 已上传文件显示区域 -->
        <div v-if="uploadedFiles.length > 0" class="uploaded-files-area">
          <div class="uploaded-files-header">
            <Icon :icon="'mdi:paperclip'" width="16" height="16" />
            <span class="files-count">已上传 {{ uploadedFiles.length }} 个文件</span>
            <button @click="clearUploadedFiles" class="clear-files-btn" title="清空所有文件">
              <Icon :icon="'mdi:close'" width="14" height="14" />
            </button>
          </div>
          <div class="uploaded-files-list">
            <div
              v-for="(file, index) in uploadedFiles"
              :key="index"
              class="uploaded-file-item"
            >
              <Icon :icon="getFileIcon(file.fileType)" width="14" height="14" />
              <span class="file-name">{{ file.fileName }}</span>
              <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
              <button
                @click="removeUploadedFile(index)"
                class="remove-file-btn"
                title="移除文件"
              >
                <Icon :icon="'mdi:close'" width="12" height="12" />
              </button>
            </div>
          </div>
        </div>

        <!-- 输入框区域 -->
        <div class="input-wrapper" :class="{ 'with-config': hasConfigData && isConfigMode }">
          <!-- 模式切换按钮 -->
          <button
            v-if="hasConfigData"
            @click="toggleConfigMode"
            class="mode-toggle-btn"
            :class="{ active: isConfigMode }"
            :title="isConfigMode ? '切换到聊天模式' : '切换到配置模式'"
          >
            <Icon :icon="isConfigMode ? chatIcon : settingsIcon" width="18" height="18" />
          </button>

          <!-- 文本输入框 - 始终显示，可以与配置一起编辑 -->
          <textarea
            v-model="inputMessage"
            ref="inputTextarea"
            class="message-input"
            :placeholder="!hasConfigData ? '等待配置表单...' : (isConfigMode ? '输入消息...' : '请输入您的问题...')"
            rows="1"
            @keydown="handleKeyDown"
            @input="adjustTextareaHeight"
            :disabled="isAnyAiTyping || !hasConfigData || isGenerating"
          ></textarea>

          <!-- 发送/停止按钮 -->
          <button
            @click="sendMessage"
            class="send-btn"
            :class="{ 'stop-mode': isGenerating }"
            :disabled="(!hasConfigData && !isConfigMode) || (!isGenerating && !inputMessage.trim() && !isConfigMode) || isAnyAiTyping"
            :title="isGenerating ? '停止生成' : (isConfigMode ? '应用配置' : '发送消息')"
          >
            <Icon 
              :icon="isGenerating ? stopIcon : (isConfigMode ? checkIcon : sendIcon)" 
              width="20" 
              height="20" 
            />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { Icon } from '@iconify/vue'
import { ipc } from '@/utils/ipcRenderer'
import { ipcApiRoute } from '@/ipc/ipcApi'
import { sendAiStreamChatMessage, type ExtendedFileInfo } from '@/api/modules/ai/chat'
import { useUserStore } from '@/stores/modules/user'

import FileUpload from './components/FileUpload.vue'
import MessageRenderer from './components/MessageRenderer.vue'

// 导入图标
import sparklesIcon from '@iconify-icons/fluent-emoji/sparkles'
import closeIcon from '@iconify-icons/mdi/close'
import userIcon from '@iconify-icons/mdi/account'
import sendIcon from '@iconify-icons/mdi/send'
import settingsIcon from '@iconify-icons/mdi/cog'
import chatIcon from '@iconify-icons/mdi/chat'
import checkIcon from '@iconify-icons/mdi/check'
import refreshIcon from '@iconify-icons/mdi/refresh'
import stopIcon from '@iconify-icons/mdi/stop'

// 类型定义
interface ChatMessage {
  id: string
  role: 'user' | 'ai'
  content: string
  timestamp: number
  status?: 'generating' | 'completed' | 'stopped' | 'error'
  canRegenerate?: boolean
  messageType?: 'text' | 'file' | 'image' | 'mixed'
  files?: ExtendedFileInfo[]
}

interface AppInfo {
  id: number
  appId?: string
  name: string
  description: string
  type: number
  themeColor?: string
}

interface AppConfigData {
  appInfo: AppInfo
  inputs: Record<string, any>
  configFields?: FormField[]
}

interface FormField {
  label: string
  name: string
  class: string
  value: string | number
  type?: string
  options?: Array<{label: string, value: string}>
  min?: number
  max?: number
  step?: number
  accept?: string
  multiple?: boolean
}

// 配置数据来源枚举
const ConfigSource = {
  WINDOW: 'window',
  IPC: 'ipc',
  DEFAULT: 'default'
} as const

// 默认配置
const defaultConfig: AppConfigData = {
  appInfo: {
    id: 0,
    name: 'AI助手',
    description: '默认AI助手',
    type: 1
  },
  inputs: {},
  configFields: []
}

// 工具函数
const hexToRgb = (hex: string) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      }
    : null
}

const generateMessageId = (): string => {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// 状态定义
const messages = ref<ChatMessage[]>([])
const inputMessage = ref('')
const messagesContainer = ref<HTMLElement | null>(null)
const inputTextarea = ref<HTMLTextAreaElement | null>(null)
const conversationId = ref<string | null>(null)
const currentStreamController = ref<any>(null)
const isGenerating = ref(false)
const lastUserMessage = ref('')
const themeColor = ref('#8957e5')
const appConfigData = ref<AppConfigData | null>(null)
const currentConfig = ref<Record<string, any>>({})
const isConfigMode = ref(false)
const configFields = ref<FormField[]>([])
const hasConfigData = ref(false)
const configSource = ref<typeof ConfigSource[keyof typeof ConfigSource]>(ConfigSource.DEFAULT)
const uploadedFiles = ref<ExtendedFileInfo[]>([]) // 存储已上传的文件信息
const fileUploadRefs = ref<Record<string, any>>({}) // 存储FileUpload组件的引用

// 计算属性
const themeStyles = computed(() => {
  const rgb = hexToRgb(themeColor.value)
  return rgb
    ? {
        '--theme-color': themeColor.value,
        '--theme-color-r': rgb.r.toString(),
        '--theme-color-g': rgb.g.toString(),
        '--theme-color-b': rgb.b.toString()
      }
    : {
        '--theme-color': '#8957e5',
        '--theme-color-r': '137',
        '--theme-color-g': '87',
        '--theme-color-b': '229'
      }
})

const selectFields = computed(() =>
  configFields.value.filter(field => field.type === 'select')
)

const rangeFields = computed(() =>
  configFields.value.filter(field => field.type === 'range')
)

const fileFields = computed(() =>
  configFields.value.filter(field => field.type === 'file')
)

const otherFields = computed(() =>
  configFields.value.filter(field => !field.type || (field.type !== 'select' && field.type !== 'range' && field.type !== 'file'))
)

const isAnyAiTyping = computed(() => 
  messages.value.some(message => message.role === 'ai' && message.status === 'generating')
)

// 消息管理方法
const createAiMessage = (): ChatMessage => ({
  id: generateMessageId(),
  role: 'ai',
  content: '',
  timestamp: Date.now(),
  status: 'generating',
  canRegenerate: false
})

const createErrorMessage = (content: string): ChatMessage => ({
  id: generateMessageId(),
  role: 'ai',
  content,
  timestamp: Date.now(),
  status: 'error',
  canRegenerate: true
})

const updateMessageContent = (messageId: string, content: string) => {
  const messageIndex = messages.value.findIndex(msg => msg.id === messageId)
  if (messageIndex !== -1) {
    messages.value[messageIndex].content = content
  }
}

const updateMessageStatus = (messageId: string, status: ChatMessage['status'], content?: string) => {
  const messageIndex = messages.value.findIndex(msg => msg.id === messageId)
  if (messageIndex !== -1) {
    messages.value[messageIndex].status = status
    messages.value[messageIndex].canRegenerate = status !== 'generating'
    if (content) {
      messages.value[messageIndex].content = content
    }
  }
}

// 配置管理方法
const debouncedReceiveConfigData = debounce(
  (data: AppConfigData, source: typeof ConfigSource[keyof typeof ConfigSource]) => {
    receiveConfigData(data, source)
  },
  300
)

const receiveConfigData = (data: AppConfigData, source: typeof ConfigSource[keyof typeof ConfigSource]) => {
  if (configSource.value === ConfigSource.IPC && source !== ConfigSource.IPC) {
    return
  }

  configSource.value = source
  if (data.appInfo.themeColor) {
    themeColor.value = data.appInfo.themeColor
  }

  appConfigData.value = data
  currentConfig.value = { ...data.inputs }
  hasConfigData.value = true

  if (data.configFields && data.configFields.length > 0) {
    configFields.value = data.configFields.map(field => ({
      ...field,
      value: data.inputs[field.name] || field.value
    }))
  } else {
    configFields.value = Object.entries(data.inputs).map(([key, value]) => ({
      label: key,
      name: key,
      class: 'form-control',
      value: value as string | number,
      type: typeof value === 'number' ? 'number' : 'text'
    }))
  }

  if (configFields.value.length > 0) {
    isConfigMode.value = true
  }
}

// UI交互方法
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const adjustTextareaHeight = () => {
  const textarea = inputTextarea.value
  if (textarea) {
    textarea.style.height = 'auto'
    const maxHeight = 100
    const newHeight = Math.min(textarea.scrollHeight, maxHeight)
    textarea.style.height = newHeight + 'px'
  }
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

const closeWindow = () => {
  ipc?.invoke(ipcApiRoute.closeAiChatWindow)
}

const toggleConfigMode = () => {
  isConfigMode.value = !isConfigMode.value
  if (isConfigMode.value) {
    inputMessage.value = ''
  }
}

const updateConfigField = (fieldName: string, value: any) => {
  currentConfig.value[fieldName] = value
  const field = configFields.value.find(f => f.name === fieldName)
  if (field) {
    field.value = value
  }
}

// 生命周期钩子
onMounted(() => {
  inputTextarea.value?.focus()

  receiveConfigData(defaultConfig, ConfigSource.DEFAULT)

  if ((window as any).aiAppConfig) {
    receiveConfigData((window as any).aiAppConfig, ConfigSource.WINDOW)
  }

  if (ipc) {
    ipc.on('ai-app-config', handleIpcConfig)
  }
})

onBeforeUnmount(() => {
  if (ipc) {
    ipc.removeListener('ai-app-config', handleIpcConfig)
  }
})

const handleIpcConfig = (_event: any, data: AppConfigData) => {
  debouncedReceiveConfigData(data, ConfigSource.IPC)
}

// 添加防抖函数
function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null
  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(() => {
      fn(...args)
      timeoutId = null
    }, delay)
  }
}

// 添加流式API调用函数
const callStreamApiWithTyping = async (message: string, aiMessage: ChatMessage): Promise<string> => {
  return new Promise((resolve, reject) => {
    try {
      // 构建请求参数
      const streamParams = {
        query: message,
        ...(conversationId.value && { conversationId: conversationId.value }),
        ...(appConfigData.value && {
          app_id: appConfigData.value.appInfo.appId,
          inputs: currentConfig.value
        }),
        shouldProcessNodeStarted: false,
        files: uploadedFiles.value.map(file => ({
          type: file.type,
          transferMethod: file.transferMethod,
          url: file.url,
          uploadFileId: file.uploadFileId
        }))
      }

      let fullResponse = ''
      const controller = sendAiStreamChatMessage(streamParams, {
        onData: (chunk) => {
          fullResponse += chunk.content
          updateMessageContent(aiMessage.id, fullResponse)
          if (chunk.conversationId && !conversationId.value) {
            conversationId.value = chunk.conversationId
          }
        },
        onError: (error) => {
          updateMessageStatus(aiMessage.id, 'error', `抱歉，AI服务出现错误：${error.message}`)
          currentStreamController.value = null
          isGenerating.value = false
          reject(error)
        },
        onComplete: () => {
          if (fullResponse.trim()) {
            updateMessageStatus(aiMessage.id, 'completed', fullResponse)
            currentStreamController.value = null
            isGenerating.value = false
            resolve(fullResponse)
          } else {
            updateMessageStatus(aiMessage.id, 'error', '抱歉，AI没有返回任何内容，请重试。')
            currentStreamController.value = null
            isGenerating.value = false
            reject(new Error('流式API返回空内容'))
          }
        }
      })

      currentStreamController.value = controller
    } catch (error) {
      aiMessage.status = 'error'
      aiMessage.canRegenerate = true
      currentStreamController.value = null
      isGenerating.value = false
      reject(error)
    }
  })
}

// 添加配置消息发送函数
const sendConfigMessage = async () => {
  if (!appConfigData.value || !hasConfigData.value) {
    const errorMessage = createErrorMessage('抱歉，当前没有可用的AI应用配置，无法发送配置信息。')
    messages.value.push(errorMessage)
    return
  }

  // 过滤掉文件类型的配置项，避免在消息中显示文件详情
  const nonFileConfigItems = configFields.value
    .filter(field => field.type !== 'file')
    .map(field => `${field.label}=${field.value}`)
    .join(', ')

  // 单独处理文件配置项
  const fileConfigItems = configFields.value
    .filter(field => field.type === 'file' && field.value)
    .map(field => `${field.label}=${field.value}`)
    .join(', ')

  let messageContent = ''
  if (nonFileConfigItems) {
    messageContent += `已应用配置：${nonFileConfigItems}`
  }
  if (fileConfigItems) {
    messageContent += messageContent ? `\n文件：${fileConfigItems}` : `文件：${fileConfigItems}`
  }
  if (inputMessage.value.trim()) {
    messageContent += messageContent ? `\n\n${inputMessage.value.trim()}` : inputMessage.value.trim()
  }

  // 确定配置消息类型
  const hasFiles = uploadedFiles.value.length > 0
  const hasText = messageContent.length > 0
  let messageType: 'text' | 'file' | 'image' | 'mixed' = 'text'

  if (hasFiles && hasText) {
    messageType = 'mixed'
  } else if (hasFiles) {
    // 检查是否全是图片文件
    const allImages = uploadedFiles.value.every(file => file.fileType.startsWith('image/'))
    messageType = allImages ? 'image' : 'file'
  }

  const configMessage: ChatMessage = {
    id: generateMessageId(),
    role: 'user',
    content: messageContent,
    timestamp: Date.now(),
    messageType,
    files: hasFiles ? [...uploadedFiles.value] : []
  }

  messages.value.push(configMessage)
  const currentInput = inputMessage.value.trim()
  inputMessage.value = ''

  // 构建简化的配置描述用于API调用
  const configSummary = nonFileConfigItems || '默认配置'
  lastUserMessage.value = currentInput || `请根据以下配置为我提供帮助：${configSummary}`
  isConfigMode.value = false

  await nextTick()
  scrollToBottom()
  adjustTextareaHeight()

  try {
    const aiMessage = createAiMessage()
    messages.value.push(aiMessage)
    await nextTick()
    scrollToBottom()

    isGenerating.value = true
    const messageToSend = currentInput || `请根据以下配置为我提供帮助：${configSummary}`
    await callStreamApiWithTyping(messageToSend, aiMessage)

    // 配置消息发送成功后清空已上传的文件
    uploadedFiles.value = []
  } catch (error) {
    const errorMessage = createErrorMessage(
      `配置模式AI接口调用失败：${(error as Error)?.message || '未知错误'}`
    )
    messages.value.push(errorMessage)
  } finally {
    isGenerating.value = false
    currentStreamController.value = null
    await nextTick()
    scrollToBottom()
    inputTextarea.value?.focus()
  }
}

// 添加发送消息函数
const sendMessage = async () => {
  if (isConfigMode.value) {
    await sendConfigMessage()
    return
  }

  if (!inputMessage.value.trim() || isAnyAiTyping.value) return
  if (!hasConfigData.value) {
    const errorMessage = createErrorMessage('抱歉，当前没有可用的AI应用配置。')
    messages.value.push(errorMessage)
    return
  }

  // 确定消息类型和内容
  const hasFiles = uploadedFiles.value.length > 0
  const hasText = inputMessage.value.trim().length > 0
  let messageType: 'text' | 'file' | 'image' | 'mixed' = 'text'

  if (hasFiles && hasText) {
    messageType = 'mixed'
  } else if (hasFiles) {
    // 检查是否全是图片文件
    const allImages = uploadedFiles.value.every(file => file.fileType.startsWith('image/'))
    messageType = allImages ? 'image' : 'file'
  }

  const userMessage: ChatMessage = {
    id: generateMessageId(),
    role: 'user',
    content: inputMessage.value.trim(),
    timestamp: Date.now(),
    messageType,
    files: hasFiles ? [...uploadedFiles.value] : []
  }

  messages.value.push(userMessage)
  const currentInput = inputMessage.value.trim()
  inputMessage.value = ''
  lastUserMessage.value = currentInput

  await nextTick()
  scrollToBottom()
  adjustTextareaHeight()

  try {
    const aiMessage = createAiMessage()
    messages.value.push(aiMessage)
    await nextTick()
    scrollToBottom()

    isGenerating.value = true
    await callStreamApiWithTyping(currentInput, aiMessage)

    // 消息发送成功后清空已上传的文件
    uploadedFiles.value = []
  } catch (error) {
    const errorMessage = createErrorMessage(
      `AI服务暂时不可用：${(error as Error)?.message || '未知错误'}`
    )
    messages.value.push(errorMessage)
  } finally {
    isGenerating.value = false
    currentStreamController.value = null
    await nextTick()
    scrollToBottom()
    inputTextarea.value?.focus()
  }
}

// 添加停止生成函数
const stopGeneration = () => {
  if (!currentStreamController.value || !isGenerating.value) return

  currentStreamController.value.abort()
  const lastAiMessage = messages.value.slice().reverse().find(msg =>
    msg.role === 'ai' && msg.status === 'generating'
  )

  if (lastAiMessage) {
    const stoppedContent = lastAiMessage.content
      ? `${lastAiMessage.content}\n\n[已停止生成]`
      : '[已停止生成]'
    updateMessageStatus(lastAiMessage.id, 'stopped', stoppedContent)
  }

  currentStreamController.value = null
  isGenerating.value = false
}

// 添加重新生成函数
const regenerateResponse = async (messageId: string) => {
  if (!lastUserMessage.value || isGenerating.value) return

  const messageIndex = messages.value.findIndex(msg => msg.id === messageId)
  if (messageIndex === -1) return
  messages.value.splice(messageIndex)

  try {
    const newAiMessage = createAiMessage()
    messages.value.push(newAiMessage)
    await nextTick()
    scrollToBottom()

    isGenerating.value = true
    await callStreamApiWithTyping(lastUserMessage.value, newAiMessage)
  } catch (error) {
    const errorMessage = createErrorMessage(
      `重新生成失败：${(error as Error)?.message || '未知错误'}。请稍后再试。`
    )
    messages.value.push(errorMessage)
  } finally {
    isGenerating.value = false
    currentStreamController.value = null
    await nextTick()
    scrollToBottom()
  }
}

// 设置FileUpload组件引用
const setFileUploadRef = (fieldName: string, el: any) => {
  if (el) {
    fileUploadRefs.value[fieldName] = el
  }
}

// 添加文件处理函数
const handleFileSelected = async (fieldName: string, files: File[]) => {
  if (files.length > 0) {
    try {
      const fileUploadRef = fileUploadRefs.value[fieldName]
      if (!fileUploadRef) {
        throw new Error('FileUpload组件引用未找到')
      }

      console.log(`[主组件] 开始上传字段 "${fieldName}" 的文件:`, files.map(f => f.name))

      const uploadResults = await fileUploadRef.uploadFiles(files)
      uploadedFiles.value.push(...uploadResults)

      const fileNames = uploadResults.map((result: ExtendedFileInfo) => result.fileName).join(', ')
      updateConfigField(fieldName, fileNames)

      console.log(`[主组件] 字段 "${fieldName}" 文件上传完成:`, uploadResults)
    } catch (error) {
      console.error(`[主组件] 字段 "${fieldName}" 文件上传失败:`, error)
      handleFileError(`文件上传失败: ${(error as Error).message}`)
    }
  } else {
    updateConfigField(fieldName, '')
    uploadedFiles.value = uploadedFiles.value.filter(file =>
      !file.fileName.includes(fieldName)
    )
  }
}

const handleFileError = (error: string) => {
  console.error('文件上传错误:', error)
  const errorMessage = createErrorMessage(`文件上传失败：${error}`)
  messages.value.push(errorMessage)
}

// 文件管理函数
const clearUploadedFiles = () => {
  uploadedFiles.value = []
}

const removeUploadedFile = (index: number) => {
  uploadedFiles.value.splice(index, 1)
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const getFileIcon = (mimeType: string): string => {
  if (mimeType.startsWith('image/')) return 'mdi:file-image'
  if (mimeType.startsWith('video/')) return 'mdi:file-video'
  if (mimeType.startsWith('audio/')) return 'mdi:file-music'
  if (mimeType.includes('pdf')) return 'mdi:file-pdf-box'
  if (mimeType.includes('word') || mimeType.includes('document')) return 'mdi:file-word'
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'mdi:file-excel'
  if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'mdi:file-powerpoint'
  if (mimeType.includes('text/')) return 'mdi:file-document'
  return 'mdi:file'
}

const resetConfig = () => {
  if (appConfigData.value) {
    const currentAppConfig = appConfigData.value
    currentConfig.value = { ...currentAppConfig.inputs }
    configFields.value = configFields.value.map(field => ({
      ...field,
      value: currentAppConfig.inputs[field.name] || field.value
    }))
  }
}
</script>

<style scoped lang="scss">
.ai-chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 2px);
  background: #f8f6fd;
  color: #2c3e50;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid #e6e1f4;
  margin: 1px;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 18px;
  height: 48px;
  background: var(--theme-color);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-top-left-radius: 7px;
  border-top-right-radius: 7px;
  -webkit-app-region: drag;
  box-shadow: 0 1px 3px rgba(var(--theme-color-r), var(--theme-color-g), var(--theme-color-b), 0.1);

  .header-content {
    display: flex;
    align-items: center;
    gap: 10px;

    .header-icon {
      color: #ffffff;
      opacity: 0.9;
    }

    .header-title {
      font-size: 17px;
      font-weight: 600;
      margin: 0;
      color: #ffffff;
      letter-spacing: 0.5px;
    }
  }

  .close-btn {
    -webkit-app-region: no-drag;
    background: rgba(255, 255, 255, 0.15);
    border: none;
    border-radius: 6px;
    padding: 8px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.25);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0px);
    }
  }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 18px;
  background: #ffffff;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f8f6fd;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--theme-color);
    opacity: 0.5;
    border-radius: 3px;
    
    &:hover {
      opacity: 0.7;
    }
  }
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;

  .welcome-icon {
    margin-bottom: 16px;
    color: var(--theme-color);
  }

  .welcome-text {
    font-size: 17px;
    color: #6b6b6b;
    margin: 0;
  }
}

.message-item {
  display: flex;
  gap: 8px;
  
  &.user-message {
    flex-direction: row-reverse;
    
    .message-content {
      align-items: flex-end;
    }
    
    .message-bubble {
      background: var(--theme-color);
      color: #ffffff;
    }
  }
  
  &.ai-message {
    .message-bubble {
      background: transparent;
      color: #2c3e50;
      border: none;
      padding: 0;
    }
  }
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f8f6fd;
  border: 1px solid #e6e1f4;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.message-bubble {
  padding: 8px 12px;
  border-radius: 12px;
  border: 1px solid #e0e3e7;
  
  .message-text {
    margin: 0;
    line-height: 1.5;
    font-size: 15px;
    word-wrap: break-word;
  }
}

.message-time {
  font-size: 13px;
  color: #909399;
  margin-top: 5px;
  padding: 0 4px;
}

// 加载指示器样式
.ai-loading-container {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;

  .thinking-dots {
    display: flex;
    gap: 4px;

    span {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: var(--theme-color);
      opacity: 0.7;
      animation: simple-bounce 1.4s infinite ease-in-out both;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }

      &:nth-child(3) {
        animation-delay: 0s;
      }
    }
  }

  .thinking-text {
    font-size: 13px;
    color: #909399;
  }
}

// 流式内容样式
.ai-streaming-content {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  transition: all 0.2s ease-in-out;

  .message-renderer {
    transition: opacity 0.15s ease-in-out;
  }
}



// AI最终内容样式
.ai-final-content {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
}

// 简化动画定义
@keyframes simple-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.message-actions {
  display: flex;
  gap: 6px;
  margin-top: 6px;
  opacity: 0;
  transition: opacity 0.2s ease;

  .action-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: none;
    border-radius: 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;

    &.stop-btn {
      background: #ff4757;
      color: white;

      &:hover {
        background: #ff3742;
        transform: translateY(-1px);
      }
    }

    &.regenerate-btn {
      background: #f1f3f4;
      color: #5f6368;
      border: 1px solid #e8eaed;

      &:hover {
        background: #e8f0fe;
        color: var(--theme-color);
        border-color: var(--theme-color);
        transform: translateY(-1px);
      }
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// 鼠标悬停时显示操作按钮
.message-item:hover .message-actions {
  opacity: 1;
}

.input-wrapper {
  display: flex;
  gap: 10px;
  align-items: flex-end;
  background: #ffffff;
  padding: 10px 15px;
  transition: all 0.2s ease;

  &.with-config {
    border-top: 1px solid rgba(230, 225, 244, 0.5);
  }

  .mode-toggle-btn {
    background: var(--theme-color);
    border: none;
    border-radius: 8px;
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
    position: relative;

    &:hover {
      filter: brightness(0.9);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(var(--theme-color-r), var(--theme-color-g), var(--theme-color-b), 0.3);
    }

    &:active {
      transform: translateY(0);
    }

    &.active {
      background: #ff6b35;

      &:hover {
        background: #e55a2b;
        box-shadow: 0 4px 8px rgba(255, 107, 53, 0.3);
      }
    }

    // 添加小圆点指示器
    &.active::after {
      content: '';
      position: absolute;
      top: -2px;
      right: -2px;
      width: 8px;
      height: 8px;
      background: var(--theme-color);
      border-radius: 50%;
      border: 2px solid #ffffff;
    }
  }

  .config-mode-hint {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--theme-color);
    font-size: 14px;
    font-weight: 500;
    padding: 10px 16px;
    background: linear-gradient(135deg, #f3f0fb 0%, #fef7f4 100%);
    border-radius: 10px;
    border: 1px solid #e6e1f4;

    svg {
      opacity: 0.8;
    }
  }
}

.message-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: #2c3e50;
  font-size: 16px;
  line-height: 1.5;
  padding: 6px 0;
  resize: none;
  min-height: 28px;
  max-height: 100px;
  
  &::placeholder {
    color: #9ca3af;
    font-size: 16px;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.send-btn {
  background: var(--theme-color);
  border: none;
  border-radius: 8px;
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(var(--theme-color-r), var(--theme-color-g), var(--theme-color-b), 0.2);

  &:hover:not(:disabled) {
    filter: brightness(0.9);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--theme-color-r), var(--theme-color-g), var(--theme-color-b), 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(var(--theme-color-r), var(--theme-color-g), var(--theme-color-b), 0.2);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    box-shadow: none;
  }

  // 停止生成模式样式
  &.stop-mode {
    background: #ff4757;
    box-shadow: 0 2px 6px rgba(255, 71, 87, 0.2);

    &:hover:not(:disabled) {
      background: #ff3742;
      box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
    }

    &:active:not(:disabled) {
      background: #e33142;
      box-shadow: 0 2px 6px rgba(255, 71, 87, 0.2);
    }
  }
}

// 一体化输入区域样式
.input-container {
  padding: 12px 20px 20px;
  background: #ffffff;
  border-top: 1px solid #e6e1f4;
}

.no-config-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 14px;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin-bottom: 12px;

  .notice-icon {
    color: #856404;
    opacity: 0.8;
  }

  .notice-text {
    color: #856404;
    font-size: 15px;
    font-weight: 500;
  }
}

.unified-input-area {
  background: #ffffff;
  border-radius: 12px;
  border: 2px solid #e6e1f4;
  overflow: hidden;
  transition: all 0.3s ease;

  &.has-config {
    border-color: var(--theme-color);
    box-shadow: 0 4px 12px rgba(var(--theme-color-r), var(--theme-color-g), var(--theme-color-b), 0.15);
  }

  &.disabled {
    opacity: 0.6;
    background: #f8f9fa;
    border-color: #dee2e6;
    pointer-events: none;
  }

  &:focus-within:not(.disabled) {
    border-color: var(--theme-color);
    box-shadow: 0 0 0 2px rgba(var(--theme-color-r), var(--theme-color-g), var(--theme-color-b), 0.1);
  }
}

// 配置标签区域
.config-tags-area {
  background: #f8f6fd;
  padding: 14px 18px;
  border-bottom: 1px solid #e6e1f4;

  .config-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 14px;

    .config-icon {
      color: var(--theme-color);
      opacity: 0.9;
    }

    .config-title {
      color: #2c3e50;
      font-weight: 600;
      font-size: 15px;
      flex: 1;
    }

    .reset-btn {
      background: #e6e1f4;
      border: none;
      border-radius: 6px;
      padding: 6px;
      color: var(--theme-color);
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: #f3f0fb;
        transform: rotate(180deg);
      }
    }
  }

  .config-tags {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  // 标签选择组
  .config-tag-group {
    .tag-label {
      display: block;
      color: #2c3e50;
      font-size: 13px;
      font-weight: 500;
      margin-bottom: 5px;
      opacity: 0.8;
    }

    .tag-options {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;

      .tag-option {
        background: #ffffff;
        border: 1px solid #e6e1f4;
        border-radius: 16px;
        padding: 5px 11px;
        color: #2c3e50;
        font-size: 13px;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;

        &:hover {
          background: #f3f0fb;
          border-color: var(--theme-color);
          transform: translateY(-1px);
        }

        &.active {
          background: var(--theme-color);
          color: #ffffff;
          font-weight: 600;
          border-color: var(--theme-color);
          box-shadow: 0 2px 4px rgba(var(--theme-color-r), var(--theme-color-g), var(--theme-color-b), 0.2);
        }
      }
    }
  }

  // 滑块控制组
  .config-range-group {
    .range-label {
      display: block;
      color: #2c3e50;
      font-size: 13px;
      font-weight: 500;
      margin-bottom: 6px;
      opacity: 0.8;
    }

    .range-control {
      display: flex;
      align-items: center;
      gap: 8px;

      .range-slider {
        flex: 1;
        height: 4px;
        background: #e6e1f4;
        border-radius: 2px;
        outline: none;
        -webkit-appearance: none;
        appearance: none;

        &::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: var(--theme-color);
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(var(--theme-color-r), var(--theme-color-g), var(--theme-color-b), 0.3);
          transition: all 0.2s ease;

          &:hover {
            transform: scale(1.1);
            filter: brightness(0.9);
          }
        }

        &::-moz-range-thumb {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: var(--theme-color);
          cursor: pointer;
          border: none;
          box-shadow: 0 2px 4px rgba(var(--theme-color-r), var(--theme-color-g), var(--theme-color-b), 0.3);
        }
      }

      .range-value {
        color: #2c3e50;
        font-size: 13px;
        font-weight: 600;
        min-width: 35px;
        text-align: center;
        background: #ffffff;
        border: 1px solid #e6e1f4;
        padding: 3px 7px;
        border-radius: 8px;
        transition: all 0.2s ease;

        &:focus {
          outline: none;
          border-color: var(--theme-color);
          background: #ffffff;
          box-shadow: 0 0 0 2px rgba(var(--theme-color-r), var(--theme-color-g), var(--theme-color-b), 0.1);
        }
      }
    }
  }

  // 紧凑输入组
  .config-input-group {
    display: flex;
    align-items: center;
    gap: 6px;

    .input-label {
      color: #2c3e50;
      font-size: 13px;
      font-weight: 500;
      opacity: 0.8;
      min-width: 60px;
    }

    .compact-input {
      flex: 1;
      padding: 5px 9px;
      border: 1px solid #e6e1f4;
      border-radius: 6px;
      font-size: 13px;
      color: #2c3e50;
      background: #ffffff;
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--theme-color);
        background: #ffffff;
        box-shadow: 0 0 0 2px rgba(var(--theme-color-r), var(--theme-color-g), var(--theme-color-b), 0.1);
      }
    }
  }

  // 文件上传组
  .config-file-group {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .file-label {
      color: #2c3e50;
      font-size: 13px;
      font-weight: 500;
      opacity: 0.8;
    }
  }
}

// 已上传文件区域样式
.uploaded-files-area {
  background: #f8f6fd;
  border: 1px solid #e6e1f4;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;

  .uploaded-files-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: var(--theme-color);
    font-size: 13px;
    font-weight: 500;

    .files-count {
      flex: 1;
    }

    .clear-files-btn {
      background: #e6e1f4;
      border: none;
      border-radius: 4px;
      padding: 4px;
      color: #666;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: #d1c7e8;
        color: #333;
      }
    }
  }

  .uploaded-files-list {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .uploaded-file-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 10px;
      background: #ffffff;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      font-size: 12px;

      .file-name {
        flex: 1;
        color: #2c3e50;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-size {
        color: #6c757d;
        font-size: 11px;
      }

      .remove-file-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 18px;
        height: 18px;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #c82333;
        }
      }
    }
  }
}
</style>
