<template>
  <div class="typing-effect">
    <span class="typing-text">{{ displayText }}</span>
    <span v-if="isTyping" class="typing-cursor">|</span>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'

interface Props {
  text: string
  speed?: number // 打字速度（毫秒/字符）
  delay?: number // 开始前的延迟（毫秒）
  cursorBlink?: boolean // 是否闪烁光标
  autoStart?: boolean // 是否自动开始
}

const props = withDefaults(defineProps<Props>(), {
  speed: 30,
  delay: 0,
  cursorBlink: true,
  autoStart: true
})

const emit = defineEmits<{
  complete: []
  start: []
}>()

const displayText = ref('')
const isTyping = ref(false)
const cursorVisible = ref(true)
let typingTimer: number | null = null
let cursorTimer: number | null = null
let currentIndex = 0

// 开始打字效果
const startTyping = () => {
  if (isTyping.value) return
  
  isTyping.value = true
  currentIndex = 0
  displayText.value = ''
  emit('start')
  
  // 延迟开始
  if (props.delay > 0) {
    setTimeout(() => {
      typeNextChar()
    }, props.delay)
  } else {
    typeNextChar()
  }
  
  // 光标闪烁
  if (props.cursorBlink) {
    startCursorBlink()
  }
}

// 打字下一个字符
const typeNextChar = () => {
  if (currentIndex < props.text.length) {
    displayText.value += props.text[currentIndex]
    currentIndex++
    
    typingTimer = window.setTimeout(() => {
      typeNextChar()
    }, props.speed)
  } else {
    // 打字完成
    isTyping.value = false
    stopCursorBlink()
    emit('complete')
  }
}

// 开始光标闪烁
const startCursorBlink = () => {
  cursorTimer = window.setInterval(() => {
    cursorVisible.value = !cursorVisible.value
  }, 500)
}

// 停止光标闪烁
const stopCursorBlink = () => {
  if (cursorTimer) {
    clearInterval(cursorTimer)
    cursorTimer = null
  }
  cursorVisible.value = true
}

// 停止打字
const stopTyping = () => {
  if (typingTimer) {
    clearTimeout(typingTimer)
    typingTimer = null
  }
  stopCursorBlink()
  isTyping.value = false
  displayText.value = props.text
}

// 重置打字效果
const reset = () => {
  stopTyping()
  displayText.value = ''
  currentIndex = 0
  cursorVisible.value = true
}

// 监听文本变化
watch(() => props.text, (newText) => {
  if (newText !== displayText.value) {
    reset()
    if (props.autoStart) {
      startTyping()
    }
  }
}, { immediate: true })

// 组件挂载时自动开始
onMounted(() => {
  if (props.autoStart && props.text) {
    startTyping()
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopTyping()
})

// 暴露方法给父组件
defineExpose({
  startTyping,
  stopTyping,
  reset,
  isTyping: () => isTyping.value
})
</script>

<style scoped lang="scss">
.typing-effect {
  display: inline;
  line-height: inherit;
  
  .typing-text {
    white-space: pre-wrap;
    word-break: break-word;
  }
  
  .typing-cursor {
    display: inline-block;
    width: 2px;
    height: 1.2em;
    background: currentColor;
    margin-left: 2px;
    animation: blink 1s infinite;
    vertical-align: middle;
    
    &.hidden {
      opacity: 0;
    }
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

// 优化性能的CSS
.typing-effect {
  will-change: contents;
  contain: layout style paint;
  
  // 使用 transform 而不是改变布局属性来优化性能
  .typing-text {
    transform: translateZ(0);
    backface-visibility: hidden;
  }
  
  .typing-cursor {
    transform: translateZ(0);
    will-change: opacity;
  }
}
</style> 