/**
 * 链接处理工具函数
 */

// 链接正则表达式 - 更全面的URL检测
export const URL_REGEX = /(https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*)?(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)/gi

// 检测文本中是否包含链接
export const hasLinks = (text: string): boolean => {
  return URL_REGEX.test(text)
}

// 提取文本中的所有链接
export const extractLinks = (text: string): string[] => {
  const matches = text.match(URL_REGEX)
  return matches ? [...new Set(matches)] : [] // 去重
}

// 检查URL是否为图片链接
export const isImageUrl = (url: string): boolean => {
  return /\.(jpg|jpeg|png|gif|webp|svg|bmp|ico)(\?.*)?$/i.test(url)
}

// 检查URL是否为视频链接
export const isVideoUrl = (url: string): boolean => {
  return /\.(mp4|webm|ogg|avi|mov|wmv|flv|mkv)(\?.*)?$/i.test(url)
}

// 检查URL是否为音频链接
export const isAudioUrl = (url: string): boolean => {
  return /\.(mp3|wav|ogg|aac|flac|m4a)(\?.*)?$/i.test(url)
}

// 检查URL是否为文档链接
export const isDocumentUrl = (url: string): boolean => {
  return /\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt)(\?.*)?$/i.test(url)
}

// 检查是否为特殊平台链接（YouTube、Twitter等）
export const getSpecialPlatform = (url: string): string | null => {
  const platforms = [
    { name: 'youtube', pattern: /(?:youtube\.com|youtu\.be)/i },
    { name: 'twitter', pattern: /(?:twitter\.com|x\.com)/i },
    { name: 'github', pattern: /github\.com/i },
    { name: 'bilibili', pattern: /bilibili\.com/i },
    { name: 'zhihu', pattern: /zhihu\.com/i },
    { name: 'weibo', pattern: /weibo\.com/i },
  ]

  for (const platform of platforms) {
    if (platform.pattern.test(url)) {
      return platform.name
    }
  }
  
  return null
}

// 验证URL格式
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// 获取域名
export const getDomain = (url: string): string => {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    return url
  }
}

// 清理URL（移除追踪参数等）
export const cleanUrl = (url: string): string => {
  try {
    const urlObj = new URL(url)
    
    // 移除常见的追踪参数
    const trackingParams = [
      'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
      'fbclid', 'gclid', 'ref', 'source', 'from'
    ]
    
    trackingParams.forEach(param => {
      urlObj.searchParams.delete(param)
    })
    
    return urlObj.toString()
  } catch {
    return url
  }
}

// 文本内容分段 - 将包含链接的文本分割为文本段和链接段
export interface TextSegment {
  type: 'text' | 'link'
  content: string
  url?: string
}

export const parseTextWithLinks = (text: string): TextSegment[] => {
  if (!text) return []
  
  const segments: TextSegment[] = []
  let lastIndex = 0
  
  // 重置正则表达式
  URL_REGEX.lastIndex = 0
  
  let match
  while ((match = URL_REGEX.exec(text)) !== null) {
    const url = match[0]
    const startIndex = match.index
    
    // 添加链接前的文本
    if (startIndex > lastIndex) {
      const textContent = text.substring(lastIndex, startIndex).trim()
      if (textContent) {
        segments.push({
          type: 'text',
          content: textContent
        })
      }
    }
    
    // 添加链接
    segments.push({
      type: 'link',
      content: url,
      url: url
    })
    
    lastIndex = startIndex + url.length
  }
  
  // 添加最后剩余的文本
  if (lastIndex < text.length) {
    const textContent = text.substring(lastIndex).trim()
    if (textContent) {
      segments.push({
        type: 'text',
        content: textContent
      })
    }
  }
  
  // 如果没有找到链接，返回整个文本作为文本段
  if (segments.length === 0) {
    segments.push({
      type: 'text',
      content: text
    })
  }
  
  return segments
}

// 检查链接是否应该显示预览
export const shouldShowPreview = (url: string): boolean => {
  // 不为图片、视频、音频、文档等直接媒体文件显示预览
  if (isImageUrl(url) || isVideoUrl(url) || isAudioUrl(url) || isDocumentUrl(url)) {
    return false
  }

  // 检查是否为有效的HTTP/HTTPS链接
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return false
  }

  // 检查URL长度，避免过长的URL
  if (url.length > 2000) {
    return false
  }

  // 检查是否为本地链接
  try {
    const urlObj = new URL(url)
    if (urlObj.hostname === 'localhost' || urlObj.hostname === '127.0.0.1' || urlObj.hostname.endsWith('.local')) {
      return false
    }
  } catch {
    return false
  }

  return true
}

// 格式化链接显示文本
export const formatLinkText = (url: string, maxLength: number = 50): string => {
  if (url.length <= maxLength) {
    return url
  }
  
  try {
    const urlObj = new URL(url)
    const domain = urlObj.hostname
    const path = urlObj.pathname + urlObj.search
    
    if (domain.length + 3 >= maxLength) {
      return domain.substring(0, maxLength - 3) + '...'
    }
    
    const remainingLength = maxLength - domain.length - 3
    if (path.length > remainingLength) {
      return domain + path.substring(0, remainingLength) + '...'
    }
    
    return domain + path
  } catch {
    return url.substring(0, maxLength - 3) + '...'
  }
}
