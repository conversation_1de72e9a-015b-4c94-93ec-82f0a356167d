<template>
  <div class="max-w-[260px]">
    <div class="file-box flex relative overflow-hidden items-start rounded-lg bg-gray-100 px-2 py-3 overflow-hidden">
      <div class="file-name flex gap-2 text-sm font-medium text-gray-900">
        <fileAvatar :type-avatar="fileNameVal" />
        <span style="min-width: 150px; word-break: break-all; margin-bottom: 15px">{{ props.dataItem.msg }}</span>
      </div>
      <MessageLoad :data="props.dataItem" />
      <div class="proress" v-if="props.dataItem.progress">
        <span v-if="props.dataItem.proStatus==1" class="tipsbox bg-red-500 text-white absolute z-50 left-1/2 top-1/2 text-xs py-0.5 px-1.5 flex items-center">
          上传失败
        </span>
        <el-progress type="circle" :width="50" :stroke-width="3" :percentage="props.dataItem.progress"></el-progress>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import MessageLoad from "./MessageLoad.vue";
import fileAvatar from "@/components/Avatar/FileAvatar.vue";
const fileNameVal = ref("");
const props = defineProps({
  dataItem: {
    type: Object,
    required: true
  }
});
const getFileExtensionWithDot = filename => {
  // 使用 lastIndexOf 找到最后一个点的位置
  const dotIndex = filename.lastIndexOf(".");
  // 如果找到了点并且它不是字符串的第一个字符，返回从该位置到字符串末尾的部分
  if (dotIndex > 0 && dotIndex < filename.length - 1) {
    return filename.substring(dotIndex);
  }
  // 如果没有找到点或者点在字符串的最后一个字符位置，返回空字符串
  return "";
};
onMounted(() => {
  fileNameVal.value = getFileExtensionWithDot(props.dataItem.msg);
});
</script>
<style lang="scss" scoped>
.proress {
  @apply w-full h-full bg-gray-100 bg-opacity-80 flex items-center justify-center rounded-lg absolute left-0 top-0;
}
.tipsbox{
  border-radius: 4px;
  margin-left: -30px; 
  margin-top: -10px;
}
</style>
