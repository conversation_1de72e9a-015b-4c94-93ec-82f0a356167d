# 文件上传组件使用指南

## 概述

本文档介绍了新增的文件上传组件功能，包括在AI应用配置中添加文件上传字段以及在AI聊天界面中渲染文件上传组件。

## 功能特性

### 1. 文件上传组件 (FileUpload.vue)

位置：`frontend/src/views/ai-chat/components/FileUpload.vue`

#### 主要特性：
- 支持单文件和多文件上传
- 文件类型验证
- 文件大小限制
- 文件预览和管理
- 拖拽上传支持（可扩展）
- 上传进度显示
- 错误处理

#### 组件属性：
```typescript
interface FileUploadProps {
  /** 接受的文件类型 */
  accept?: string
  /** 是否支持多文件 */
  multiple?: boolean
  /** 占位符文本 */
  placeholder?: string
  /** 最大文件大小（字节） */
  maxSize?: number
  /** 最大文件数量 */
  maxFiles?: number
}
```

#### 组件事件：
```typescript
interface FileUploadEmits {
  /** 文件选择事件 */
  (e: 'file-selected', files: File[]): void
  /** 文件上传事件 */
  (e: 'file-uploaded', result: any): void
  /** 错误事件 */
  (e: 'error', error: string): void
}
```

### 2. AI应用配置中的文件字段

在 `AiAppWidget.vue` 中，可以为AI应用添加文件上传字段：

```typescript
{
  label: '上传文档',
  name: 'uploadFile',
  class: 'form-control',
  value: '',
  type: 'file',
  accept: '.pdf,.doc,.docx,.txt,.md',
  multiple: false
}
```

#### 支持的文件字段属性：
- `type: 'file'` - 标识为文件上传字段
- `accept` - 接受的文件类型，如 `.pdf,.doc,.docx,.txt,.md`
- `multiple` - 是否支持多文件选择

### 3. AI聊天界面中的文件渲染

在 `ai-chat/index.vue` 中，文件上传字段会自动渲染为 `FileUpload` 组件：

```vue
<!-- 文件上传字段 -->
<div
  v-for="field in fileFields"
  :key="field.name"
  class="config-file-group"
>
  <label class="file-label">{{ field.label }}</label>
  <FileUpload
    :accept="field.accept"
    :multiple="field.multiple"
    :placeholder="`请选择${field.label}`"
    @file-selected="(files) => handleFileSelected(field.name, files)"
    @error="(error) => handleFileError(error)"
  />
</div>
```

## 使用示例

### 1. 在AI应用中添加文件上传字段

```typescript
// 在 AiAppWidget.vue 的应用配置中添加
{
  id: 2,
  name: '智能伴读',
  icon: blueBookIcon,
  config: {
    fields: [
      // 其他字段...
      {
        label: '上传文档',
        name: 'uploadFile',
        class: 'form-control',
        value: '',
        type: 'file',
        accept: '.pdf,.doc,.docx,.txt,.md',
        multiple: false
      }
    ]
  }
}
```

### 2. 处理文件选择事件

```typescript
const handleFileSelected = (fieldName: string, files: File[]) => {
  if (files.length > 0) {
    const file = files[0]
    // 更新配置字段值
    updateConfigField(fieldName, file.name)
    
    console.log(`文件选择: ${fieldName}`, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    })
    
    // 这里可以实现文件上传到服务器的逻辑
    // uploadFileToServer(file)
  }
}
```

### 3. 错误处理

```typescript
const handleFileError = (error: string) => {
  console.error('文件上传错误:', error)
  // 显示错误提示给用户
  // showErrorToast(error)
}
```

## 文件类型支持

组件支持以下文件类型的图标显示：

- 图片文件：`mdi:file-image`
- 视频文件：`mdi:file-video`
- 音频文件：`mdi:file-music`
- PDF文件：`mdi:file-pdf-box`
- Word文档：`mdi:file-word`
- Excel表格：`mdi:file-excel`
- PowerPoint演示：`mdi:file-powerpoint`
- 文本文件：`mdi:file-document`
- 其他文件：`mdi:file`

## 样式定制

文件上传组件使用了蓝橙渐变的设计风格，与整体UI保持一致：

```scss
.file-upload-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  // ...其他样式
}
```

## 扩展功能

### 1. 服务器上传

可以在 `handleFileSelected` 方法中添加实际的文件上传逻辑：

```typescript
const uploadFileToServer = async (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  
  try {
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    })
    
    const result = await response.json()
    return result
  } catch (error) {
    console.error('文件上传失败:', error)
    throw error
  }
}
```

### 2. 拖拽上传

可以扩展 `FileUpload` 组件支持拖拽上传：

```vue
<div
  class="drop-zone"
  @dragover.prevent
  @drop.prevent="handleDrop"
>
  <!-- 拖拽区域内容 -->
</div>
```

### 3. 上传进度

可以添加上传进度显示：

```vue
<div v-if="uploading" class="upload-progress">
  <div class="progress-bar" :style="{ width: `${uploadProgress}%` }"></div>
</div>
```

## 注意事项

1. 文件大小限制默认为10MB，可通过 `maxSize` 属性调整
2. 多文件模式下默认最多支持5个文件，可通过 `maxFiles` 属性调整
3. 文件类型验证基于文件扩展名和MIME类型
4. 组件暴露了 `clearFiles` 方法用于清空已选文件
5. 建议在生产环境中实现真实的文件上传服务器端逻辑

## 测试

可以通过以下步骤测试文件上传功能：

1. 启动前端开发服务器：`npm run dev`
2. 打开AI微应用，点击"智能伴读"应用
3. 在配置模式下查看文件上传组件
4. 选择文件并观察控制台输出
5. 验证文件信息是否正确传递到配置中
