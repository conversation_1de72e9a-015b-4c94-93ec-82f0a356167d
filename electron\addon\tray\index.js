const { Tray, Menu, ipcMain, app } = require("electron");
const path = require("path");
const Ps = require("ee-core/ps");
const Log = require("ee-core/log");
const Electron = require("ee-core/electron");
const CoreWindow = require("ee-core/electron/window");
const Conf = require("ee-core/config");
const EE = require("ee-core/ee");
const Services = require("ee-core/services");

/**
 * 托盘插件
 * @class
 */
class TrayAddon {
  constructor() {
    this.tray = null;
    this.blinkInterval = null; // 新增变量，控制闪烁间隔
    this.iconPathNormal = ""; // 正常图标路径
    this.iconPathBlink = ""; // 闪烁时使用的图标路径
    this.isBlinking = false; // 是否正在闪烁
  }

  /**
   * 创建托盘
   */
  create() {
    // 开发环境，代码热更新开启时，会导致托盘中有残影
    if (Ps.isDev() && Ps.isHotReload()) return;

    Log.info("[addon:tray] load");
    const { CoreApp } = EE;
    const cfg = Conf.getValue("addons.tray");
    const mainWindow = CoreWindow.getMainWindow();

    // 托盘图标
    this.iconPathNormal = path.join(Ps.getHomeDir(), cfg.icon);
    this.iconPathBlink = path.join(Ps.getHomeDir(), cfg.icon_empty); // 添加闪烁图标路径

    // 托盘菜单功能列表
    let trayMenuTemplate = [
      {
        label: "还原",
        click: function () {
          mainWindow.show();
        },
      },
      {
        label: "设置",
        click: () => this.showConfigWindow(),
      },
      {
        label: "重启",
        click: function () {
          app.relaunch();
          app.quit();
        },
      },
      {
        label: "退出",
        click: function () {
          CoreApp.appQuit();
        },
      },
      // {
      //   label: "开始闪烁",
      //   click: () => this.startBlinking(),
      // },
      // {
      //   label: "停止闪烁",
      //   click: () => this.stopBlinking(),
      // },

    ];
    // 点击关闭，最小化到托盘
    mainWindow.on("close", (event) => {
      if (Electron.extra.closeWindow == true) {
        return;
      }
      mainWindow.hide();
      event.preventDefault();
    });

    // 实例化托盘
    this.tray = new Tray(this.iconPathNormal);
    this.tray.setToolTip(cfg.title);
    const contextMenu = Menu.buildFromTemplate(trayMenuTemplate);
    this.tray.setContextMenu(contextMenu);
    this.tray.on("double-click", () => {
      mainWindow.show();
    });

    // 设置托盘闪烁监听
    ipcMain.on("message-start-blinking", (event, data) => {
      this.startBlinking();
    });
    ipcMain.on("message-stop-blinking", (event) => {
      this.stopBlinking();
    });
  }

  startBlinking() {
    if (this.blinkInterval) return; // 已经在闪烁中，防止重复启动

    const step = 450; // 保持闪烁图标毫秒
    this.isBlinking = true;
    if (this.tray) {
      const toggleTrayImg = () => {
        this.tray.setImage(
          this.isBlinking ? this.iconPathBlink : this.iconPathNormal
        );
        this.isBlinking = !this.isBlinking;
      };
      toggleTrayImg();
      this.blinkInterval = setInterval(() => {
        toggleTrayImg();
      }, step);
    }
  }

  stopBlinking() {
    if (this.blinkInterval) {
      this.isBlinking = false;
      clearInterval(this.blinkInterval);
      this.blinkInterval = null;
      // 停止闪烁后，重置为原始图标（可选）
      if (this.tray) {
        this.tray.setImage(this.iconPathNormal);
      }
    }
  }

  // 显示配置窗口
  showConfigWindow() {
    Services.get("os").showConfigWindow("#/config");
  }
}

TrayAddon.toString = () => "[class TrayAddon]";
module.exports = TrayAddon;
