<template>
  <el-dialog
    v-model="visible"
    width="500px"
    top="20vh"
    class="custom-dialog"
    center
    :show-close="false"
    :close-on-click-modal="false"
    append-to-body
  >
    <!-- Header -->
    <template #header>
      <button class="close-btn" @click="visible = false">
        <font-awesome-icon :icon="['fas', 'times']" />
      </button>
      <div class="flex items-center gap-2">
        <font-awesome-icon :icon="['fas', 'plus']" class="icon" />
        <span class="title">添加待办事项</span>
      </div>
    </template>

    <!-- Body -->
    <div class="input-container" :class="{ 'ai-generating': isGenerating }">
      <div class="input-wrapper">
        <el-input
          v-model="newTodo"
          ref="inputRef"
          placeholder="直接添加待办任务或者交给AI规划..."
          size="large"
          @keydown.ctrl.enter="addTodoAndClose"
          @keydown.escape="visible = false"
          :disabled="isGenerating"
          class="input-with-buttons"
        >
          <template #prefix>
            <Icon icon="solar:pen-new-square-bold" />
          </template>
          <template #suffix>
            <div class="input-buttons">
              <el-button 
                class="ai-button"
                @click="aiPlanTodo" 
                :disabled="!newTodo.trim() || isGenerating"
                size="small"
                text
              >
                <template v-if="isGenerating">
                  <div class="loading-spinner"></div>
                </template>
                <template v-else>
                  <font-awesome-icon :icon="['fas', 'wand-magic-sparkles']" class="magic-icon" />
                </template>
                {{ isGenerating ? '规划中...' : 'AI规划' }}
              </el-button>
              <el-button
                class="add-button"
                type="primary"
                @click="addTodoAndClose"
                :disabled="!newTodo.trim() || isGenerating"
                size="small"
              >
                <Icon icon="solar:add-circle-bold" />
                添加
              </el-button>
            </div>
          </template>
        </el-input>
      </div>
      
      <!-- AI 生成状态指示器 -->
      <div v-if="isGenerating && currentStatus" class="status-container">
        <div class="status-shimmer">
          <div class="status-content">
            <font-awesome-icon :icon="['fas', 'brain']" class="brain-icon" />
            <div class="status-text">{{ currentStatus }}</div>
          </div>
        </div>
      </div>

      <!-- 待办事项预览卡片 -->
      <div v-if="previewTodos.length > 0" class="preview-todos">
        <div class="preview-title">
          <Icon icon="solar:checklist-bold-duotone" class="text-green-500" />
          <span>待办事项预览</span>
        </div>
        <div class="todo-cards">
          <div 
            v-for="(todo, index) in previewTodos" 
            :key="index" 
            class="todo-card"
            :class="{ 'todo-card-generating': todo.isGenerating }"
          >
            <div class="todo-content">
              <font-awesome-icon :icon="['fas', 'circle-check']" class="todo-icon" />
              <span class="todo-text">{{ todo.content }}</span>
              <el-button
                class="add-todo-btn"
                type="success"
                size="small"
                text
                @click="addSingleTodo(todo.content, index)"
                :disabled="todo.isGenerating || todo.isAdded"
                :loading="todo.isAdding"
              >
                <template v-if="todo.isAdded">
                  <font-awesome-icon :icon="['fas', 'check']" class="check-icon" />
                  已添加
                </template>
                <template v-else>
                  <font-awesome-icon :icon="['fas', 'plus']" class="plus-icon" />
                  添加
                </template>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>


  </el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { Icon } from '@iconify/vue'
import { ElMessage } from 'element-plus'
import { sendAiStreamChatMessage, type AiStreamChatRequest } from '@/api/modules/ai/chat'
import { addTodo as apiAddTodo } from '@/api/modules/todos'

interface PreviewTodo {
  content: string
  isGenerating: boolean
  isAdded: boolean // 新增：是否已添加到数据库
  isAdding: boolean // 新增：添加过程中
}

const visible = ref(false)
const newTodo = ref('')
const isGenerating = ref(false)
const inputRef = ref()
const currentStatus = ref('')
const previewTodos = ref<PreviewTodo[]>([])

// 打开对话框
const open = () => {
  newTodo.value = ''
  currentStatus.value = ''
  previewTodos.value = []
  visible.value = true
  nextTick(() => {
    inputRef.value?.focus?.()
  })
}

// 暴露方法给父组件
defineExpose({
  open
})

// AI规划
const aiPlanTodo = () => {
  if (!newTodo.value.trim() || isGenerating.value) return
  
  isGenerating.value = true
  currentStatus.value = '正在连接AI...'
  previewTodos.value = []
  let resultText = ''
  
  const request: AiStreamChatRequest = {
    query: newTodo.value.trim(),
    app_id: 'todo',
    inputs: { context: '根据用户输入规划待办事项' },
    shouldProcessNodeStarted: true
  }
  
  sendAiStreamChatMessage(request, {
    onData: chunk => {
      // 处理状态更新
      if (chunk.type === 'node_started' && chunk.content) {
        currentStatus.value = chunk.content
        return
      }
      
      // 处理文本内容
      resultText += chunk.content
      
      // 检查是否包含待办事项标记
      if (chunk.content.includes('##TODOItem') || resultText.includes('##TODOItem')) {
        // 解析待办事项
        parseTodoItems(resultText)
      } else {
        // 更新输入框内容
        newTodo.value = resultText
      }
    },
    onError: err => {
      console.error('AI规划失败', err)
      ElMessage.error('AI规划失败，请稍后重试')
      isGenerating.value = false
      currentStatus.value = ''
      
      // 重置所有待办事项的生成状态
      previewTodos.value.forEach(todo => {
        todo.isGenerating = false
      })
    },
    onComplete: () => {
      isGenerating.value = false
      currentStatus.value = ''
      
      // 重置所有待办事项的生成状态
      previewTodos.value.forEach(todo => {
        todo.isGenerating = false
      })
      
      ElMessage.success('AI规划完成')
      nextTick(() => inputRef.value?.focus?.())
    }
  })
}

// 解析待办事项
const parseTodoItems = (text: string) => {
  try {
    const newTodos: PreviewTodo[] = []
    
    // 匹配 ##TODOItem: 格式的待办事项
    const todoItemRegex = /##TODOItem:\s*(.+?)(?=##TODOItem:|$)/gs
    const matches = text.matchAll(todoItemRegex)
    
    for (const match of matches) {
      const todoContent = match[1].trim()
      if (todoContent) {
        // 处理多行内容，去除空行
        const lines = todoContent.split('\n').filter(line => line.trim())
        for (const line of lines) {
          const cleanContent = line.trim()
          // 去除可能的列表标记
          const content = cleanContent.replace(/^[-*+•]\s*/, '').replace(/^\d+\.\s*/, '')
          if (content && !content.startsWith('#')) {
            newTodos.push({
              content: content,
              isGenerating: false,
              isAdded: false, // 初始化为未添加
              isAdding: false // 初始化为未添加中
            })
          }
        }
      }
    }
    
    // 如果没有找到 ##TODOItem: 格式，尝试传统解析方式
    if (newTodos.length === 0) {
      const lines = text.split('\n')
      let inTodoSection = false
      
      for (const line of lines) {
        if (line.includes('##TODOItem')) {
          inTodoSection = true
          // 检查是否是 ##TODOItem: 格式并直接包含内容
          const directMatch = line.match(/##TODOItem:\s*(.+)/)
          if (directMatch && directMatch[1].trim()) {
            newTodos.push({
              content: directMatch[1].trim(),
              isGenerating: false,
              isAdded: false, // 初始化为未添加
              isAdding: false // 初始化为未添加中
            })
          }
          continue
        }
        
        if (inTodoSection && line.trim()) {
          // 匹配各种待办事项格式
          const todoMatch = line.match(/^[-*+•]\s+(.+)$/) || 
                           line.match(/^\d+\.\s+(.+)$/) ||
                           line.match(/^(.+)$/)
          
          if (todoMatch && todoMatch[1]) {
            const content = todoMatch[1].trim()
            if (content && !content.startsWith('#')) {
              newTodos.push({
                content: content,
                isGenerating: false,
                isAdded: false, // 初始化为未添加
                isAdding: false // 初始化为未添加中
              })
            }
          }
        }
      }
    }
    
    // 添加生成中的效果
    if (newTodos.length > 0) {
      const lastTodo = newTodos[newTodos.length - 1]
      if (lastTodo && !lastTodo.content.endsWith('。') && !lastTodo.content.endsWith('.')) {
        lastTodo.isGenerating = true
      }
    }
    
    previewTodos.value = newTodos
    
    // 如果有待办事项，清空输入框
    if (newTodos.length > 0) {
      newTodo.value = ''
    }
  } catch (error) {
    console.error('解析待办事项失败:', error)
  }
}

// 定义事件
const emit = defineEmits<{
  todoAdded: []
}>()

// 添加待办
const addTodoAndClose = async () => {
  if (!newTodo.value.trim() || isGenerating.value) return
  try {
    await apiAddTodo({ content: newTodo.value.trim() })
    ElMessage.success('待办事项添加成功')
    visible.value = false
    // 通知父组件刷新待办列表
    emit('todoAdded')
  } catch (err) {
    console.error(err)
    ElMessage.error('添加失败，请稍后重试')
  }
}

// 添加单个待办事项
const addSingleTodo = async (content: string, index: number) => {
  const todo = previewTodos.value[index];
  if (todo.isGenerating || todo.isAdded || todo.isAdding) return;

  todo.isAdding = true;
  try {
    await apiAddTodo({ content: content });
    ElMessage.success('待办事项添加成功');
    todo.isAdded = true;
    todo.isAdding = false;
    // 通知父组件刷新待办列表
    emit('todoAdded');
  } catch (err) {
    console.error(err);
    ElMessage.error('添加失败，请稍后重试');
    todo.isAdding = false;
  }
};
</script>

<style lang="scss" scoped>


@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 输入容器样式
.input-container {
  .input-wrapper {
    // 输入框通用样式
    // 圆角
    :deep(.el-input__wrapper) {
      border-radius: 12px !important;
    }

    // 获得焦点时边框加粗
    :deep(.el-input__wrapper.is-focus) {
      border: 1px solid var(--el-color-primary) !important;
    }

    // AI生成时的特殊样式
    :deep(.el-input.is-disabled .el-input__wrapper) {
      opacity: 0.6;
    }
    
    // 输入框中的按钮样式
    .input-buttons {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: flex-end;
      margin-right: 2px;
      
      .ai-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        background-size: 300% 300%;
        border: none;
        color: white !important;
        font-weight: 500;
        font-size: 12px;
        padding: 4px 12px;
        height: 28px;
        border-radius: 8px;
        transition: all 0.3s ease;
        animation: gradientShift 3s ease-in-out infinite;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          animation-duration: 1.5s;
        }
        
        .magic-icon {
          margin-right: 4px;
          font-size: 12px;
          transition: transform 0.3s ease;
        }
        
        .loading-spinner {
          margin-right: 4px;
          width: 12px;
          height: 12px;
          border: 1.5px solid rgba(255, 255, 255, 0.3);
          border-top: 1.5px solid white;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        
        &:hover .magic-icon {
          transform: rotate(15deg) scale(1.1);
        }
        
        &.is-disabled {
          opacity: 0.6;
          transform: none;
          box-shadow: none;
        }
      }
      
      .add-button {
        font-size: 12px;
        padding: 4px 12px;
        height: 28px;
        border-radius: 8px;
        font-weight: 500;
        
        :deep(.el-icon) {
          margin-right: 4px;
          font-size: 12px;
        }
      }
    }
  }
  
  // AI生成时的效果
  &.ai-generating .input-wrapper {
    :deep(.el-input__wrapper) {
      border-color: var(--el-color-primary) !important;
      box-shadow: 0 0 0 1px var(--el-color-primary-light-8) !important;
    }
  }
}

// 状态显示容器
.status-container {
  margin-top: 16px;
  
  .status-shimmer {
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    position: relative;
    
    .status-content {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .brain-icon {
        font-size: 16px;
        color: #8b5cf6;
        animation: brainPulse 2s ease-in-out infinite;
      }
      
      .status-text {
        font-size: 13px;
        color: transparent;
        font-weight: 500;
        background: linear-gradient(90deg, #667eea 25%, #764ba2 50%, #f093fb 75%);
        background-size: 200% 100%;
        -webkit-background-clip: text;
        background-clip: text;
        animation: textShimmer 2s ease-in-out infinite;
      }
    }
  }
}

.shimmer-animation {
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes textShimmer {
  0%, 100% {
    background-position: -200% 0;
  }
  50% {
    background-position: 200% 0;
  }
}

@keyframes brainPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

// 待办事项预览样式
.preview-todos {
  margin-top: 16px;
  
  .preview-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 600;
    color: #475569;
  }
  
  .todo-cards {
    space-y: 8px;
    
    .todo-card {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 12px 16px;
      margin-bottom: 8px;
      transition: all 0.3s ease;
      
      &.todo-card-generating {
        // 为文本添加 shimmer 效果
        .todo-content .todo-text {
          position: relative;
          color: transparent !important;
          background: linear-gradient(90deg, #e2e8f0 25%, #cbd5e1 50%, #e2e8f0 75%);
          background-size: 200% 100%;
          -webkit-background-clip: text;
          background-clip: text;
          animation: shimmer 2s infinite;
        }
        // 隐藏原先的骨架/加载指示器
        .generating-indicator {
          display: none;
        }
      }
      
      &:hover {
        background: #f1f5f9;
        border-color: #cbd5e1;
      }
      
      &.todo-card-generating {
        background: linear-gradient(135deg, #987ffe 0%, #a78bfa 100%);
        border-color: #8b5cf6;
      }
      
      .todo-content {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .todo-icon {
          color: #10b981;
          font-size: 16px;
          flex-shrink: 0;
        }
        
        .todo-text {
          font-size: 14px;
          color: #374151;
          line-height: 1.4;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .add-todo-btn {
          flex-shrink: 0;
          background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
          background-size: 300% 300%;
          border: none;
          color: white !important;
          font-weight: 500;
          font-size: 12px;
          padding: 4px 12px;
          height: 28px;
          border-radius: 8px;
          transition: all 0.3s ease;
          animation: gradientShift 3s ease-in-out infinite;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(66, 213, 108, 0.3);
            animation-duration: 1.5s;
          }

          .plus-icon {
            margin-right: 4px;
            font-size: 12px;
            transition: transform 0.3s ease;
          }

          .check-icon {
            margin-right: 4px;
            font-size: 12px;
            color: white;
          }

          &.is-disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
          }
        }
      }

      
      
      .generating-indicator {
        margin-top: 8px;
        display: flex;
        justify-content: center;
        
        .dots {
          display: flex;
          gap: 4px;
          
          span {
            width: 6px;
            height: 6px;
            background: #8b5cf6;
            border-radius: 50%;
            animation: dotPulse 1.4s ease-in-out infinite both;
            
            &:nth-child(1) { animation-delay: -0.32s; }
            &:nth-child(2) { animation-delay: -0.16s; }
            &:nth-child(3) { animation-delay: 0s; }
          }
        }
      }
    }
  }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 暗色模式适配
:global(.dark) {
  .status-shimmer {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-color: #475569;
    
    .status-content {
      .brain-icon {
        color: #a855f7;
      }
      
      .status-text {
        background: linear-gradient(90deg, #8b5cf6 25%, #a855f7 50%, #c084fc 75%);
        background-size: 200% 100%;
        -webkit-background-clip: text;
        background-clip: text;
      }
    }
  }
  
  .preview-todos {
    .preview-title {
      color: #cbd5e1;
    }
    
    .todo-card {
      background: #1e293b;
      border-color: #475569;
      
      &:hover {
        background: #334155;
        border-color: #64748b;
      }
      
      &.todo-card-generating {
        background: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%);
        border-color: #7c3aed;
      }
      
      .todo-content .todo-text {
        color: #e2e8f0;
      }
    }
  }
}
</style>
