import { defineStore } from "pinia";
import { Talk } from "../interface/index";
import piniaPersistConfig from "../helper/persist";
import * as talkApi from "@/api/modules/talk";
import { useRecentStore } from "./recent";

export const useTalkStore = defineStore("lark-talk", {
  state: (): Talk => ({
    activeChatId: "",
    activeContact: {},
    filterWords: [],
    createGroup: {},
    commonEmoStr:
      "[K歌],[NO],[OK],[爱你],[爱情],[爱心],[傲慢],[白眼],[棒棒糖],[抱拳],[爆筋],[鄙视],[闭嘴],[鞭炮],[便便],[擦汗],[菜刀],[差劲],[钞票],[车头],[车尾],[车厢],[呲牙],[打哈欠],[大兵],[大哭],[蛋糕],[刀],[得意],[灯笼],[电灯泡],[凋谢],[发财],[发呆],[发抖],[发怒],[饭],[飞机],[飞吻],[奋斗],[风车],[尴尬],[勾引],[购物],[鼓掌],[害羞],[憨笑],[喝彩],[喝奶],[坏笑],[挥手],[回头],[饥饿],[激动],[街舞],[戒指],[惊恐],[惊讶],[咖啡],[磕头],[可爱],[可怜],[抠鼻],[骷髅],[酷],[快哭了],[困],[篮球],[冷汗],[礼物],[流汗],[流泪],[玫瑰],[面],[难过],[闹钟],[怄火],[啤酒],[瓢虫],[撇嘴],[乒乓],[祈祷],[气球],[汽车],[强],[敲打],[亲亲],[青蛙],[晴天],[糗大了],[拳头],[弱],[色],[沙发],[闪电],[胜利],[示爱],[手枪],[衰],[双喜],[睡],[太阳],[调皮],[跳绳],[跳跳],[偷笑],[吐],[微笑],[委屈],[握手],[西瓜],[下雨],[吓],[献吻],[香蕉],[象棋],[心碎],[信],[熊猫],[嘘],[药],[疑问],[阴险],[拥抱],[右哼哼],[右太极],[雨伞],[月亮],[晕],[再见],[炸弹],[招财猫],[折磨],[纸巾],[咒骂],[猪头],[抓狂],[转圈],[足球],[左哼哼],[左太极]",
    commonEmoji: {},
    casicEmoStr:
      "保密,抱抱,比心,出差,对不起,发快递,归零,国家利益高于一切,哈哈,嗨,好的,哼,加油,甲方爸爸,哭,快舟镖局,嗯嗯,你好,前方高能,闪人,什么,誓保完成任务,天网恢恢疏而不漏,偷笑,哇,晚安,无语,捂脸,谢谢,辛苦,一见倾心,再见",
    casicEmoji: {},
    approveRole: [],
    ifChat: false,
    ifContact: false,
    quoteMsg: {},
    isBol: false,
    isBolStatus: false,
    isStatus: false
  }),
  getters: {
    commonEmojiList: state => state.commonEmoStr.split(","),
    casicEmojiList: state => state.casicEmoStr.split(",")
  },
  actions: {
    // 获取敏感词数据
    async getFilterWords() {
      const res = await talkApi.getFilterWords();
      this.filterWords = res.data.list;
    },
    // 创建新群组
    setNewGroup(obj: object) {
      this.createGroup = obj;
    },
    // 设置当前会话
    setActiveChat(id: string) {
      if (id) {
        const recentStore = useRecentStore();
        this.activeChatId = id;
        this.activeContact = recentStore.listRecents.find((item: any) => item.contactId == id) || {};
      } else {
        this.activeChatId = "";
        this.activeContact = {};
      }
    },
    // 修改群组名称后更新当前会话
    updateActiveChat(name: string) {
      this.activeContact.contactName = name;
    },
    // 获取审批角色
    async getApproveRole() {
      const { data } = await talkApi.getApproveRole();
      this.approveRole = data.list;
    },
    // 设置引用消息
    setQuoteMsg(obj?: any) {
      if (obj) {
        this.quoteMsg = obj;
      } else {
        this.quoteMsg = {};
      }
    },
    // 设值转发弹框显示
    setDialogBol(val: any) {
      if (val) {
        this.isBol = val;
      } else {
        this.isBol = false;
      }
    },
    setDialogBolStatus(val: any) {
      if (val) {
        this.isBolStatus = val;
      } else {
        this.isBolStatus = false;
      }
    },
    setStatus(val: any) {
      if (val) {
        this.isStatus = val;
      } else {
        this.isStatus = false;
      }
    },
    getCommonEmoji() {
      // 动态导入 /src/assets/face 目录下所有 PNG 文件
      const emojiModules = import.meta.glob("@/assets/face/*.png", { eager: true, import: "default" });

      const facesMap: any = {};

      Object.keys(emojiModules).forEach(path => {
        // 获取文件名（不含扩展名）
        const fileName = path.split("/").pop()?.split(".")[0];
        if (fileName) {
          facesMap[fileName] = emojiModules[path];
        }
      });

      this.commonEmoji = facesMap;
    },
    getCasicEmoji() {
      // 动态导入 /src/assets/face 目录下所有 PNG 文件
      const emojiModules = import.meta.glob("@/assets/face/casic/*.gif", { eager: true, import: "default" });

      const facesMap: any = {};

      Object.keys(emojiModules).forEach(path => {
        // 获取文件名（不含扩展名）
        const fileName = path.split("/").pop()?.split(".")[0];
        if (fileName) {
          facesMap[fileName] = emojiModules[path];
        }
      });

      this.casicEmoji = facesMap;
    }
  },
  persist: piniaPersistConfig("lark-talk-session")
});
