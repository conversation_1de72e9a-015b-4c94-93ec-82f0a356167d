{"logger": {"output_json": false, "level": "info", "filename": "ee-go.log", "max_size": 1024, "max_age": 10}, "core_logger": {"output_json": false, "level": "info", "filename": "ee-go-core.log", "max_size": 1024, "max_age": 10}, "http": {"enable": false, "port": 7073, "network": false}, "static": {"enable": true, "package": "public/package.json", "config": "public/config", "dist": "public/dist", "html": "public/html", "ssl": "public/ssl"}}