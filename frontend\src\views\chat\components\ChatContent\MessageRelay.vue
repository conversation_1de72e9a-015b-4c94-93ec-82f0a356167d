<template>
  <div class="vote-box rounded-lg border border-gray-200 bg-white shadow-md p-4 hover:shadow-lg transition-shadow duration-300">
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center space-x-2 mr-2">
        <span
          class="head flex items-center gap-1 font-medium text-green-700 hover:bg-green-100 rounded-md px-2 py-1 text-xs bg-green-50"
        >
          <font-awesome-icon :icon="['fas', 'list']" class="text-xs" />
          接龙
        </span>
        <div class="ongoing text-xs px-2 py-0.5 rounded font-medium bg-gray-50 text-gray-700" :class="{ active: isActive }">
          {{ isActive ? "进行中" : "已结束" }}
        </div>
      </div>
      <div class="time flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 bg-gray-50 px-2 py-0.5 rounded-md">
        <font-awesome-icon :icon="['fas', 'clock']" class="text-xs" />
        截止: {{ dayjs(relayInfo.deadline).format("YYYY-MM-DD HH:mm") }}
      </div>
    </div>
    <h3 class="title mb-2 text-base font-bold text-gray-900 hover:text-green-600 cursor-pointer transition-colors duration-200">
      {{ relayInfo.title }}
    </h3>
    <p class="description mb-3 text-sm text-gray-600 bg-white/80 p-2 rounded border-l-2 border-green-300">
      {{ relayInfo.description }}
    </p>
    <div class="record mb-3 p-2.5 bg-green-50 rounded">
      <div class="flex items-center justify-between mb-1.5">
        <div class="record-info flex items-center text-sm text-green-700">
          <font-awesome-icon :icon="['fas', 'users']" class="mr-1 text-xs" />
          参与情况
        </div>
        <span class="record-num text-xs font-medium text-green-700 bg-green-100 px-1.5 py-0.5 rounded">
          {{ relayInfo.participantRespVOList?.length ?? 0 }}/{{ relayInfo.maxParticipants }}
        </span>
      </div>
      <el-progress :percentage="percentage" :stroke-width="6" :show-text="false"></el-progress>
      <div class="record-status mt-1 flex justify-between">
        <span class="text-xs text-green-700">{{ percentage }}% 已完成</span>
        <span class="text-xs text-green-700">
          还剩 {{ relayInfo.maxParticipants - relayInfo.participantRespVOList?.length }} 个名额
        </span>
      </div>
    </div>
    <div class="record-1 mb-3 grid grid-cols-2 gap-2 bg-white/80 p-2 rounded">
      <div class="flex items-center text-xs text-gray-600">
        <font-awesome-icon :icon="['fas', 'user']" class="mr-1 text-xs text-green-500" />
        已参与: {{ relayInfo.participantRespVOList?.length ?? 0 }}人
      </div>
      <div class="flex items-center text-xs text-gray-600">
        <font-awesome-icon :icon="['fas', 'calendar']" class="mr-1 text-xs text-green-500" />
        创建于: {{ dayjs(relayInfo.createdAt ?? 0).format("MM-DD") }}
      </div>
    </div>
    <div class="mb-4 flex flex-wrap gap-1.5">
      <span
        v-for="item in relayInfo._tags"
        class="relay-tag inline-flex items-center rounded-md bg-purple-50 px-2 py-1 text-xs font-medium text-purple-700"
        >{{ item }}</span
      >
    </div>
    <div class="relay-list mb-4 border border-gray-200 rounded-lg overflow-hidden">
      <div class="relay-list-head bg-green-50 p-2.5 flex justify-between items-center">
        <h4 class="text-sm font-medium text-green-700">参与者列表</h4>
        <span class="text-xs text-green-600">{{ relayInfo.participantRespVOList?.length }} 位参与者</span>
      </div>
      <div class="relay-list-box divide-y divide-gray-100 max-h-48 overflow-y-auto">
        <div
          v-for="item in relayInfo.participantRespVOList"
          class="relay-list-item flex items-start space-x-2.5 p-3 bg-white transition-colors duration-200"
        >
          <DynamicAvatar :id="item.creator" :data-info="item" :relation-name="item.creatorName" :size="20" :type-avatar="0" />
          <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between mb-1">
              <span class="item-user text-xs font-medium text-gray-900">{{ item.creatorName }}</span>
              <span class="item-time text-xs text-gray-500 whitespace-nowrap ml-2">{{
                dayjs(item.updateTime).format("HH:mm")
              }}</span>
            </div>
            <p class="item-info text-xs text-gray-600 truncate max-w-full">{{ item.content }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="flex items-center space-x-2">
      <DynamicAvatar
        :id="relayInfo.creatorId"
        :data-info="relayInfo"
        :relation-name="relayInfo.creatorName"
        :size="24"
        :type-avatar="0"
      />
      <span className="vote-by text-xs text-gray-500">由 {{ relayInfo.creatorName }} 创建</span>
    </div>
    <div class="mt-3">
      <button
        v-if="!isActive"
        class="w-full flex items-center justify-center gap-1 px-3 py-2 text-sm font-medium text-gray-300 bg-gray-50 rounded"
      >
        已结束
      </button>
      <button
        v-else
        @click="goRelay(relayInfo)"
        class="w-full flex items-center justify-center gap-1 px-3 py-2 text-sm font-medium text-white bg-green-600 rounded hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200"
      >
        <template v-if="isParticipate">
          <font-awesome-icon :icon="['fas', 'edit']" class="text-xs" />
          修改
        </template>
        <template v-else>
          <font-awesome-icon :icon="['fas', 'plus-circle']" class="text-xs" />
          参与接龙
        </template>
      </button>
    </div>
  </div>

  <el-dialog v-model="state.visible" :show-close="false" :append-to-body="true" class="custom-dialog">
    <template #header>
      <button class="close-btn" @click="closeDialog">
        <font-awesome-icon :icon="['fas', 'times']" />
      </button>
      <div class="flex items-center gap-2">
        <span class="title">参与接龙：{{ relayInfo.title }}</span>
      </div>
    </template>
    <div>
      <div class="mb-4 p-3 bg-purple-50 rounded-lg dark:bg-purple-900/20">
        <p class="text-sm text-purple-800 dark:text-purple-200">请输入您的接龙内容，内容将展示在接龙卡片中。</p>
      </div>
    </div>
    <div>
      <el-form
        :model="state.relayContentForm"
        :rules="rules"
        label-position="top"
        require-asterisk-position="right"
        ref="ruleForm"
      >
        <el-form-item label="接龙内容" prop="content">
          <el-input
            v-model="state.relayContentForm.content"
            type="textarea"
            show-word-limit
            :maxlength="200"
            :autosize="{ minRows: 4, maxRows: 4 }"
            placeholder="请输入您的接龙内容..."
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex w-full items-center justify-end space-x-3">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="danger" v-if="state.relayContentForm.id" @click="deleteRelay(state.relayContentForm.id)">
          删除接龙
        </el-button>
        <el-button type="primary" @click="submitContentForm(ruleForm)">提交接龙</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts" name="MessageRelay">
import { ref, reactive, computed, watch } from "vue";
import { useHistoryStore } from "@/stores/modules/history";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import { creatRelayParticipant, deleteRelayParticipant, updateRelayParticipant } from "@/api/modules/contact";
import { ElMessage, FormInstance, ElMessageBox } from "element-plus";
import dayjs from "dayjs";
import WebSocketClient from "@/utils/websocket/websocketClient";
import { MessageCode } from "@/utils/websocket/messageInfo";
import { sendMessage } from "@/utils/websocket/messageService";
import { getBusinessList } from "@/api/modules/business";

const ruleForm = ref<any>();

const talkStore = useTalkStore();
const historyStore = useHistoryStore();
const userStore = useUserStore();

const props = defineProps({
  relayId: {
    type: String,
    required: true
  }
});
type relayInfoType = {
  id: string;
  deadline: number;
  createdAt: number;
  title: string;
  description: string;
  creatorId: string;
  creatorName: string;
  maxParticipants: number;
  tags: string;
  _tags: string[];
  participantRespVOList: any[];
};
const state = reactive<{
  visible: boolean;
  relayContentForm: {
    id: string;
    content: string;
    creatorId: string;
    time: string;
    relayId: string;
  };
  sendMessageContent: any;
}>({
  visible: false,
  relayContentForm: {
    id: "",
    relayId: props.relayId,
    creatorId: userStore.userId,
    content: "",
    time: ""
  },
  sendMessageContent: {}
});

const rules = ref({
  content: [{ required: true, message: "请输入您的接龙内容...", trigger: "change" }]
});

watch(
  () => props.relayId,
  async val => {
    await historyStore.getRelayInfo(val);
  },
  { immediate: true }
);

const relayInfo = computed<relayInfoType>(() => {
  const defaultData = { participantRespVOList: [], id: '', creatorId: '' }
  if (!props.relayId) return defaultData;
  const info = historyStore.relayList[props.relayId] || defaultData;
  if (info?.tags) {
    info._tags = info.tags.split(",");
  }
  return info;
});

let percentage = computed(() => {
  let p = (relayInfo.value.participantRespVOList?.length ?? 0) / (relayInfo.value.maxParticipants || 1);
  return Math.min(Math.round(p * 100), 100);
});

let isParticipate = computed(() => relayInfo.value.participantRespVOList?.some(item => item.creatorId == userStore.userId));

let isActive = computed(() => {
  const currentTimestamp = dayjs().valueOf();
  let isActive = currentTimestamp < relayInfo.value.deadline;

  if (isActive) {
    if (!isParticipate.value && percentage.value == 100) {
      isActive = false;
    }
  }
  return isActive;
});

const goRelay = ({ deadline }: relayInfoType) => {
  const currentTimestamp = dayjs().valueOf();

  state.relayContentForm.content = "";
  state.relayContentForm.id = "";

  if (currentTimestamp > deadline || (!isParticipate.value && percentage.value == 100)) {
    ElMessage.warning("接龙已结束");
    return;
  } else if (isParticipate.value) {
    let { content = "", id = "" } = relayInfo.value.participantRespVOList?.find(item => item.creatorId == userStore.userId);
    state.relayContentForm.content = content;
    state.relayContentForm.id = id;
  }

  state.visible = true;
};

const wsClient = WebSocketClient.getInstance();
const submitContentForm = (ruleForm: FormInstance | null) => {
  if (!ruleForm) return;

  ruleForm.validate(async (valid: boolean) => {
    if (valid) {
      state.relayContentForm.time = dayjs().format("YYYY-MM-DD HH:mm:ss");
      let res: any;

      if (isParticipate.value) {
        res = await updateRelayParticipant(state.relayContentForm);
      } else {
        res = await creatRelayParticipant(state.relayContentForm);
      }

      if (res.code == 0) {
        const { relayId, creatorId, content, time, id = "" } = state.relayContentForm;
        let setData: any = {
          content: content,
          creatorId,
          id,
          time: dayjs(time).valueOf(),
          updateTime: dayjs(time).valueOf(),
          updater: creatorId,
          relayId: relayId
        };

        if (!id) {
          Object.assign(setData, {
            avatar: userStore.avatar,
            createTime: dayjs(time).valueOf(),
            creator: creatorId,
            creatorName: userStore.name,
            id: res.data
          });
        }

        try {
          await sendMessage(wsClient!, {
            code: MessageCode.PARTICIPATES_RELAY,
            receiverId: talkStore.activeChatId,
            isGroup: true,
            content: {
              ...setData
            }
          });
          getActivityData();
        } catch (error: any) {
          ElMessage.error(error.message);
        }

        state.visible = false;
      }

      ElMessage.success("接龙成功");
    }
  });
};

const deleteRelay = (id: string) => {
  ElMessageBox.confirm("确认删除？", "温馨提示", {
    type: "warning"
  }).then(async () => {
    let res = await deleteRelayParticipant(id);
    if (res.code == 0) {
      try {
        await sendMessage(wsClient!, {
          code: MessageCode.PARTICIPATES_RELAY,
          receiverId: talkStore.activeChatId,
          isGroup: true,
          content: {
            id,
            eventType: "delete",
            relayId: props.relayId
          }
        });

        getActivityData();
      } catch (error: any) {
        ElMessage.error(error.message);
      }
    }

    state.visible = false;
  });
};

const getActivityData = async () => {
  const res: any = await getBusinessList(talkStore.activeChatId);
  if (res.code == 0) {
    historyStore.activity = res.data;
  }
};

watch(
  () => state.visible,
  newVal => {
    if (!newVal) {
      state.relayContentForm.content = "";
    }
    if (ruleForm.value) ruleForm.value.clearValidate();
  }
);

const closeDialog = () => {
  state.visible = false;
};
</script>

<style scoped lang="scss">
.vote-box {
  background: rgb(233, 255, 229) !important;
  border-color: rgb(195, 254, 196) !important;
  :deep(.el-progress) {
    .el-progress-bar__outer {
      @apply bg-gray-200;
    }
    .el-progress-bar__inner {
      @apply bg-green-600;
    }
  }
}
.dark {
  .vote-box {
    background: rgba(30, 58, 138, 0.05) !important;
    border-color: rgba(29, 78, 216, 0.2) !important;
    @apply border-gray-700 bg-gray-800;
    .head {
      @apply text-purple-900 hover:bg-purple-300 bg-purple-900/40;
    }
    .ongoing {
      @apply bg-gray-700 text-gray-300;
      &.active {
        @apply bg-green-900/40 text-green-300;
      }
    }
    .time {
      @apply bg-gray-700;
    }
    .title {
      @apply text-white hover:text-purple-300;
    }
    .description {
      @apply text-gray-300 bg-gray-700/40 border-purple-700;
    }
    .record {
      @apply bg-purple-900/20;
      .record-info {
        @apply text-purple-400;
      }
      .record-num {
        @apply text-purple-400 bg-purple-900/40;
      }
      .record-status {
        span {
          @apply text-green-400;
        }
      }
    }
    :deep(.el-progress) {
      .el-progress-bar__outer {
        @apply bg-gray-700;
      }
      .el-progress-bar__inner {
        @apply bg-green-500;
      }
    }
    .record-1 {
      @apply bg-gray-700/30;
      div {
        @apply text-gray-400;
        svg {
          @apply text-purple-400;
        }
      }
    }
    .relay-tag {
      @apply bg-purple-900/30 text-purple-400;
    }
    .relay-list {
      @apply border-gray-700;
      .relay-list-head {
        @apply bg-purple-900/20;
        h4,
        span {
          @apply text-purple-400;
        }
      }
      .relay-list-box {
        @apply divide-gray-700;
        .relay-list-item {
          @apply bg-gray-700/40;
          .item-user {
            @apply text-white;
          }
          .item-time {
            @apply text-gray-400;
          }
          .item-info {
            @apply text-gray-300;
          }
        }
      }
    }
    .vote-br {
      @apply border-gray-700;
    }
    .vote-by {
      @apply text-gray-400;
    }
    button {
      @apply bg-purple-700 hover:bg-purple-600;
    }
  }
}
</style>
