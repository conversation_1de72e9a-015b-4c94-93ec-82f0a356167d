<template>
  <div class="widget-container h-full">
    <!-- 悬浮标题和按钮容器 -->
    <div class="absolute top-0 left-4 -translate-y-1/2 flex items-center gap-2 z-10">
      <!-- 小组件标题 -->
      <div
        class="widget-title bg-white dark:bg-gray-800 px-3 py-1.5 rounded-full shadow-md border border-gray-200 dark:border-gray-700 flex items-center gap-2"
      >
        <Icon :icon="chartSquareBoldDuotone" class="text-lg text-purple-500" />
        <span class="text-xs font-bold text-gray-700 dark:text-gray-200">数据统计</span>
      </div>
      <!-- 刷新按钮 -->
      <button
        @click="refreshStats"
        :disabled="isLoading"
        class="w-7 h-7 rounded-full bg-purple-500 text-white flex items-center justify-center hover:bg-purple-600 transition-colors shadow-md disabled:opacity-50"
        title="刷新数据"
      >
        <font-awesome-icon :icon="['fas', 'sync-alt']" class="w-4 h-4" :class="{ 'fa-spin': isLoading }" />
      </button>
    </div>

    <!-- 内容区域 -->
    <div class="p-3 pt-8 h-full flex flex-col">
      <div v-if="isLoading" class="flex items-center justify-center flex-1">
        <div class="text-center text-gray-500 dark:text-gray-400">
          <font-awesome-icon :icon="['fas', 'fa-spinner']" class="text-2xl fa-spin mb-2" />
          <p class="text-sm">加载中...</p>
        </div>
      </div>
      
      <div v-else class="grid grid-cols-2 gap-1.5 flex-1 min-h-0 overflow-hidden">
        <!-- 会议数量卡片 - 使用AI统计数据 -->
        <div 
          class="stats-card bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-2xl p-3 border border-orange-200/50 dark:border-orange-700/30 shadow-sm hover:shadow-lg transition-shadow duration-200 cursor-pointer"
          @click="handleMeetingClick"
          title="点击查看会议详情"
        >
          <div class="flex flex-col h-full">
            <div class="flex items-center gap-2 mb-2">
              <Icon :icon="usersGroupTwoRoundedBoldDuotone" class="text-lg text-orange-500" />
              <span class="text-sm font-semibold text-gray-700 dark:text-gray-200 truncate">会议</span>
            </div>
            <div class="flex-1 flex items-end">
              <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {{ aiNoticeStore.meetingStats }}
              </div>
            </div>
          </div>
        </div>

        <!-- 公文数量卡片 - 使用AI统计数据 -->
        <div 
          class="stats-card bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-2xl p-3 border border-purple-200/50 dark:border-purple-700/30 shadow-sm hover:shadow-lg transition-shadow duration-200 cursor-pointer"
          @click="handleDocumentClick"
          title="点击查看公文详情"
        >
          <div class="flex flex-col h-full">
            <div class="flex items-center gap-2 mb-2">
              <Icon :icon="documentTextBoldDuotone" class="text-lg text-purple-500" />
              <span class="text-sm font-semibold text-gray-700 dark:text-gray-200 truncate">公文</span>
            </div>
            <div class="flex-1 flex items-end">
              <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {{ aiNoticeStore.documentStats }}
              </div>
            </div>
          </div>
        </div>

        <!-- 邮件数量卡片 - 使用AI统计数据 -->
        <div 
          class="stats-card bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-3 border border-blue-200/50 dark:border-blue-700/30 shadow-sm hover:shadow-lg transition-shadow duration-200 cursor-pointer"
          @click="handleEmailClick"
          title="点击查看邮件详情"
        >
          <div class="flex flex-col h-full">
            <div class="flex items-center gap-2 mb-2">
              <Icon :icon="letterBoldDuotone" class="text-lg text-blue-500" />
              <span class="text-sm font-semibold text-gray-700 dark:text-gray-200 truncate">邮件</span>
            </div>
            <div class="flex-1 flex items-end">
              <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {{ aiNoticeStore.emailStats }}
              </div>
            </div>
          </div>
        </div>

        <!-- 计划数量卡片 - 使用AI统计数据 -->
        <div 
          class="stats-card bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 rounded-2xl p-3 border border-emerald-200/50 dark:border-emerald-700/30 shadow-sm hover:shadow-lg transition-shadow duration-200 cursor-pointer"
          @click="handlePlanClick"
          title="点击查看计划详情"
        >
          <div class="flex flex-col h-full">
            <div class="flex items-center gap-2 mb-2">
              <Icon :icon="calendarMarkBoldDuotone" class="text-lg text-emerald-500" />
              <span class="text-sm font-semibold text-gray-700 dark:text-gray-200 truncate">计划</span>
            </div>
            <div class="flex-1 flex items-end">
              <div class="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                {{ aiNoticeStore.planStats }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { Icon } from "@iconify/vue";
import { useRecentStore } from "@/stores/modules/recent";
import { useAiNoticeStore } from "@/stores/modules/aiNotice";
import chartSquareBoldDuotone from "@iconify-icons/solar/chart-square-bold-duotone";
import letterBoldDuotone from "@iconify-icons/solar/letter-bold-duotone";
import calendarMarkBoldDuotone from "@iconify-icons/solar/calendar-mark-bold-duotone";
import usersGroupTwoRoundedBoldDuotone from "@iconify-icons/solar/users-group-two-rounded-bold-duotone";
import documentTextBoldDuotone from "@iconify-icons/solar/document-text-bold-duotone";

const isLoading = ref(true)

const recentStore = useRecentStore()
const aiNoticeStore = useAiNoticeStore()

// 计算属性
// AI智能通知总数 - 使用store的getter
const aiNotificationTotal = computed(() => {
  return aiNoticeStore.aiNotificationTotal;
});

// 事件处理方法
const handleEmailClick = () => {
  // 这里可以跳转到邮件页面或打开邮件弹窗
  console.log("点击邮件卡片，跳转到邮件页面");
  console.log("AI邮件通知数:", aiNoticeStore.emailStats);
  // 例如：router.push('/email') 或者打开一个模态框
};

const handlePlanClick = () => {
  // 这里可以跳转到待办页面或打开待办弹窗
  console.log("点击计划卡片，跳转到计划页面");
  console.log("AI计划通知数:", aiNoticeStore.planStats);
  // 例如：router.push('/todos') 或者打开一个模态框
};

const handleMeetingClick = () => {
  // 这里可以跳转到会议页面或打开会议弹窗
  console.log('点击会议卡片，跳转到会议页面')
  console.log("AI会议通知数:", aiNoticeStore.meetingStats);
  // 例如：router.push('/meetings') 或者打开一个模态框
}

const handleDocumentClick = () => {
  // 这里可以跳转到公文页面或打开公文弹窗
  console.log('点击公文卡片，跳转到公文页面')
  console.log("AI公文通知数:", aiNoticeStore.documentStats);
  // 例如：router.push('/documents') 或者打开一个模态框
}

const handleAiNotificationClick = () => {
  // 这里可以跳转到AI智能通知页面或打开通知弹窗
  console.log('点击AI智能通知卡片，跳转到通知页面')
  console.log('当前AI通知统计数据:', aiNoticeStore.aiNotificationStats)
  // 例如：router.push('/ai-notifications') 或者打开一个模态框
}

// 获取AI智能通知统计数据 - 使用store方法
const getAiNotificationStats = async () => {
  await aiNoticeStore.fetchAiNotificationStats();
}

// 刷新所有统计数据
const refreshStats = async () => {
  isLoading.value = true;
  try {
    // 只需要获取AI统计数据，包含所有4种类型
    await getAiNotificationStats();
  } catch (error) {
    console.error("刷新统计数据失败:", error);
  } finally {
    isLoading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  refreshStats();
});
</script>

<style scoped>
.widget-container {
  overflow: visible;
}

.stats-card {
  cursor: pointer;
}

/* 确保在小屏幕上也能正常显示 */
@media (max-height: 400px) {
  .grid {
    grid-template-rows: repeat(2, minmax(0, 1fr));
  }

  .stats-card {
    padding: 0.5rem;
  }

  .text-3xl {
    font-size: 1.875rem;
  }
}
</style>
