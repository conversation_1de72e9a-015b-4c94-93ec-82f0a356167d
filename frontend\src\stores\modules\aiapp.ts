import { defineStore } from "pinia";
import { AiAppState, AiAppData } from "../interface";
import { getMyAppList } from "@/api/modules/aiApps";
import piniaPersistConfig from "../helper/persist";

export const useAiAppStore = defineStore("lark-ai-app", {
  state: (): AiAppState => ({
    myAppList: [],
    loading: false
  }),
  getters: {
    // 获取我的 AI 微应用总数
    myAppCount: (state) => {
      return state.myAppList.length;
    },
    // 获取按分类分组的应用
    appsByCategory: (state) => {
      const categories: Record<string, AiAppData[]> = {};
      state.myAppList.forEach(app => {
        const category = app.category || '其他';
        if (!categories[category]) {
          categories[category] = [];
        }
        categories[category].push(app);
      });
      return categories;
    },
    // 获取热门应用
    hotApps: (state) => {
      return state.myAppList.filter(app => app.isHot);
    }
  },
  actions: {
    // 获取我的 AI 微应用列表
    async fetchMyAppList() {
      try {
        this.loading = true;
        const { data } = await getMyAppList();
        this.myAppList = data || [];
        console.log('✅ 成功获取我的 AI 微应用列表:', this.myAppList);
      } catch (error) {
        console.error('❌ 获取我的 AI 微应用列表失败:', error);
        this.myAppList = [];
      } finally {
        this.loading = false;
      }
    },
    
    // 添加应用到我的列表（本地状态更新）
    addAppToMyList(app: AiAppData) {
      const exists = this.myAppList.find(item => item.id === app.id);
      if (!exists) {
        this.myAppList.push(app);
        console.log('✅ 应用已添加到我的列表:', app.name);
      }
    },
    
    // 从我的列表中移除应用（本地状态更新）
    removeAppFromMyList(appId: number) {
      const index = this.myAppList.findIndex(app => app.id === appId);
      if (index > -1) {
        const removedApp = this.myAppList.splice(index, 1)[0];
        console.log('✅ 应用已从我的列表中移除:', removedApp.name);
      }
    },
    
    // 清空我的应用列表
    clearMyAppList() {
      this.myAppList = [];
      console.log('✅ 已清空我的 AI 微应用列表');
    }
  },
  persist: piniaPersistConfig("lark-ai-app")
});
