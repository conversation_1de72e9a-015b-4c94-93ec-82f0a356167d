/**
 * 检查图标透明度的脚本
 */

const { execSync } = require('child_process');
const fs = require('fs');

// 需要检查透明度的图标文件
const iconsToCheck = [
  'public/images/tray.png',
  'public/images/<EMAIL>',
  'public/images/logo-32.png',
  'build/icons/32x32.png',
  'build/icons/64x64.png',
];

/**
 * 检查 PNG 文件是否有透明背景
 */
function checkTransparency(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return { hasAlpha: false, error: '文件不存在' };
    }

    // 使用 ImageMagick 检查图像信息
    const result = execSync(`magick identify -verbose "${filePath}"`, { encoding: 'utf8' });
    
    // 检查是否有 Alpha 通道
    const hasAlpha = result.includes('Alpha:') || result.includes('Matte:') || result.includes('sRGBA');
    
    // 检查颜色类型
    const colorType = result.match(/Type: (\w+)/);
    const isTransparent = colorType && (
      colorType[1].includes('TrueColorAlpha') ||
      colorType[1].includes('PaletteAlpha') ||
      colorType[1].includes('GrayscaleAlpha')
    );

    // 检查是否有 Alpha 通道信息
    const hasAlphaChannel = result.includes('Alpha: 1-bit') || result.includes('Alpha: 8-bit');

    return {
      hasAlpha: hasAlpha || isTransparent || hasAlphaChannel,
      colorType: colorType ? colorType[1] : 'Unknown',
      details: result.split('\n').filter(line =>
        line.includes('Type:') ||
        line.includes('Alpha:') ||
        line.includes('Matte:') ||
        line.includes('Colorspace:') ||
        line.includes('Base type:')
      )
    };
  } catch (error) {
    return { hasAlpha: false, error: error.message };
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔍 检查图标透明度...\n');
  
  let allTransparent = true;
  
  for (const iconPath of iconsToCheck) {
    const result = checkTransparency(iconPath);
    
    if (result.error) {
      console.log(`❌ ${iconPath}: ${result.error}`);
      allTransparent = false;
    } else {
      const status = result.hasAlpha ? '✅ 透明' : '❌ 不透明';
      console.log(`${status} ${iconPath}`);
      console.log(`   颜色类型: ${result.colorType}`);
      
      if (result.details && result.details.length > 0) {
        result.details.forEach(detail => {
          console.log(`   ${detail.trim()}`);
        });
      }
      
      if (!result.hasAlpha) {
        allTransparent = false;
      }
    }
    console.log('');
  }
  
  console.log('='.repeat(60));
  
  if (allTransparent) {
    console.log('✅ 所有图标都有透明背景！');
  } else {
    console.log('❌ 部分图标没有透明背景，需要重新生成');
    console.log('💡 建议检查源 SVG 文件和生成脚本');
  }
}

// 运行检查
if (require.main === module) {
  main();
}

module.exports = { checkTransparency, main };
