<template>
  <div class="inline-editor-container" :class="{ 'read-only': readOnly }">
    <!-- 编辑器头部 -->
    <div v-if="!readOnly" class="editor-header">
      <input
        v-model="currentTitle"
        placeholder="输入标题..."
        class="title-input"
        ref="titleInputRef"
      />
      <div class="editor-actions">
        <button @click="handleCancel" class="action-btn cancel-btn">
          取消
        </button>
        <button @click="handleSave" :disabled="saving" class="action-btn save-btn">
          {{ saving ? '保存中...' : '保存' }}
        </button>
      </div>
    </div>

    <!-- Tiptap 编辑器容器 -->
    <div class="editor-content">
      <!-- 编辑器菜单栏 -->
      <div v-if="!readOnly && editor" class="editor-menubar">
        <!-- 文本格式化 -->
        <div class="menu-group">
          <button
            @click="editor.chain().focus().toggleBold().run()"
            :class="{ 'is-active': editor.isActive('bold') }"
            class="menu-button"
            title="粗体"
          >
            <font-awesome-icon icon="bold" />
          </button>
          <button
            @click="editor.chain().focus().toggleItalic().run()"
            :class="{ 'is-active': editor.isActive('italic') }"
            class="menu-button"
            title="斜体"
          >
            <font-awesome-icon icon="italic" />
          </button>
          <button
            @click="editor.chain().focus().toggleMark('underline').run()"
            :class="{ 'is-active': editor.isActive('underline') }"
            class="menu-button"
            title="下划线"
          >
            <font-awesome-icon icon="underline" />
          </button>
          <button
            @click="editor.chain().focus().toggleStrike().run()"
            :class="{ 'is-active': editor.isActive('strike') }"
            class="menu-button"
            title="删除线"
          >
            <font-awesome-icon icon="strikethrough" />
          </button>
          <button
            @click="editor.chain().focus().toggleCode().run()"
            :class="{ 'is-active': editor.isActive('code') }"
            class="menu-button"
            title="行内代码"
          >
            <font-awesome-icon icon="code" />
          </button>
        </div>

        <!-- 分隔线 -->
        <div class="menu-divider"></div>

        <!-- 标题 -->
        <div class="menu-group">
          <button
            @click="editor.chain().focus().toggleHeading({ level: 1 }).run()"
            :class="{ 'is-active': editor.isActive('heading', { level: 1 }) }"
            class="menu-button"
            title="标题 1"
          >
            H1
          </button>
          <button
            @click="editor.chain().focus().toggleHeading({ level: 2 }).run()"
            :class="{ 'is-active': editor.isActive('heading', { level: 2 }) }"
            class="menu-button"
            title="标题 2"
          >
            H2
          </button>
          <button
            @click="editor.chain().focus().toggleHeading({ level: 3 }).run()"
            :class="{ 'is-active': editor.isActive('heading', { level: 3 }) }"
            class="menu-button"
            title="标题 3"
          >
            H3
          </button>
          <button
            @click="editor.chain().focus().setParagraph().run()"
            :class="{ 'is-active': editor.isActive('paragraph') }"
            class="menu-button"
            title="段落"
          >
            <font-awesome-icon icon="paragraph" />
          </button>
        </div>

        <!-- 分隔线 -->
        <div class="menu-divider"></div>

        <!-- 列表 -->
        <div class="menu-group">
          <button
            @click="editor.chain().focus().toggleBulletList().run()"
            :class="{ 'is-active': editor.isActive('bulletList') }"
            class="menu-button"
            title="无序列表"
          >
            <font-awesome-icon icon="list-ul" />
          </button>
          <button
            @click="editor.chain().focus().toggleOrderedList().run()"
            :class="{ 'is-active': editor.isActive('orderedList') }"
            class="menu-button"
            title="有序列表"
          >
            <font-awesome-icon icon="list-ol" />
          </button>
          <button
            @click="editor.chain().focus().toggleBlockquote().run()"
            :class="{ 'is-active': editor.isActive('blockquote') }"
            class="menu-button"
            title="引用"
          >
            <font-awesome-icon icon="quote-left" />
          </button>
        </div>

        <!-- 分隔线 -->
        <div class="menu-divider"></div>

        <!-- 对齐 -->
        <div class="menu-group">
          <button
            @click="editor.commands.setTextAlign('left')"
            :class="{ 'is-active': editor.isActive({ textAlign: 'left' }) }"
            class="menu-button"
            title="左对齐"
          >
            <font-awesome-icon icon="align-left" />
          </button>
          <button
            @click="editor.commands.setTextAlign('center')"
            :class="{ 'is-active': editor.isActive({ textAlign: 'center' }) }"
            class="menu-button"
            title="居中对齐"
          >
            <font-awesome-icon icon="align-center" />
          </button>
          <button
            @click="editor.commands.setTextAlign('right')"
            :class="{ 'is-active': editor.isActive({ textAlign: 'right' }) }"
            class="menu-button"
            title="右对齐"
          >
            <font-awesome-icon icon="align-right" />
          </button>
        </div>

        <!-- 分隔线 -->
        <div class="menu-divider"></div>

        <!-- 表格功能 -->
        <div class="menu-group">
          <button
            @click="insertTable"
            class="menu-button"
            title="插入表格"
          >
            <font-awesome-icon icon="table" />
          </button>
          <button
            @click="editor.chain().focus().addColumnBefore().run()"
            :disabled="!editor.isActive('table')"
            class="menu-button"
            title="在前面插入列"
          >
            <font-awesome-icon icon="table-columns" />
          </button>
          <button
            @click="editor.chain().focus().addRowBefore().run()"
            :disabled="!editor.isActive('table')"
            class="menu-button"
            title="在上面插入行"
          >
            <font-awesome-icon icon="table-rows" />
          </button>
          <button
            @click="editor.chain().focus().deleteTable().run()"
            :disabled="!editor.isActive('table')"
            class="menu-button"
            title="删除表格"
          >
            <font-awesome-icon icon="trash" />
          </button>
        </div>

        <!-- 分隔线 -->
        <div class="menu-divider"></div>

        <!-- 代码和格式 -->
        <div class="menu-group">
          <button
            @click="editor.chain().focus().toggleCodeBlock().run()"
            :class="{ 'is-active': editor.isActive('codeBlock') }"
            class="menu-button"
            title="代码块"
          >
            <font-awesome-icon icon="code" />
          </button>
          <button
            @click="editor.chain().focus().toggleSuperscript().run()"
            :class="{ 'is-active': editor.isActive('superscript') }"
            class="menu-button"
            title="上标"
          >
            <span style="font-size: 10px;">X²</span>
          </button>
          <button
            @click="editor.chain().focus().toggleSubscript().run()"
            :class="{ 'is-active': editor.isActive('subscript') }"
            class="menu-button"
            title="下标"
          >
            <span style="font-size: 10px;">X₂</span>
          </button>
        </div>

        <!-- 分隔线 -->
        <div class="menu-divider"></div>

        <!-- 其他功能 -->
        <div class="menu-group">
          <button
            @click="editor.chain().focus().setHorizontalRule().run()"
            class="menu-button"
            title="分割线"
          >
            <font-awesome-icon icon="minus" />
          </button>
          <!-- AI生成按钮已移除，使用选中文本后的AI工具栏替代 -->
        </div>

        <!-- 分隔线 -->
        <div class="menu-divider"></div>

        <!-- 撤销重做 -->
        <div class="menu-group">
          <button
            @click="editor.chain().focus().undo().run()"
            :disabled="!editor.can().undo()"
            class="menu-button"
            title="撤销"
          >
            <font-awesome-icon icon="undo" />
          </button>
          <button
            @click="editor.chain().focus().redo().run()"
            :disabled="!editor.can().redo()"
            class="menu-button"
            title="重做"
          >
            <font-awesome-icon icon="redo" />
          </button>
        </div>
      </div>

      <!-- 编辑器内容 -->
      <div class="editor-wrapper">
        <editor-content :editor="editor" />

        <!-- 字数统计显示 -->
        <div v-if="showWordCount && !readOnly" class="word-count-display">
          <span>字符: {{ wordCount.characters }}</span>
          <span>单词: {{ wordCount.words }}</span>
        </div>
      </div>
    </div>
    
    <!-- AI助手按钮 -->
    <div v-if="!readOnly" class="floating-ai-button" :class="{ 'disabled': !editorReady }">
      <div
        ref="aiButtonRef"
        class="ai-button"
        @click="editorReady ? openAiDialog('qa') : null"
        :title="!editorReady ? '编辑器初始化中...' : 'AI问答'"
        :class="{ 'disabled': !editorReady }"
      >
        <font-awesome-icon icon="robot" />
      </div>
    </div>

    <!-- AI对话框组件 -->
    <template v-if="!readOnly">
      <AiChatDialog
        :visible="aiDialogStates.qa.visible"
        type="qa"
        :messages="aiDialogStates.qa.messages"
        :loading="isQaProcessing"
        :prompt="aiDialogStates.qa.prompt"
        :selected-text="aiDialogStates.qa.selectedText"
        :position="aiButtonPosition"
        @close="closeDialog('qa')"
        @clear="clearDialogHistory('qa')"
        @send="(prompt) => sendDialogMessage('qa', prompt)"
        @apply="(content, messageIndex) => applyResult('qa', content, messageIndex)"
        @reject="(messageIndex) => rejectResult('qa', messageIndex)"
        @update:prompt="(value) => aiDialogStates.qa.prompt = value"
        @clear-selected="clearSelectedText('qa')"
      />
      
      <AiChatDialog
        :visible="aiDialogStates.polish.visible"
        type="polish"
        :messages="aiDialogStates.polish.messages"
        :loading="isPolishing"
        :prompt="aiDialogStates.polish.prompt"
        :selected-text="aiDialogStates.polish.selectedText"
        :position="aiButtonPosition"
        @close="closeDialog('polish')"
        @clear="clearDialogHistory('polish')"
        @send="(prompt) => sendDialogMessage('polish', prompt)"
        @apply="(content, messageIndex) => applyResult('polish', content, messageIndex)"
        @reject="(messageIndex) => rejectResult('polish', messageIndex)"
        @update:prompt="(value) => aiDialogStates.polish.prompt = value"
        @clear-selected="clearSelectedText('polish')"
      />

      <AiChatDialog
        :visible="aiDialogStates.draft.visible"
        type="draft"
        :messages="aiDialogStates.draft.messages"
        :loading="isDrafting"
        :prompt="aiDialogStates.draft.prompt"
        :selected-text="aiDialogStates.draft.selectedText"
        :position="aiButtonPosition"
        @close="closeDialog('draft')"
        @clear="clearDialogHistory('draft')"
        @send="(prompt) => sendDialogMessage('draft', prompt)"
        @apply="(content, messageIndex) => applyResult('draft', content, messageIndex)"
        @reject="(messageIndex) => rejectResult('draft', messageIndex)"
        @update:prompt="(value) => aiDialogStates.draft.prompt = value"
        @clear-selected="clearSelectedText('draft')"
      />

      <AiChatDialog
        :visible="aiDialogStates.check.visible"
        type="check"
        :messages="aiDialogStates.check.messages"
        :loading="isChecking"
        :prompt="aiDialogStates.check.prompt"
        :selected-text="aiDialogStates.check.selectedText"
        :position="aiButtonPosition"
        @close="closeDialog('check')"
        @clear="clearDialogHistory('check')"
        @send="(prompt) => sendDialogMessage('check', prompt)"
        @apply="(content, messageIndex) => applyResult('check', content, messageIndex)"
        @reject="(messageIndex) => rejectResult('check', messageIndex)"
        @update:prompt="(value) => aiDialogStates.check.prompt = value"
        @clear-selected="clearSelectedText('check')"
      />
    </template>

    <!-- 简化的AI快捷工具栏 -->
    <div v-if="!readOnly && aiToolbarVisible" class="simple-ai-toolbar" :style="{
      left: aiToolbarPosition.x + 'px',
      top: aiToolbarPosition.y + 'px'
    }">
      <button @click="openAiDialog('polish')" class="ai-quick-btn polish" title="润色文本">
        <font-awesome-icon icon="magic-wand-sparkles" />
      </button>
      <button @click="openAiDialog('draft')" class="ai-quick-btn draft" title="文章起稿">
        <font-awesome-icon icon="pen-to-square" />
      </button>
      <button @click="openAiDialog('check')" class="ai-quick-btn check" title="检查错误">
        <font-awesome-icon icon="magnifying-glass" />
      </button>
      <button @click="openAiDialog('qa')" class="ai-quick-btn qa" title="AI问答">
        <font-awesome-icon icon="robot" />
      </button>
      <button @click="hideAiToolbar" class="ai-quick-btn close" title="关闭">
        <font-awesome-icon icon="times" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/ipc/ipcApi';
import { useEditor, EditorContent } from '@tiptap/vue-3';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';
import TextAlign from '@tiptap/extension-text-align';
import Underline from '@tiptap/extension-underline';
import Strike from '@tiptap/extension-strike';
import Code from '@tiptap/extension-code';
import Blockquote from '@tiptap/extension-blockquote';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import BubbleMenu from '@tiptap/extension-bubble-menu';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import CodeBlock from '@tiptap/extension-code-block';
import Superscript from '@tiptap/extension-superscript';
import Subscript from '@tiptap/extension-subscript';
import { sendAiStreamChatMessage, type AiStreamChatRequest } from '@/api/modules/ai/chat';
import AiChatDialog, { type AiMessage } from '@/views/home/<USER>/AiChat/AiChatDialog.vue';
// import { smartConvertContent, hasMarkdownSyntax } from '@/utils/markdownToTiptap';
// AiGeneration 扩展已移除，使用 AiChatDialog 替代

// 简单的 Markdown 语法检测函数
const hasMarkdownSyntax = (text: string): boolean => {
  const markdownPatterns = [
    /^#{1,6}\s+/m,           // 标题
    /\*\*.*?\*\*/,           // 粗体
    /\*.*?\*/,               // 斜体
    /`.*?`/,                 // 行内代码
    /```[\s\S]*?```/,        // 代码块
    /^\s*[-*+]\s+/m,         // 无序列表
    /^\s*\d+\.\s+/m,         // 有序列表
    /^\s*>\s+/m,             // 引用
    /\[.*?\]\(.*?\)/,        // 链接
    /!\[.*?\]\(.*?\)/        // 图片
  ];

  return markdownPatterns.some(pattern => pattern.test(text));
};

// 简单的 Markdown 到 HTML 转换函数
const smartConvertContent = (content: string): string => {
  let html = content;

  // 转换标题
  html = html.replace(/^(#{1,6})\s+(.+)$/gm, (_, hashes, text) => {
    const level = hashes.length;
    return `<h${level}>${text.trim()}</h${level}>`;
  });

  // 转换粗体
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

  // 转换斜体
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

  // 转换行内代码
  html = html.replace(/`(.*?)`/g, '<code>$1</code>');

  // 转换代码块
  html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

  // 转换无序列表
  html = html.replace(/^\s*[-*+]\s+(.+)$/gm, '<li>$1</li>');
  html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

  // 转换有序列表
  html = html.replace(/^\s*\d+\.\s+(.+)$/gm, '<li>$1</li>');

  // 转换引用
  html = html.replace(/^\s*>\s+(.+)$/gm, '<blockquote>$1</blockquote>');

  // 转换段落（将剩余的文本行包装为段落）
  const lines = html.split('\n');
  const processedLines = lines.map(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('<') && !trimmed.endsWith('>')) {
      return `<p>${trimmed}</p>`;
    }
    return line;
  }).filter(line => line.trim() !== ''); // 过滤掉空行

  return processedLines.join(''); // 使用空字符串连接，避免多余的换行符
};

interface Props {
  mode?: 'create' | 'edit';
  noteId?: number;
  initialContent?: string;
  initialTitle?: string;
  readOnly?: boolean;
}

interface Emits {
  (e: 'save', data: { id?: number; content: string; title: string; originalContent: string }): void;
  (e: 'cancel'): void;
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'create',
  initialContent: '',
  initialTitle: '',
  readOnly: false
});

const emit = defineEmits<Emits>();

// 响应式数据
const currentTitle = ref(props.initialTitle || '');
const saving = ref(false);
const titleInputRef = ref<HTMLInputElement>();
const isPolishing = ref(false);
const isQaProcessing = ref(false);
const isDrafting = ref(false);
const isChecking = ref(false);
const chatBodyRef = ref<HTMLDivElement>();
const editorReady = ref(false);
const showWordCount = ref(false);
const isFullscreen = ref(false);
const wordCount = ref({ characters: 0, words: 0 });
const aiButtonRef = ref<HTMLDivElement>();
const aiButtonPosition = ref({ bottom: 0, right: 0 });

// 防抖定时器
let updateSelectedTextTimer: NodeJS.Timeout | null = null;

// 每种AI对话类型的独立状态
const aiDialogStates = ref({
  qa: {
    messages: [] as AiMessage[],
    visible: false,
    prompt: '',
    selectedText: '',
    selectedContentInfo: null as any
  },
  polish: {
    messages: [] as AiMessage[],
    visible: false,
    prompt: '',
    selectedText: '',
    selectedContentInfo: null as any
  },
  draft: {
    messages: [] as AiMessage[],
    visible: false,
    prompt: '',
    selectedText: '',
    selectedContentInfo: null as any
  },
  check: {
    messages: [] as AiMessage[],
    visible: false,
    prompt: '',
    selectedText: '',
    selectedContentInfo: null as any
  }
});

// 选中内容的位置信息
const selectedContentInfo = ref<{
  blockIndex: number;
  originalText: string;
  selectedText: string;
  beforeText: string;
  afterText: string;
} | null>(null);

const statusMap = {
  qa: isQaProcessing,
  polish: isPolishing,
  draft: isDrafting,
  check: isChecking
};

let currentAiController: any = null;

// AI工具栏状态
const aiToolbarVisible = ref(false);
const aiToolbarPosition = ref({ x: 0, y: 0 });
const aiToolbarSelectedText = ref('');
const aiToolbarLoading = ref(false);

// 创建 Tiptap 编辑器实例
const editor = useEditor({
  extensions: [
    StarterKit,
    Placeholder.configure({
      placeholder: props.readOnly ? '' : '开始记录你的想法...',
    }),
    TextStyle,
    Color,
    Highlight,
    TextAlign.configure({
      types: ['heading', 'paragraph'],
    }),
    Underline,
    Strike,
    Code,
    Blockquote,
    HorizontalRule,
    Table.configure({
      resizable: true,
    }),
    TableRow,
    TableHeader,
    TableCell,
    CodeBlock.configure({
      HTMLAttributes: {
        class: 'code-block',
      },
    }),
    Superscript,
    Subscript,
    BubbleMenu.configure({
      element: null, // 我们将手动控制显示
    }),
    // AiGeneration 扩展已移除，使用 AiChatDialog 替代
  ],
  content: '',
  editable: !props.readOnly,
  onCreate: ({ editor }) => {
    editorReady.value = true;
    console.log('编辑器初始化完成');

    // 设置初始内容
    if (props.initialContent) {
      try {
        // 尝试解析为 HTML 或纯文本
        const content = props.initialContent.startsWith('<')
          ? props.initialContent
          : `<p>${props.initialContent}</p>`;
        editor.commands.setContent(content);
      } catch (error) {
        console.error('设置初始内容失败:', error);
        editor.commands.setContent(`<p>${props.initialContent}</p>`);
      }
    }
  },
  onSelectionUpdate: ({ editor }) => {
    // 当选择变化时检查是否显示AI工具栏
    if (!props.readOnly) {
      const { from, to } = editor.state.selection;
      const selectedText = editor.state.doc.textBetween(from, to);

      // 更新所有打开的对话框的选中文本
      updateOpenDialogsSelectedText(selectedText);

      if (selectedText.trim()) {
        // 延迟显示，避免频繁触发
        setTimeout(() => {
          showAiToolbar();
        }, 100);
      } else {
        hideAiToolbar();
      }
    }
  },
  onUpdate: () => {
    // 更新字数统计
    if (showWordCount.value) {
      updateWordCount();
    }
  },
});

// AI工具栏相关函数
const showAiToolbar = (event?: MouseEvent) => {
  if (!editor.value || props.readOnly) return;

  const { from, to } = editor.value.state.selection;
  const selectedText = editor.value.state.doc.textBetween(from, to);

  if (!selectedText.trim()) {
    hideAiToolbar();
    return;
  }

  aiToolbarSelectedText.value = selectedText;

  // 计算工具栏位置
  if (event) {
    aiToolbarPosition.value = {
      x: event.clientX,
      y: event.clientY - 60 // 在鼠标上方显示
    };
  } else {
    // 如果没有事件，使用选择区域的位置
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      aiToolbarPosition.value = {
        x: rect.left + rect.width / 2,
        y: rect.top - 60
      };
    }
  }

  aiToolbarVisible.value = true;
};

const hideAiToolbar = () => {
  aiToolbarVisible.value = false;
  aiToolbarSelectedText.value = '';
  aiToolbarLoading.value = false;
};

// 计算AI按钮位置
const calculateAiButtonPosition = () => {
  if (!aiButtonRef.value) return;

  const buttonRect = aiButtonRef.value.getBoundingClientRect();
  // 对话框底部距离按钮顶部12px，所以对话框的bottom应该是按钮顶部 + 12px
  const dialogBottom = window.innerHeight - buttonRect.top + 12;
  aiButtonPosition.value = {
    bottom: dialogBottom, // 对话框底部位置
    right: window.innerWidth - buttonRect.right // 与按钮右对齐
  };
};

// 打开AI对话框
const openAiDialog = async (action: 'qa' | 'polish' | 'draft' | 'check') => {
  if (!editor.value || !editorReady.value) {
    ElMessage.warning('编辑器正在初始化中，请稍后再试');
    return;
  }

  // 计算AI按钮位置
  calculateAiButtonPosition();

  // 关闭其他对话框
  Object.keys(aiDialogStates.value).forEach(key => {
    if (key !== action) {
      aiDialogStates.value[key as keyof typeof aiDialogStates.value].visible = false;
    }
  });

  // 获取选中内容信息
  const selectedInfo = await getSelectedContentInfo();
  let systemMessage = `操作类型: ${getActionName(action)}. `;

  if (selectedInfo) {
    aiDialogStates.value[action].selectedContentInfo = selectedInfo;
    aiDialogStates.value[action].selectedText = selectedInfo.selectedText;
    systemMessage += `已选中以下文本，后续操作将基于此文本：\n"${selectedInfo.selectedText}"`;
  } else {
    aiDialogStates.value[action].selectedText = '';
    systemMessage += '未选中任何文本，后续操作将基于整篇文章或您的输入。';
  }

  // 添加系统消息（如果还没有的话）
  const lastMessage = aiDialogStates.value[action].messages[aiDialogStates.value[action].messages.length - 1];
  if (!lastMessage || lastMessage.role !== 'system' || !lastMessage.content.includes(getActionName(action))) {
    aiDialogStates.value[action].messages.push({ role: 'system', content: systemMessage });
  }

  aiDialogStates.value[action].visible = true;
  aiDialogStates.value[action].prompt = '';

  // 隐藏快捷工具栏
  hideAiToolbar();
};

// 插入表格
const insertTable = () => {
  if (!editor.value) return;

  editor.value.chain().focus().insertTable({
    rows: 3,
    cols: 3,
    withHeaderRow: true
  }).run();
};

// AI生成功能已移除，使用 AiChatDialog 替代
// 用户可以通过选中文本后的AI工具栏或直接在对话框中输入来生成内容

// 这些方法已经被 openAiDialog 和 AiChatDialog 组件替代

// 获取选中内容的位置信息
const getSelectedContentInfo = async (): Promise<{
  originalText: string;
  selectedText: string;
  beforeText: string;
  afterText: string;
} | null> => {
  if (!editor.value) return null;

  const { from, to } = editor.value.state.selection;
  const selectedText = editor.value.state.doc.textBetween(from, to);

  if (!selectedText.trim()) return null;

  // 获取整个文档的文本内容
  const fullText = editor.value.getText();
  const startIndex = fullText.indexOf(selectedText);

  if (startIndex >= 0) {
    return {
      originalText: fullText,
      selectedText,
      beforeText: fullText.substring(0, startIndex),
      afterText: fullText.substring(startIndex + selectedText.length),
    };
  }

  return null;
};

// Tiptap 编辑器不需要单独的初始化函数，已在 useEditor 中处理

const handleSave = async () => {
  if (!editor.value || !editorReady.value || saving.value) return;

  if (!currentTitle.value.trim()) {
    ElMessage.warning('请输入笔记标题');
    titleInputRef.value?.focus();
    return;
  }

  saving.value = true;

  try {
    // 获取 HTML 内容和纯文本内容
    const htmlContent = editor.value.getHTML();
    const plainTextContent = editor.value.getText();

    // 保存到本地SQLite数据库
    let noteId = props.noteId;

    const noteData = {
      content: plainTextContent,
      title: currentTitle.value,
      id: noteId,
      originalContent: htmlContent // 保存 HTML 格式的内容
    };

    const localResult = await ipc.invoke(ipcApiRoute.saveNote, noteData);

    if (localResult && localResult.id) {
      noteId = localResult.id;
      ElMessage.success('笔记已保存');
    } else {
      throw new Error('保存失败');
    }

    // 发送保存成功事件
    emit('save', {
      id: noteId,
      content: plainTextContent,
      title: currentTitle.value,
      originalContent: htmlContent
    });

  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败，请重试');
  } finally {
    saving.value = false;
  }
};

const handleCancel = () => {
  emit('cancel');
};

// 切换字数统计显示
const toggleWordCount = () => {
  showWordCount.value = !showWordCount.value;
  if (showWordCount.value && editor.value) {
    updateWordCount();
  }
};

// 更新字数统计
const updateWordCount = () => {
  if (!editor.value) return;

  const text = editor.value.getText();
  wordCount.value = {
    characters: text.length,
    words: text.trim() ? text.trim().split(/\s+/).length : 0
  };
};

// 切换全屏模式
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;

  if (isFullscreen.value) {
    document.documentElement.requestFullscreen?.();
  } else {
    document.exitFullscreen?.();
  }
};

// 清除选中文本
const clearSelectedText = (dialogType: 'qa' | 'polish' | 'draft' | 'check') => {
  aiDialogStates.value[dialogType].selectedText = '';
  aiDialogStates.value[dialogType].selectedContentInfo = null;
};

// 更新所有打开的对话框的选中文本（带防抖）
const updateOpenDialogsSelectedText = (selectedText: string) => {
  // 清除之前的定时器
  if (updateSelectedTextTimer) {
    clearTimeout(updateSelectedTextTimer);
  }

  // 设置防抖延迟
  updateSelectedTextTimer = setTimeout(async () => {
    try {
      // 检查是否有对话框打开
      const hasOpenDialog = Object.values(aiDialogStates.value).some(state => state.visible);
      if (!hasOpenDialog) {
        return;
      }

      // 获取选中内容信息
      const selectedInfo = selectedText.trim() ? await getSelectedContentInfo() : null;

      // 更新所有可见的对话框
      Object.keys(aiDialogStates.value).forEach(key => {
        const dialogType = key as keyof typeof aiDialogStates.value;
        if (aiDialogStates.value[dialogType].visible) {
          const oldSelectedText = aiDialogStates.value[dialogType].selectedText;
          const newSelectedText = selectedText.trim();

          // 只有当选中文本真正改变时才更新
          if (oldSelectedText !== newSelectedText) {
            aiDialogStates.value[dialogType].selectedText = newSelectedText;
            aiDialogStates.value[dialogType].selectedContentInfo = selectedInfo;

            // 如果有选中文本，添加或更新系统消息
            if (newSelectedText) {
              const systemMessage = `操作类型: ${getActionName(dialogType)}. 已选中以下文本，后续操作将基于此文本：\n"${newSelectedText}"`;

              // 查找最后一条系统消息
              const messages = aiDialogStates.value[dialogType].messages;
              let lastSystemIndex = -1;
              for (let i = messages.length - 1; i >= 0; i--) {
                if (messages[i].role === 'system') {
                  lastSystemIndex = i;
                  break;
                }
              }

              if (lastSystemIndex >= 0 && messages[lastSystemIndex].content.includes(getActionName(dialogType))) {
                // 更新现有的系统消息
                messages[lastSystemIndex].content = systemMessage;
              } else {
                // 添加新的系统消息
                messages.push({ role: 'system', content: systemMessage });
              }
            } else {
              // 如果没有选中文本，更新系统消息
              const messages = aiDialogStates.value[dialogType].messages;
              let lastSystemIndex = -1;
              for (let i = messages.length - 1; i >= 0; i--) {
                if (messages[i].role === 'system') {
                  lastSystemIndex = i;
                  break;
                }
              }

              if (lastSystemIndex >= 0 && messages[lastSystemIndex].content.includes(getActionName(dialogType))) {
                const systemMessage = `操作类型: ${getActionName(dialogType)}. 未选中任何文本，后续操作将基于整篇文章或您的输入。`;
                messages[lastSystemIndex].content = systemMessage;
              }
            }
          }
        }
      });
    } catch (error) {
      console.error('更新对话框选中文本失败:', error);
    }
  }, 200); // 200ms 防抖延迟
};

// 旧的 handleAiAction 方法已被 openAiDialog 替代

// 新的对话框管理函数
const closeDialog = (action: 'qa' | 'polish' | 'draft' | 'check') => {
  aiDialogStates.value[action].visible = false;
  resetAiState();
};

const clearDialogHistory = (action: 'qa' | 'polish' | 'draft' | 'check') => {
  aiDialogStates.value[action].messages = [];
  aiDialogStates.value[action].prompt = '';
};

const sendDialogMessage = async (action: 'qa' | 'polish' | 'draft' | 'check', promptText?: string) => {
  const dialogState = aiDialogStates.value[action];
  const prompt = promptText || dialogState.prompt.trim();
  if (!prompt) return;

  dialogState.messages.push({ role: 'user', content: prompt });
  const status = statusMap[action];
  status.value = true;
  if (!promptText) {
    dialogState.prompt = ''; // 如果是从组件传入的prompt，不清空输入框
  }

  // 滚动到底部
  await nextTick();
  if (chatBodyRef.value) {
    chatBodyRef.value.scrollTop = chatBodyRef.value.scrollHeight;
  }
  
  try {
    const contentText = dialogState.selectedContentInfo?.originalText ?? editor.value!.getText();

    const request: AiStreamChatRequest = {
      query: prompt, // 只发送用户输入的内容，不包含系统提示词
      app_id: "note",
      inputs: {
        operation_type: action,
        content: contentText,
        selected_text: dialogState.selectedText
      },
      shouldProcessNodeStarted: false
    };
    
    const aiMessageIndex = dialogState.messages.length;
    dialogState.messages.push({ role: 'ai', content: '', streaming: true });

    let resultText = '';
    
    currentAiController = sendAiStreamChatMessage(request, {
      onData: (chunk) => {
        resultText += chunk.content;
        dialogState.messages[aiMessageIndex].content = resultText;
        // 实时滚动
        if (chatBodyRef.value) {
            chatBodyRef.value.scrollTop = chatBodyRef.value.scrollHeight;
        }
      },
      onError: (error) => {
        dialogState.messages[aiMessageIndex].content = `抱歉，处理时遇到错误：${error.message}`;
        handleAiError(error, action);
      },
      onComplete: () => {
        dialogState.messages[aiMessageIndex].streaming = false;
        status.value = false;
        currentAiController = null;
      }
    });

  } catch (error) {
    handleAiError(error as Error, action);
  }
};

// 通用的应用结果方法
const applyResult = async (dialogType: 'qa' | 'polish' | 'draft' | 'check', content: string, messageIndex: number) => {
  switch (dialogType) {
    case 'polish':
      await applyPolishResult(content, messageIndex);
      break;
    case 'draft':
      await applyDraftResult(content, messageIndex);
      break;
    default:
      ElMessage.info('该功能暂不支持应用结果');
  }
};

const applyPolishResult = async (content: string, messageIndex: number) => {
  if (!editor.value || !editorReady.value) {
    ElMessage.warning('编辑器未就绪，请稍后再试');
    return;
  }

  try {
    const selectedInfo = aiDialogStates.value.polish.selectedContentInfo;
    
    // 检查内容是否包含 markdown 语法
    const isMarkdown = hasMarkdownSyntax(content);
    let processedContent: string;

    if (isMarkdown) {
      // 如果是 markdown 格式，使用智能转换
      processedContent = smartConvertContent(content);
      console.log('检测到 Markdown 语法，已转换:', processedContent);
    } else {
      // 如果是纯文本，直接使用
      processedContent = content;
    }
    
    if (selectedInfo) {
      // 替换选中的文本
      const { from, to } = editor.value.state.selection;
      if (from !== to) {
        // 有选中文本，替换选中内容
        editor.value.chain().focus().deleteSelection();

        if (isMarkdown) {
          const wrappedContent = `<div class="ai-generated-content ai-polish">${processedContent}</div>`;
          editor.value.chain().insertContent(wrappedContent).run();
        } else {
          editor.value.chain().insertContent(`<p>${processedContent}</p>`).run();
        }

        const successMessage = isMarkdown
          ? '文本润色已以 Markdown 格式应用到选中内容'
          : '文本润色已应用到选中内容';
        ElMessage.success(successMessage);
      } else {
        // 没有选中文本，添加到当前位置
        if (isMarkdown) {
          const wrappedContent = `<div class="ai-generated-content ai-polish">${processedContent}</div>`;
          editor.value.chain().focus().insertContent(wrappedContent).run();
        } else {
          editor.value.chain().focus().insertContent(`<p>${processedContent}</p>`).run();
        }

        const successMessage = isMarkdown
          ? '润色内容已以 Markdown 格式添加到当前位置'
          : '润色内容已添加到当前位置';
        ElMessage.success(successMessage);
      }
    } else {
      // 如果没有选中文本，添加到最后
      const docSize = editor.value.state.doc.content.size;
      editor.value.chain().focus().setTextSelection(docSize);

      if (isMarkdown) {
        const wrappedContent = `<div class="ai-generated-content ai-polish">${processedContent}</div>`;
        editor.value.chain().insertContent(wrappedContent).run();
      } else {
        const wrappedContent = `<div class="ai-generated-content ai-polish"><p>${processedContent}</p></div>`;
        editor.value.chain().insertContent(wrappedContent).run();
      }

      const successMessage = isMarkdown
        ? '润色内容已以 Markdown 格式添加到笔记末尾'
        : '润色内容已添加到笔记末尾';
      ElMessage.success(successMessage);
    }

    // 设置消息状态为已应用
    if (aiDialogStates.value.polish.messages[messageIndex]) {
      aiDialogStates.value.polish.messages[messageIndex].status = 'applied';
      console.log('润色结果已应用，消息状态已更新:', messageIndex);
      // 强制触发响应式更新
      await nextTick();
    }
  } catch (error) {
    console.error('应用润色结果失败:', error);
    ElMessage.error('应用润色结果失败');
  }
};

const applyDraftResult = async (content: string, messageIndex: number) => {
  if (!editor.value || !editorReady.value) {
    ElMessage.warning('编辑器未就绪，请稍后再试');
    return;
  }

  try {
    // 检查内容是否包含 markdown 语法
    const isMarkdown = hasMarkdownSyntax(content);
    let processedContent: string;

    if (isMarkdown) {
      // 如果是 markdown 格式，使用智能转换
      processedContent = smartConvertContent(content);
      console.log('检测到 Markdown 语法，已转换:', processedContent);
    } else {
      // 如果是纯文本，用段落标签包装并添加 AI 标识类
      processedContent = `<div class="ai-generated-content ai-draft"><p>${content}</p></div>`;
    }

    // 移动到文档末尾并插入内容
    const docSize = editor.value.state.doc.content.size;
    editor.value.chain().focus().setTextSelection(docSize);

    // 插入内容
    if (isMarkdown) {
      const wrappedContent = `<div class="ai-generated-content ai-draft">${processedContent}</div>`;
      editor.value.chain().insertContent(wrappedContent).run();
    } else {
      editor.value.chain().insertContent(processedContent).run();
    }
    
    // 添加一些间距
    editor.value.chain().insertContent('<p></p>').run();

    const successMessage = isMarkdown 
      ? '起稿内容已以 Markdown 格式添加到笔记' 
      : '起稿内容已添加到笔记';
    ElMessage.success(successMessage);

    // 设置消息状态为已应用
    if (aiDialogStates.value.draft.messages[messageIndex]) {
      aiDialogStates.value.draft.messages[messageIndex].status = 'applied';
      console.log('起稿结果已应用，消息状态已更新:', messageIndex);
      // 强制触发响应式更新
      await nextTick();
    }
  } catch (error) {
    console.error('应用起稿结果失败:', error);
    ElMessage.error('应用起稿结果失败');
  }
};

// 拒绝结果
const rejectResult = (dialogType: 'polish' | 'draft' | 'check' | 'qa', messageIndex: number) => {
  if (aiDialogStates.value[dialogType].messages[messageIndex]) {
    aiDialogStates.value[dialogType].messages[messageIndex].status = 'rejected';
    console.log(`${dialogType} 结果已拒绝，消息状态已更新:`, messageIndex);
  }
  ElMessage.info('已拒绝该建议');
};

// 工具函数已移除，Tiptap 不需要手动调整大小

const resetAiState = () => {
  selectedContentInfo.value = null;
  Object.values(statusMap).forEach(status => status.value = false);
  if (currentAiController) {
    currentAiController.abort();
    currentAiController = null;
  }
};

const handleAiError = (error: Error, actionType: string) => {
  if (error.name === 'AbortError') {
    console.log('AI request aborted');
    return;
  }
  console.error(`${getActionName(actionType)}失败:`, error);
  ElMessage.error(`${getActionName(actionType)}失败: ${error.message}`);

  const status = statusMap[actionType as keyof typeof statusMap];
  if(status) status.value = false;

  currentAiController = null;
};

const getActionName = (action: string) => {
  const nameMap = {
    qa: 'AI问答',
    polish: '文本润色',
    draft: '文章起稿',
    check: '错误检查'
  };
  return nameMap[action as keyof typeof nameMap] || action;
};

// Tiptap 不需要 extractTextFromBlocks 函数，直接使用 editor.getText() 即可



// 点击外部隐藏AI工具栏
const handleDocumentClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.ai-toolbar') && !target.closest('.ProseMirror')) {
    hideAiToolbar();
  }
};

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl+S 保存
  if (event.ctrlKey && event.key === 's') {
    event.preventDefault();
    handleSave();
    return;
  }

  // F11 全屏
  if (event.key === 'F11') {
    event.preventDefault();
    toggleFullscreen();
    return;
  }

  // Ctrl+Shift+C 字数统计
  if (event.ctrlKey && event.shiftKey && event.key === 'C') {
    event.preventDefault();
    toggleWordCount();
    return;
  }

  // Escape 关闭对话框
  if (event.key === 'Escape') {
    // 关闭所有AI对话框
    Object.keys(aiDialogStates.value).forEach(key => {
      aiDialogStates.value[key as keyof typeof aiDialogStates.value].visible = false;
    });
    hideAiToolbar();
    return;
  }
};

// 全屏状态变化处理函数
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
};

// 生命周期钩子
onMounted(() => {
  // Tiptap 编辑器已在 useEditor 中初始化，这里不需要额外操作
  console.log('组件已挂载');

  // 添加全局点击监听器
  document.addEventListener('click', handleDocumentClick);

  // 添加键盘快捷键监听器
  document.addEventListener('keydown', handleKeydown);

  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', handleFullscreenChange);
});

onUnmounted(() => {
  if (editor.value) {
    editor.value.destroy();
  }
  if (currentAiController) {
    currentAiController.abort();
  }

  // 清理定时器
  if (updateSelectedTextTimer) {
    clearTimeout(updateSelectedTextTimer);
    updateSelectedTextTimer = null;
  }

  // 移除全局监听器
  document.removeEventListener('click', handleDocumentClick);
  document.removeEventListener('keydown', handleKeydown);
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
});

// 监听props变化
watch(() => props.initialTitle, (newTitle) => {
  currentTitle.value = newTitle || '';
});

watch(() => props.readOnly, (newReadOnly, oldReadOnly) => {
  if (newReadOnly !== oldReadOnly && editor.value) {
    editor.value.setEditable(!newReadOnly);
  }
});

watch(() => props.initialContent, (newContent, oldContent) => {
  if (newContent !== oldContent && editor.value && !props.readOnly) {
    try {
      // 尝试解析为 HTML 或纯文本
      const content = newContent?.startsWith('<')
        ? newContent
        : `<p>${newContent || ''}</p>`;
      editor.value.commands.setContent(content);
    } catch (error) {
      console.error('设置内容失败:', error);
      editor.value.commands.setContent(`<p>${newContent || ''}</p>`);
    }
  }
});
</script>

<style scoped lang="scss">
.inline-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
  position: relative;
  overflow-x: hidden; // 防止横向滚动条

  &.read-only {
    background: transparent;
    padding: 0;

    .editor-content {
      padding: 0;
    }

    :deep(.codex-editor--readonly) {
      padding-bottom: 0 !important;
    }
  }
}

.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #ffffff;
  z-index: 10;
}

.title-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  background: transparent;
  margin-right: 16px;

  &::placeholder {
    color: #9ca3af;
  }
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #ffffff;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background: #f9fafb;
    border-color: #9ca3af;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &.save-btn {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;

    &:hover:not(:disabled) {
      background: #2563eb;
      border-color: #2563eb;
    }
  }
}

// 编辑器菜单栏样式
.editor-menubar {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-wrap: wrap;

  .menu-group {
    display: flex;
    align-items: center;
    gap: 2px;
  }

  .menu-divider {
    width: 1px;
    height: 20px;
    background: #d1d5db;
    margin: 0 8px;
  }

  .menu-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: transparent;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;

    &:hover:not(:disabled) {
      background: #e5e7eb;
      color: #111827;
    }

    &:active:not(:disabled) {
      background: #d1d5db;
      transform: scale(0.95);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &.is-active {
      background: #3b82f6;
      color: white;

      &:hover {
        background: #2563eb;
      }
    }

    svg {
      font-size: 14px;
    }

    // AI生成按钮样式已移除
  }
}

.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-wrapper {
  flex: 1;
  overflow-y: auto;
  position: relative;

  // 编辑器内容容器
  :deep(.ProseMirror-focused) {
    outline: none;
  }

  // Tiptap 编辑器样式
  :deep(.ProseMirror) {
    outline: none;
    min-height: 300px;
    font-size: 14px;
    line-height: 1.6;
    color: #374151;
    padding: 20px;

    p {
      margin: 0 0 16px 0;

      &:last-child {
        margin-bottom: 0;
      }

      &.is-editor-empty:first-child::before {
        content: attr(data-placeholder);
        float: left;
        color: #9ca3af;
        pointer-events: none;
        height: 0;
      }
    }

    h1, h2, h3, h4, h5, h6 {
      margin: 24px 0 16px 0;
      font-weight: 600;
      line-height: 1.3;

      &:first-child {
        margin-top: 0;
      }
    }

    h1 { font-size: 28px; }
    h2 { font-size: 24px; }
    h3 { font-size: 20px; }
    h4 { font-size: 18px; }
    h5 { font-size: 16px; }
    h6 { font-size: 14px; }

    ul, ol {
      margin: 16px 0;
      padding-left: 24px;

      li {
        margin: 4px 0;
      }
    }

    blockquote {
      border-left: 4px solid #e5e7eb;
      padding-left: 16px;
      margin: 16px 0;
      color: #6b7280;
      font-style: italic;
    }

    code {
      background: #f3f4f6;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
    }

    pre {
      background: #1f2937;
      color: #f9fafb;
      padding: 16px;
      border-radius: 8px;
      margin: 16px 0;
      overflow-x: auto;

      code {
        background: none;
        padding: 0;
        color: inherit;
      }
    }

    mark {
      background: #fef3c7;
      padding: 2px 4px;
      border-radius: 2px;
    }

    strong {
      font-weight: 600;
    }

    em {
      font-style: italic;
    }
  }

}

.editor-content.read-only {
  .editor-wrapper {
    :deep(.ProseMirror) {
      min-height: auto;
      padding: 0;
    }
  }
}

.floating-ai-button {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 100;
  transition: opacity 0.3s ease;

  &.disabled {
    opacity: 0.6;
  }
}

.ai-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  cursor: pointer;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  font-size: 20px;
  position: relative;

  svg {
    font-size: 20px;
  }

  &:hover:not(.disabled) {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
  }

  &:active:not(.disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
    pointer-events: none;
    background: #9ca3af;
    box-shadow: 0 2px 8px rgba(156, 163, 175, 0.2);

    &:hover {
      transform: none;
    }
  }

  // 添加脉冲动画效果
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    opacity: 0;
    z-index: -1;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 0;
      transform: scale(1);
    }
    50% {
      opacity: 0.3;
      transform: scale(1.1);
    }
    100% {
      opacity: 0;
      transform: scale(1.2);
    }
  }
}

.floating-chat-window {
  position: fixed;
  top: 80px;
  right: 20px;
  width: 380px;
  height: calc(100vh - 120px);
  max-height: 600px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  transform: translateX(calc(100% + 20px));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 150;
  overflow: hidden;
  pointer-events: auto;
  &.visible {
    transform: translateX(0);
  }
}

// 动画
.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.fade-slide-enter-from, .fade-slide-leave-to {
  opacity: 0;
  transform: translateX(40px);
}

.chat-window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  flex-shrink: 0;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
  }

  .header-actions {
    display: flex;
    gap: 4px;
  }

  .action-btn {
    background: none;
    border: none;
    font-size: 16px;
    color: #64748b;
    cursor: pointer;
    line-height: 1;
    padding: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      background: rgba(59, 130, 246, 0.1);
      color: #3b82f6;
    }

    &.clear-btn {
      color: #ef4444;
      &:hover {
        background: rgba(239, 68, 68, 0.1);
        color: #dc2626;
      }
      
      svg {
        font-size: 14px;
      }
    }

    &.close-btn {
      color: #64748b;
      &:hover {
        background: rgba(100, 116, 139, 0.1);
        color: #475569;
      }
      
      svg {
        font-size: 16px;
      }
    }
  }
}

.chat-window-body {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: #ffffff;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    
    &:hover {
      background: #94a3b8;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    text-align: center;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.7;
      color: #9ca3af;
      
      svg {
        font-size: 48px;
      }
    }

    p {
      margin: 4px 0;
      color: #64748b;
      font-size: 14px;

      &.empty-hint {
        font-size: 12px;
        opacity: 0.8;
        color: #94a3b8;
      }
    }
  }
  
  .message-item {
    display: flex;
    margin-bottom: 16px;
    max-width: 90%;
    animation: fadeInUp 0.3s ease;

    .message-avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #f1f5f9;
      color: #475569;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-top: 4px;
      border: 1px solid #e2e8f0;
    }
    
    .message-content-wrapper {
      margin-left: 8px;
      display: flex;
      align-items: flex-start;
      flex-direction: column;
    }

    .message-content {
      padding: 10px 14px;
      border-radius: 12px;
      background: #f8fafc;
      color: #334155;
      font-size: 13px;
      line-height: 1.5;
      word-break: break-word;
      max-width: 100%;
      border: 1px solid #e2e8f0;
    }

    &.message-user {
      margin-left: auto;
      flex-direction: row-reverse;

      .message-content-wrapper {
        margin-left: 0;
        margin-right: 8px;
        align-items: flex-end;
      }

      .message-content {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border-color: #3b82f6;
      }

      .message-avatar {
        background: #dbeafe;
        border-color: #93c5fd;
      }
    }

    &.message-system {
      max-width: 100%;
      
      .message-content {
        width: 100%;
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        color: #92400e;
        font-size: 12px;
        text-align: center;
        border-radius: 8px;
        padding: 8px 12px;
        border-color: #f3d55e;
      }
    }
    
    .typing-indicator {
      display: flex;
      gap: 2px;
      margin-top: 8px;
      
      span {
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: #64748b;
        animation: typing 1.4s infinite ease-in-out;
        
        &:nth-child(2) {
          animation-delay: 0.2s;
        }
        
        &:nth-child(3) {
          animation-delay: 0.4s;
        }
      }
    }
  }

  @keyframes fadeInUp {
    from { 
      opacity: 0; 
      transform: translateY(20px); 
    }
    to { 
      opacity: 1; 
      transform: translateY(0); 
    }
  }

  @keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
  }
}

.chat-window-footer {
  padding: 0;
  border-top: none;
  background: transparent;
  flex-shrink: 0;

  .input-wrapper {
    padding: 0;
    margin: 0;
  }

  .input-container {
    position: relative;
    background: white;
    border-top: 1px solid #e5e7eb;
  }

  .prompt-input {
    width: 100%;
    border: none;
    border-radius: 0;
    padding: 16px 60px 16px 16px;
    font-size: 14px;
    color: #1e293b;
    resize: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
    outline: none;
    transition: all 0.2s;
    font-family: inherit;
    background: white;
    box-shadow: none;

    &:focus {
      box-shadow: none;
      border: none;
    }

    &::placeholder {
      color: #94a3b8;
    }
  }

  .send-btn {
    position: absolute;
    bottom: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    padding: 0;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
      box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .floating-chat-window {
    top: 70px;
    right: 16px;
    left: 16px;
    width: auto;
    height: calc(100vh - 110px);
  }
}

// 为不同类型的对话框添加特定样式
.ai-qa-dialog {
  .chat-window-header h3 {
    color: #3b82f6;
  }
  
  .empty-icon {
    color: #3b82f6;
  }
}

.ai-polish-dialog {
  .chat-window-header h3 {
    color: #8b5cf6;
  }
  
  .empty-icon {
    color: #8b5cf6;
  }
  
  .message-actions {
    margin-top: 12px;
    display: flex;
    gap: 8px;
    
    .apply-btn {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 8px 16px;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 6px;
      
      &:hover {
        background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(139, 92, 246, 0.3);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
    
    .reject-btn {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 8px 16px;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 6px;
      
      &:hover {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
  }
  
  .message-status {
    margin-top: 8px;
    
    .status-icon {
      font-size: 14px;
      
      &.applied {
        color: #10b981;
      }
      
      &.rejected {
        color: #ef4444;
      }
    }
  }
}

.ai-draft-dialog {
  .chat-window-header h3 {
    color: #10b981;
  }
  
  .empty-icon {
    color: #10b981;
  }
  
  .message-actions {
    margin-top: 12px;
    display: flex;
    gap: 8px;
    
    .apply-btn {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 8px 16px;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 6px;
      
      &:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
    
    .reject-btn {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 8px 16px;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 6px;
      
      &:hover {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
  }
  
  .message-status {
    margin-top: 8px;
    
    .status-icon {
      font-size: 14px;
      
      &.applied {
        color: #10b981;
      }
      
      &.rejected {
        color: #ef4444;
      }
    }
  }
}

.ai-check-dialog {
  .chat-window-header h3 {
    color: #f59e0b;
  }

  .empty-icon {
    color: #f59e0b;
  }
}

// 字数统计显示
.word-count-display {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  color: #6b7280;
  display: flex;
  gap: 12px;
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;

  span {
    white-space: nowrap;
  }
}

// 表格样式增强
:deep(.ProseMirror) {
  table {
    border-collapse: collapse;
    margin: 16px 0;
    overflow: hidden;
    table-layout: fixed;
    width: 100%;
    border: 1px solid #e5e7eb;
    border-radius: 6px;

    td, th {
      border: 1px solid #e5e7eb;
      box-sizing: border-box;
      min-width: 1em;
      padding: 8px 12px;
      position: relative;
      vertical-align: top;

      > * {
        margin-bottom: 0;
      }
    }

    th {
      background-color: #f9fafb;
      font-weight: 600;
      text-align: left;
    }

    .selectedCell:after {
      background: rgba(59, 130, 246, 0.1);
      content: "";
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      pointer-events: none;
      position: absolute;
      z-index: 2;
    }

    .column-resize-handle {
      background-color: #3b82f6;
      bottom: -2px;
      position: absolute;
      right: -2px;
      pointer-events: none;
      top: 0;
      width: 4px;
    }
  }

  // 代码块样式增强
  .code-block {
    background: #1f2937;
    color: #f9fafb;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    padding: 16px;
    border-radius: 8px;
    margin: 16px 0;
    overflow-x: auto;
    font-size: 14px;
    line-height: 1.5;

    &:before {
      content: attr(data-language);
      position: absolute;
      top: 8px;
      right: 12px;
      font-size: 12px;
      color: #9ca3af;
      text-transform: uppercase;
    }
  }
}

// 全屏模式样式
.inline-editor-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
}

// 简化的AI工具栏样式
.simple-ai-toolbar {
  position: fixed;
  z-index: 1000;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 6px;
  display: flex;
  gap: 4px;

  .ai-quick-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: #f8fafc;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #e2e8f0;
      color: #475569;
      transform: translateY(-1px);
    }

    &.polish {
      &:hover {
        background: #ddd6fe;
        color: #8b5cf6;
      }
    }

    &.draft {
      &:hover {
        background: #d1fae5;
        color: #10b981;
      }
    }

    &.check {
      &:hover {
        background: #fef3c7;
        color: #f59e0b;
      }
    }

    &.qa {
      &:hover {
        background: #dbeafe;
        color: #3b82f6;
      }
    }

    &.close {
      &:hover {
        background: #fee2e2;
        color: #ef4444;
      }
    }

    svg {
      font-size: 14px;
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  .editor-menubar {
    padding: 8px 12px;

    .menu-group {
      gap: 1px;
    }

    .menu-button {
      width: 28px;
      height: 28px;
      font-size: 12px;
    }
  }

  .floating-toolbar {
    bottom: 16px;
    right: 16px;

    .toolbar-item {
      width: 28px;
      height: 28px;

      svg {
        font-size: 14px;
      }
    }
  }

  .word-count-display {
    bottom: 60px;
    right: 16px;
    font-size: 11px;
    padding: 4px 8px;
    gap: 8px;
  }

  .simple-ai-toolbar {
    .ai-quick-btn {
      width: 28px;
      height: 28px;

      svg {
        font-size: 12px;
      }
    }
  }
}

// AI 生成内容样式
:deep(.ProseMirror) {
  // 为 AI 生成的内容添加特殊样式
  .ai-generated-content {
    position: relative;
    border-left: 3px solid #3b82f6;
    margin: 16px 0 16px 8px;
    padding: 12px 16px 12px 20px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(59, 130, 246, 0.05) 100%);
    border-radius: 0 8px 8px 0;
    animation: ai-content-appear 0.5s ease-out;
    
    &::before {
      content: "AI";
      position: absolute;
      top: -6px;
      left: -6px;
      background: #3b82f6;
      color: white;
      font-size: 10px;
      font-weight: 600;
      padding: 3px 6px;
      border-radius: 4px;
      line-height: 1;
      box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
      z-index: 1;
    }
    
    // 起稿内容样式
    &.ai-draft {
      border-left-color: #10b981;
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.02) 0%, rgba(16, 185, 129, 0.05) 100%);
      
      &::before {
        content: "起稿";
        background: #10b981;
        color: white;
        box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
      }
    }
    
    // 润色内容样式
    &.ai-polish {
      border-left-color: #8b5cf6;
      background: linear-gradient(135deg, rgba(139, 92, 246, 0.02) 0%, rgba(139, 92, 246, 0.05) 100%);
      
      &::before {
        content: "润色";
        background: #8b5cf6;
        color: white;
        box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
      }
    }
  }

  // Markdown 转换后的内容优化样式
  h1, h2, h3, h4, h5, h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.3;
  }
  
  h1 { font-size: 28px; }
  h2 { font-size: 24px; }
  h3 { font-size: 20px; }
  h4 { font-size: 18px; }
  h5 { font-size: 16px; }
  h6 { font-size: 14px; }
  
  p {
    margin: 8px 0;
    line-height: 1.6;
    color: #374151;
  }
  
  ul, ol {
    margin: 12px 0;
    padding-left: 24px;
    
    li {
      margin: 4px 0;
      line-height: 1.6;
      color: #374151;
      
      p {
        margin: 4px 0;
      }
    }
  }
  
  code {
    background: rgba(139, 92, 246, 0.1);
    color: #7c3aed;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    font-weight: 500;
  }
  
  pre {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    overflow-x: auto;
    
    code {
      background: none;
      color: #374151;
      padding: 0;
      border-radius: 0;
      font-weight: 400;
    }
  }
  
  blockquote {
    border-left: 4px solid #e2e8f0;
    margin: 16px 0;
    padding: 12px 20px;
    background: #f8fafc;
    border-radius: 0 8px 8px 0;
    font-style: italic;
    color: #64748b;
    
    p {
      margin: 8px 0;
      color: inherit;
    }
  }
  
  table {
    border-collapse: collapse;
    margin: 16px 0;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    th, td {
      border: 1px solid #e2e8f0;
      padding: 12px 16px;
      text-align: left;
    }
    
    th {
      background: #f8fafc;
      font-weight: 600;
      color: #374151;
    }
    
    tr:nth-child(even) {
      background: #f9fafb;
    }
    
    tr:hover {
      background: #f3f4f6;
    }
  }
  
  // 强调样式
  strong {
    font-weight: 600;
    color: #1e293b;
  }
  
  em {
    font-style: italic;
    color: #475569;
  }
  
  // 链接样式
  a {
    color: #3b82f6;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;
    
    &:hover {
      border-bottom-color: #3b82f6;
      color: #2563eb;
    }
  }
  
  // 分隔线样式
  hr {
    border: none;
    height: 2px;
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    margin: 24px 0;
    border-radius: 1px;
  }
}

// AI 内容出现动画
@keyframes ai-content-appear {
  0% {
    opacity: 0;
    transform: translateX(-20px);
    border-left-width: 0;
  }
  50% {
    opacity: 0.5;
    transform: translateX(-10px);
    border-left-width: 2px;
  }
  100% {
    opacity: 1;
    transform: translateX(0);
    border-left-width: 3px;
  }
}
</style>

