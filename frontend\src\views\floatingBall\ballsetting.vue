<template>
  <div class="app">
    <!-- 快捷访问区 -->
    <div class="quick-access-area">
      <div v-for="(item, index) in quickAccess" :key="index" class="quick-access-item" @click="back">
        <el-icon :size="20"><component :is="item.icon" /></el-icon>
        <span>{{ item.title }}</span>
      </div>
    </div>
    <SoftwareDialog :items-per-row="4" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { ElIcon } from "element-plus";
import { Back } from "@element-plus/icons-vue";
import router from "@/routes";
import SoftwareDialog from "@/components/Software/SoftwareDialog.vue";

const quickAccess = ref([{ title: "返回", icon: Back }]);

const back = () => {
  router.back();
};
</script>

<style scoped>
.app {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f0f0f0;
  padding: 20px;
}

.search-area {
  margin-bottom: 20px;
}

.search-input {
  width: 400px;
  background-color: #fff;
  border-radius: 4px;
}

.search-results {
  margin-top: 10px;
  max-height: 200px;
  overflow-y: auto;
}

.search-result-item {
  padding: 10px;
  border-bottom: 1px solid #ccc;
  cursor: pointer;
}

.search-result-item:hover {
  background-color: #f0f0f0;
}

.quick-access-area {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.quick-access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.quick-access-item:hover {
  color: #409eff;
}

.recommend-area {
  width: 400px;
}

.recommend-area h3 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.recommend-list {
  list-style-type: none;
  padding: 0;
}

.recommend-list li {
  padding: 5px 0;
  background-color: #fff;
  border: 1px solid #ccc;
  margin: 5px 0;
}

.recommend-list li:hover {
  background-color: #f0f0f0;
}
</style>
