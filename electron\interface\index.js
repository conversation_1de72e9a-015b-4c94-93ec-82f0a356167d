/**
 * 下载项状态
 * @typedef {'progressing'|'completed'|'cancelled'|'interrupted'} DownloadItemState
 */

/**
 * IPC 事件名称
 * @typedef {'openDownloadManager'
 * | 'getDownloadData'
 * | 'newDownloadFile'
 * | 'retryDownloadFile'
 * | 'openFileDialog'
 * | 'openFile'
 * | 'openFileInFolder'
 * | 'initDownloadItem'
 * | 'pauseOrResume'
 * | 'removeDownloadItem'
 * | 'clearDownloadDone'
 * | 'newDownloadItem'
 * | 'downloadItemUpdate'
 * | 'downloadItemDone'} IPCEventName
 */

/**
 * 新下载任务
 * @typedef {Object} INewDownloadFile
 * @property {string} url 下载文件 url
 * @property {string} [fileName] 文件名（可选）
 * @property {string} path 保存路径
 */

/**
 * 下载文件详情
 * @typedef {Object} IDownloadFile
 * @property {string} id 下载项 ID
 * @property {string} url 下载文件 url
 * @property {string} icon 图标路径
 * @property {string} fileName 文件名
 * @property {string} path 保存路径
 * @property {DownloadItemState} state 下载状态
 * @property {number} startTime 开始时间
 * @property {number} speed 下载速度
 * @property {number} progress 下载进度
 * @property {number} totalBytes 总字节数
 * @property {number} receivedBytes 已接收字节数
 * @property {boolean} paused 是否暂停
 * @property {*} _sourceItem 内部下载项（类型依赖 Electron 的 DownloadItem，此处为 any）
 */

/**
 * 下载数据汇总
 * @typedef {Object} IDownloadBytes
 * @property {number} receivedBytes 已接收字节数
 * @property {number} totalBytes 总字节数
 */

/**
 * 分页信息
 * @typedef {Object} IPagination
 * @property {number} pageIndex 当前页码
 * @property {number} pageCount 总页数
 */

/**
 * 添加下载项参数
 * @typedef {Object} IAddDownloadItem
 * @property {*} item Electron 的 DownloadItem 对象
 * @property {string[]} downloadIds 已下载项 ID 数组
 * @property {IDownloadFile[]} data 下载文件详细数据数组
 * @property {INewDownloadFile|null} newDownloadItem 新下载任务数据
 */

/**
 * 更新下载项参数
 * @typedef {Object} IUpdateDownloadItem
 * @property {*} item Electron 的 DownloadItem 对象
 * @property {IDownloadFile[]} data 下载文件详细数据数组
 * @property {IDownloadFile} downloadItem 当前下载项
 * @property {number} prevReceivedBytes 前一次接收的字节数
 * @property {DownloadItemState} state 下载状态
 */

// 由于这是类型定义文件，无真正导出内容，可根据实际需要导出文档或留空
module.exports = {};
