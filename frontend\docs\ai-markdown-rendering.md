# AI 文章起稿 Markdown 渲染功能说明

## 功能概述

本功能实现了在"文章起稿"中AI回复的消息点击应用后，能够以 Markdown 格式渲染到 Tiptap 编辑器中。

## 主要特性

### 1. 智能内容检测
- 自动检测 AI 回复内容是否包含 Markdown 语法
- 支持的 Markdown 语法包括：
  - 标题 (`# ## ### 等`)
  - 粗体 (`**text**`)
  - 斜体 (`*text*`)
  - 行内代码 (`` `code` ``)
  - 代码块 (`` ```code``` ``)
  - 列表 (`- * + 1.`)
  - 引用 (`> text`)
  - 链接 (`[text](url)`)
  - 图片 (`![alt](url)`)
  - 表格 (`|col1|col2|`)
  - 分隔线 (`---`)

### 2. 双重解析引擎
- **优先使用**: `@tiptap/pm/markdown` 的 `defaultMarkdownParser`
- **备用方案**: `markdown-it` 解析器
- 确保 Markdown 内容能够正确转换为 Tiptap 编辑器格式

### 3. 视觉区分样式
- AI 生成的内容具有特殊的视觉样式：
  - 左侧彩色边框标识
  - 半透明背景色
  - 标签显示内容类型（"起稿"/"润色"）
  - 入场动画效果

### 4. 内容类型支持
- **文章起稿**: 绿色标识，总是添加到文档末尾
- **文本润色**: 紫色标识，可替换选中文本或添加到指定位置

## 技术实现

### 核心文件

1. **`/utils/markdownToTiptap.ts`**
   - `markdownToTiptapJson()`: 使用 Tiptap 原生解析器
   - `markdownToTiptapHtml()`: 使用 markdown-it 解析器
   - `hasMarkdownSyntax()`: 检测 Markdown 语法
   - `smartConvertContent()`: 智能内容转换

2. **`InlineNoteEditor.vue`**
   - `applyDraftResult()`: 应用起稿结果
   - `applyPolishResult()`: 应用润色结果
   - 添加了 Markdown 检测和转换逻辑

### 工作流程

```
AI回复内容 → 检测Markdown语法 → 选择解析器 → 转换为编辑器格式 → 添加样式类 → 插入到编辑器
```

### 样式类结构

```css
.ai-generated-content {
  /* 基础AI内容样式 */
}

.ai-generated-content.ai-draft {
  /* 起稿内容特定样式 */
}

.ai-generated-content.ai-polish {
  /* 润色内容特定样式 */
}
```

## 使用方法

1. 在笔记编辑器中选中一段文本（可选）
2. 点击"文章起稿"按钮
3. 在AI对话框中输入起稿要求
4. AI 生成包含 Markdown 语法的回复
5. 点击回复消息右侧的"✓"按钮应用
6. Markdown 内容会被正确渲染并插入到编辑器中

## 示例效果

### 输入的 Markdown 内容：
```markdown
# 项目计划

## 第一阶段
- **需求分析**
- *技术调研*
- `代码实现`

### 关键特性
1. 响应式设计
2. 用户体验优化
3. 性能提升

> 这是一个重要的项目里程碑

## 代码示例
```javascript
function hello() {
  console.log("Hello, World!");
}
```

## 项目链接
[项目文档](https://example.com)
```

### 渲染后的效果：
- 标题会以正确的层级显示
- 粗体、斜体、代码等格式会被保留
- 列表会正确缩进和编号
- 引用块会有特殊样式
- 代码块会有语法高亮背景
- 链接可以点击跳转
- 整个内容会有绿色左边框和"起稿"标签

## 注意事项

1. 如果 `@tiptap/pm/markdown` 解析失败，会自动回退到 `markdown-it`
2. 纯文本内容不会触发 Markdown 解析，会直接以段落形式插入
3. AI 生成的内容会自动添加视觉标识，便于用户区分
4. 所有 Markdown 元素都会应用编辑器的统一样式主题

## 扩展性

该实现支持：
- 添加更多 Markdown 语法检测规则
- 自定义样式主题
- 支持更多内容类型（如"检查"功能）
- 集成其他 Markdown 解析器
