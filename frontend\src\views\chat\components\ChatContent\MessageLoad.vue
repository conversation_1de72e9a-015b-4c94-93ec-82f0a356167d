<template>
  <div class="text-xs absolute left-0 right-0 bottom-0 px-3 py-1 bg-black/20 text-right">
    <a class="cursor-pointer text-white" @click="downloadFile(props.data, 1)">下载</a>
    <!-- <span class="cursor-pointer text-white" @click="downloadFile(props.data, 2)">预览</span> -->
  </div>
</template>

<script setup>
import { onMounted } from "vue";
import { useFileStore } from "@/stores/modules/file";
import * as FileApi from "@/api/infra/file";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { generateRandomId } from "@/utils/common";
import IpcListenerManager from "@/ipc/IpcListener";
import "@/utils/js/base64.min.js"
import { ipc } from "@/utils/ipcRenderer";
const ipcManager = IpcListenerManager.getInstance();

const DownLoadStore = useFileStore();

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
});
onMounted(() => {
  ipcManager.add("MessageLoad", ipcApiRoute.downloadProgress, (event, arg) => {
    DownLoadStore.updateDwonloadFiles(arg);
  });
});

const downloadFile = async (item, num) => {
  if (num == 1) {
    DownLoadStore.badge++;
    const fileId = generateRandomId(10);
    const downloadUrl = import.meta.env.VITE_API_URL2 + `?id=${item.contentId}`
    let params = {
      fetchUrl: downloadUrl,
      fileId: fileId,
      fileName: item.msg,
    }
    ipc.send(ipcApiRoute.downloadFileByStream, params)
    // let def_file = {
    //   file_id: fileId,
    //   fileName: item.msg,
    //   state: "start",
    //   speed: 0,
    //   progress: 0,
    //   totalBytes: 0,
    //   receivedBytes: 0,
    //   paused: false
    // };
    // DownLoadStore.updateDwonloadFiles(def_file);
    // try {
    //   //let startTime = new Date().getTime(),
    //   // receivedBytes = 0;

    //   const response = await axios.get('/admin-api/infra/file/downloadByUrl', {
    //     responseType: 'blob', params: { id: item.contentId },
    //     // onDownloadProgress: progressEvent => {
    //     //   const { loaded, total } = progressEvent;
    //     //   const currentTime = new Date().getTime();
    //     //   const timeElapsed = (currentTime - startTime) / 1000;
    //     //   const speed = (loaded - receivedBytes) / timeElapsed;
    //     //   startTime = currentTime;
    //     //   receivedBytes = loaded;
    //     //   DownLoadStore.updateDwonloadFiles({
    //     //     file_id: fileId,
    //     //     totalBytes: total,
    //     //     receivedBytes: loaded,
    //     //     progress: Math.round((loaded * 100) / total) / 100,
    //     //     speed
    //     //   });
    //     // },
    //   })
    //   if (response) {
    //     DownLoadStore.downloadList = DownLoadStore.downloadList.filter(file => file.file_id !== fileId);
    //     fileDownload(response, item);
    //   }
    // } catch (error) {
    //   def_file.state = "error";
    //   DownLoadStore.updateDwonloadFiles(def_file);
    // }
  } else {
    const resMsg = await FileApi.getFileUrl({ id: item.contentId });
    window.open(
      import.meta.env.VITE_CHAT_PREVIEW +
      "/onlinePreview?url=" +
      encodeURIComponent(window.Base64.encode(resMsg.data.url + "?fullfilename=" + resMsg.data.name))
    );
  }
};
const fileDownload = async (response, item) => {
  let blobFile = new Blob([response.data]);
  const url = window.URL.createObjectURL(blobFile);
  const a = document.createElement('a');
  a.href = url;
  a.download = item.msg;
  document.body.appendChild(a);
  a.click();
  a.remove();
  window.URL.revokeObjectURL(url);
};
// onUnmounted(() => {
//   ipc.removeAllListeners(ipcApiRoute.downloadProgress);
// });
</script>
