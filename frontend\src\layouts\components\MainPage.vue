<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";

const keepAliveStore = useKeepAliveStore();
const { keepAliveName } = storeToRefs(keepAliveStore);
</script>

<template>
  <el-main>
    <router-view v-slot="{ Component, route }">
      <transition appear name="fade-transform" mode="out-in">
        <keep-alive :include="keepAliveName">
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
      </transition>
    </router-view>
  </el-main>
</template>

<style lang="scss" scoped>
.el-main {
  @apply p-0;
  overflow: hidden;
  background: #fff;
  border-radius: 0 0 7px 0;
}
</style>
