const CoreWindow = require("ee-core/electron/window");
const { webContents } = require("electron");
const Services = require("ee-core/services");
const Log = require("ee-core/log");

class LoadingController {
  constructor() {
    this.loadingWin = null;
  }

  // 隐藏主窗口 显示loading窗口
  async show() {
    const mainWindow = CoreWindow.getMainWindow();
    if (mainWindow.isVisible()) {
      mainWindow.hide();
    }

    this.loadingWin = await Services.get("os").createChildWindow("#/loading", {
      width: 200,
      minWidth: 200,
      height: 180,
      minHeight: 180,
      transparent: true, // 设置透明
      hasShadow: false, // 不显示阴影
      show: true,
    });

    await this.sleep(1000);
  }

  // 修改loading窗口文案
  async toggleText(text) {
    Log.info(this.loadingWin != null);
    if (!this.loadingWin) return;
    webContents
      .fromId(this.loadingWin.webContents.id)
      .send("when-loading-state-update", text);
    await this.sleep(800);
  }

  // 延迟
  sleep(time = 3000) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve();
      }, time);
    });
  }
}

module.exports = LoadingController;
