<template>
  <div class="cool-loading">
    <div class="loading"></div>
    <div class="message">{{ loadingText }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import IpcListenerManager from "@/ipc/IpcListener";
const ipcManager = IpcListenerManager.getInstance();

// 监听更新状态，替换文案
ipcManager.add("page-listen", "when-loading-state-update", (_, text) => {
  loadingText.value = text;
});

const loadingText = ref("版本更新中...");
</script>

<style scoped>
.cool-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: #ecf0f1;
  border-radius: 12px;
  overflow: hidden;
  background-color: #fff;
}

.loading {
  position: relative;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: transparent;
  border: 8px solid #3498db;
  animation: rotate 1.2s linear infinite;
}

.loading::before,
.loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  background-color: #3498db;
  border-radius: 50%;
}

.loading::before {
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
}

.loading::after {
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
}

.message {
  margin-top: 10px;
  font-size: 18px;
  color: #3498db;
  padding: 0 4px;
  text-align: center;
  min-height: 30px;
}
</style>
