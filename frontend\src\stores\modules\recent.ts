import { defineStore } from "pinia";
import { Recent } from "../interface";
import piniaPersistConfig from "../helper/persist";
import * as recentsApi from "@/api/modules/recents";
import { useTalkStore } from "./talk";
import { isEE, ipc } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { getPersonInfo } from "@/api/modules/contact";

let ifAddRecent = false;
export const useRecentStore = defineStore("lark-recent", {
  state: (): Recent => ({
    listRecents: []
  }),
  getters: {},
  actions: {
    // 获取最近联系人列表
    async getListRecents() {
      let res: any = {
        data: {
          list: []
        }
      };
      if (isEE) {
        let list = await ipc.invoke(ipcApiRoute.getContactList);
        let params: { lastUpdateTime?: string } = {};
        if (list.length) {
          let _list = JSON.parse(JSON.stringify(list));
          params.lastUpdateTime = _list.reduce((pre: number, curr: any) => {
            if (pre < curr.updateTime) pre = curr.updateTime;
            return pre;
          }, 0);
        }
        const lastestRes = await recentsApi.getListRecents(params);

        if (lastestRes.data?.list) {
          await ipc.invoke(ipcApiRoute.syncContactData, JSON.stringify(lastestRes.data.list));
          // 更新最新列表
          res.data.list = await ipc.invoke(ipcApiRoute.getContactList);
        } else {
          res.data.list = list;
        }
      } else {
        res = await recentsApi.getListRecents();
      }
      res.data.list.forEach((item: any) => {
        item.atIds = [];
      });
      this.listRecents = res.data.list;

      return this.listRecents;
    },
    // 新增
    async addListRecents(recent: any) {
      if (ifAddRecent) return;
      ifAddRecent = true;
      let index = this.listRecents.findIndex((item: any) => item.contactId == recent.contactId);
      if (index < 0) {
        const res: any = await recentsApi.saveRecentChat(recent);
        if (res.code == 0) {
          let newItem = res.data;
          newItem.avatar = recent.avatar;
          newItem.contactName = recent.contactName;
          newItem.secret = recent.secret;

          if (isEE) {
            ipc.send(ipcApiRoute.addContact, JSON.stringify(newItem));
          }

          this.listRecents.push(newItem);
          this.updateOrder(recent.contactId);
          ifAddRecent = false;
          return true;
        } else {
          ifAddRecent = false;
          return false;
        }
      } else {
        if (recent.chatType == 0) {
          await getPersonInfo(recent.contactId);
        }
        this.updateOrder(recent.contactId);
        ifAddRecent = false;
        return true;
      }
    },
    // 删除
    async deleteListRecents(id: string) {
      const res: any = await recentsApi.deleteRecentChat(id);
      if (res.code == 0) {
        if (id == "all") {
          isEE && ipc.send(ipcApiRoute.removeAllContact);
          this.listRecents = [];
        } else {
          const index = this.listRecents.findIndex(item => item.id == id);
          isEE && ipc.send(ipcApiRoute.removeContact, this.listRecents[index].contactId);
          this.listRecents.splice(index, 1);
        }
      }
    },
    // 置顶，取消置顶
    topListRecents(id: string, isTop: number) {
      const index = this.listRecents.findIndex((item: any) => item.id == id);
      this.listRecents[index].isTop = isTop;
      this.updateSqlitRecent(this.listRecents[index]);
      this.updateOrder(this.listRecents[index].contactId);
    },
    // 更新联系人顺序
    updateOrder(contactId: string) {
      const index = this.listRecents.findIndex((item: any) => item.contactId == contactId);
      const [pendingContact] = this.listRecents.splice(index, 1);
      const pinnedContactNumber = this.listRecents.reduce((total, currentItem) => {
        if (currentItem.isTop) total++;
        return total;
      }, 0);
      if (pendingContact.isTop) {
        this.listRecents.unshift(pendingContact);
      } else {
        this.listRecents.splice(pinnedContactNumber, 0, pendingContact);
      }
    },
    // 更新最后一条消息
    updateLastMsg(contactId: string, msg: string, msgId: string, msgType: number, msgTime: any) {
      const index = this.listRecents.findIndex((item: any) => item.contactId === contactId);
      this.listRecents[index].recentMsg = msg;
      this.listRecents[index].recentMsgId = msgId;
      this.listRecents[index].recentMsgType = msgType;
      this.listRecents[index].updateTime = msgTime;

      this.updateSqlitRecent(this.listRecents[index]);
    },
    // 更新撤回消息
    updateCancel(contactId: string, name: string) {
      const index = this.listRecents.findIndex((item: any) => item.contactId === contactId);
      this.listRecents[index].recentMsgType = 0;
      this.listRecents[index].recentMsg = `${name}撤回了一条消息`;

      this.updateSqlitRecent(this.listRecents[index]);
    },
    // 更新最近联系人群组名称
    updateGroupName(id: string, name: string) {
      const index = this.listRecents.findIndex((item: any) => item.contactId == id);
      this.listRecents[index].contactName = name;

      this.updateSqlitRecent(this.listRecents[index]);
    },
    // 清除未读计数
    async clearUnreadNum(contactId: string) {
      const index = this.listRecents.findIndex((item: any) => item.contactId == contactId);
      if (this.listRecents[index]?.unreadNum) {
        await recentsApi.clearUnreadNum({ id: contactId });
      }
      this.listRecents[index].unreadNum = 0;

      this.updateSqlitRecent(this.listRecents[index]);
    },
    // 增加未读计数
    async addUnreadNum(contactId: string) {
      const talkStore = useTalkStore();
      const index = this.listRecents.findIndex((item: any) => item.contactId == contactId);
      if (talkStore.activeChatId == contactId) {
        this.listRecents[index].unreadNum = 0;
        // await recentsApi.clearUnreadNum({ id: contactId });
      } else {
        this.listRecents[index].unreadNum++;
      }

      this.updateSqlitRecent(this.listRecents[index]);
    },
    // 清除at提示
    async clearAtNum(contactId: string) {
      const index = this.listRecents.findIndex((item: any) => item.contactId == contactId);
      if (this.listRecents[index].isAt) {
        await recentsApi.clearAtNum({ id: contactId });
      }
      this.listRecents[index].isAt = 0;

      this.updateSqlitRecent(this.listRecents[index]);
    },
    // 增加at提示
    async addAtNum(contactId: string, atIds: any) {
      const talkStore = useTalkStore();
      const index = this.listRecents.findIndex((item: any) => item.contactId == contactId);
      if (talkStore.activeChatId == contactId) {
        this.listRecents[index].isAt = 0;
        // await recentsApi.clearAtNum({ id: contactId });
      } else {
        this.listRecents[index].isAt = 1;
      }
      this.listRecents[index].atIds = atIds;

      this.updateSqlitRecent(this.listRecents[index]);
    },
    updateSqlitRecent(recent: any) {
      if (isEE) {
        ipc.send(ipcApiRoute.updateContact, JSON.stringify(recent));
      }
    }
  },
  persist: piniaPersistConfig("lark-recent-session")
});
