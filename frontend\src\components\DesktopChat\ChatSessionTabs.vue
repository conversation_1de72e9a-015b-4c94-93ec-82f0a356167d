<template>
  <div class="flex space-x-2 overflow-x-auto pb-1">
    <div
      v-for="(session, index) in sessions"
      :key="index"
      class="flex items-center space-x-1 px-3 py-1 rounded border text-sm cursor-pointer"
      :class="{
        'bg-blue-600 text-white': index === currentIndex,
        'bg-gray-700 text-gray-300 hover:bg-gray-600': index !== currentIndex
      }"
      @click="() => emit('select', index)"
    >
      <span>会话 {{ index + 1 }}</span>
      <button class="ml-1 text-xs text-red-300 hover:text-red-500" @click.stop="() => emit('delete', index)">×</button>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  sessions: string[][];
  currentIndex: number;
}>();

const emit = defineEmits<{
  (e: "select", index: number): void;
  (e: "delete", index: number): void;
}>();
</script>
