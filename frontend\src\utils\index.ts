const mode = import.meta.env.VITE_ROUTER_MODE;

/**
 * @description 使用递归过滤出需要渲染在左侧菜单的列表 (需剔除 isHide == true 的菜单)
 * @param {Array} menuList 菜单列表
 * @returns {Array}
 * */
export function getShowMenuList(menuList: Menu.MenuOptions[]): Array<any> {
  // 复制菜单列表
  let newMenuList: Menu.MenuOptions[] = JSON.parse(JSON.stringify(menuList));
  // 过滤隐藏菜单
  return newMenuList.filter(item => {
    // 如果有子菜单，则递归调用getShowMenuList函数
    item.children?.length && (item.children = getShowMenuList(item.children));
    // 如果菜单的meta属性中存在isHide字段，则过滤掉
    return !item.meta?.isHide;
  });
}

/**
 * @description 使用递归扁平化菜单，方便添加动态路由
 * @param {Array} originalMenuList 菜单列表
 * @returns {Array}
 */
export function getFlatMenuList(originalMenuList: Menu.MenuOptions[]): Menu.MenuOptions[] {
  // 复制原始菜单列表，避免修改原始数据
  const menuList: Menu.MenuOptions[] = [...originalMenuList];
  return menuList.flatMap(item => {
    // 如果菜单项有子菜单，则递归获取展开后的子菜单列表
    const childrenMenuList = item.children ? getFlatMenuList(item.children) : [];
    // 返回当前菜单项和其子菜单项的组合
    return [item, ...childrenMenuList];
  });
}

/**
 * 使用递归找出所有面包屑存储到 pinia 中
 * @param {Menu.MenuOptions[]} menuList 菜单列表
 * @param {Menu.MenuOptions[]} parent 父级菜单
 * @param {Recordable<Menu.MenuOptions[]>} result 处理后的结果
 * @returns {Recordable<Menu.MenuOptions[]>}
 */
export function getAllBreadcrumbList(
  menuList: Menu.MenuOptions[],
  parent: Menu.MenuOptions[] = [],
  result: Recordable<Menu.MenuOptions[]> = {}
): Recordable<Menu.MenuOptions[]> {
  for (const item of menuList) {
    result[item.path] = [...parent, item];
    if (item.children) getAllBreadcrumbList(item.children, result[item.path], result);
  }
  return result;
}

/**
 * @description 获取不同路由模式所对应的 url + params
 * @returns {String}
 */
export function getUrlWithParams(): string {
  const url = {
    hash: location.hash.substring(1),
    history: location.pathname + location.search
  };
  return url[mode];
}

/**
 * @description 获取当前时间对应的提示语
 * @returns {String}
 */
export function getTimeState() {
  let timeNow = new Date();
  let hours = timeNow.getHours();
  if (hours >= 6 && hours <= 10) return `早上好 ⛅`;
  if (hours >= 10 && hours <= 14) return `中午好 🌞`;
  if (hours >= 14 && hours <= 18) return `下午好 🌞`;
  if (hours >= 18 && hours <= 24) return `晚上好 🌛`;
  if (hours >= 0 && hours <= 6) return `凌晨好 🌛`;
}

