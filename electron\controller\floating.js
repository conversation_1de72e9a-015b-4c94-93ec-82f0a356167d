"use strict";

const _ = require("lodash");
const path = require("path");
const { Controller } = require("ee-core");
const Services = require("ee-core/services");
const Log = require("ee-core/log");
const {
  app: electronApp,
  dialog,
  shell,
  screen,
  BrowserWindow,
} = require("electron");
const Conf = require("ee-core/config");
const Ps = require("ee-core/ps");
// const Services = require("ee-core/services");
const Addon = require("ee-core/addon");
const CoreWindow = require("ee-core/electron/window");

// const ballSize = 90;
const winWidth = 140;
const winHeight = 180;

const dialogWidth = 550;
const dialogHeight = 600;

let floatingBallWin = null;
let floatingDialogWin = null;
let floatingDialogWin2 = null;
let viewDialogWin = null;

const cfg = Conf.getValue("floatingBall");
/**
 * 操作系统 - 功能demo
 * @class
 */
class FloatingController extends Controller {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 选择目录
   */
  selectFolder() {
    const filePaths = dialog.showOpenDialogSync({
      properties: ["openDirectory", "createDirectory"],
    });

    if (_.isEmpty(filePaths)) {
      return null;
    }

    return filePaths[0];
  }

  /**
   * 打开目录
   */
  openDirectory(args) {
    if (!args.id) {
      return false;
    }
    let dir = "";
    if (path.isAbsolute(args.id)) {
      dir = args.id;
    } else {
      dir = electronApp.getPath(args.id);
    }

    shell.openPath(dir);
    return true;
  }

  getFloatingBall() {
    return floatingBallWin;
  }

  /**
   * 创建悬浮球
   */
  async createFloatingBall(userInfo) {
    // 检查机器人是否可用
    const configField =
      await Services.get("database.jsondb").getAllConfigField();
    if (configField.chatbotAvailable === false) return;

    const windowTitle = "FloatingBall" + new Date().getTime();
    const windowName = "FloatingBall" + new Date().getTime();
    const content = "#/desktop/floatingBall";
    let contentUrl = null;
    let addr = "http://localhost:8080/";
    if (Ps.isProd()) {
      const mainServer = Conf.getValue("mainServer");
      if (Conf.isFileProtocol(mainServer)) {
        addr =
          mainServer.protocol +
          path.join(Ps.getHomeDir(), mainServer.indexPath);
      } else {
        addr = mainServer.protocol + mainServer.host + ":" + mainServer.port;
      }
    }

    contentUrl = addr + content;

    const opt = {
      title: windowTitle,
      width: winWidth,
      height: winHeight,
      type: "toolbar",
      frame: false,
      resizable: false,
      show: false,
      maximizable: false,
      minimizable: false,
      fullscreenable: false,
      acceptFirstMouse: true,
      transparent: true,
      hasShadow: false,
      alwaysOnTop: cfg.isBallAlwaysOnTop,
      webPreferences: {
        // preload,
        devTools: true,
      },
    };

    floatingBallWin = Addon.get("window").create(windowName, opt);
    const winContentsId = floatingBallWin.webContents.id;
    if (configField.openDevTools) {
      floatingBallWin.webContents.openDevTools();
    }
    //通过获取用户屏幕的宽高来设置悬浮球的初始位置
    const [screenWidth, screenHeight] = [
      screen.getPrimaryDisplay().workAreaSize.width,
      screen.getPrimaryDisplay().workAreaSize.height,
    ];
    floatingBallWin.setPosition(screenWidth - 160, screenHeight - 320);
    // load page
    floatingBallWin.loadURL(contentUrl);
    floatingBallWin.on("ready-to-show", async () => {
      // 初始化悬浮窗
      floatingDialogWin = await this.createFloatingDialog(
        "#/desktop/balldialog"
      );
      floatingDialogWin2 = await this.createFloatingDialog(
        "#/desktop/balldialog2"
      );

      floatingDialogWin.on("show", () => {
        if (floatingDialogWin2.isVisible()) {
          floatingDialogWin2.hide();
        }
      });
      floatingDialogWin2.on("show", () => {
        if (floatingDialogWin.isVisible()) {
          floatingDialogWin.hide();
        }
      });
    });
    floatingBallWin.on("close", () => {
      floatingBallWin = null;
    });

    // 跟随主窗口可见性
    const mainWindow = CoreWindow.getMainWindow();
    mainWindow.on("show", () => {
      // 当主窗口显示时，隐藏浮动球及悬浮窗口
      [floatingBallWin, floatingDialogWin, floatingDialogWin2].map(
        (closeWin) => {
          if (closeWin && closeWin.isVisible()) {
            closeWin.hide();
          }
        }
      );
    });
    mainWindow.on("hide", () => {
      // 当主窗口隐藏时，显示浮动球窗口
      if (!floatingBallWin.isVisible()) {
        floatingBallWin.show();
      }
    });
    return winContentsId;
  }

  ballClick(code) {
    this.showOrHideSoftwareDialog(code);
  }

  ballLeave() {
    // floatingDialogWin.hide();
  }

  ballClose(code) {
    this.showOrHideSoftwareDialog(code);
  }

  showOrHideFloatingBall() {
    if (!floatingBallWin) return;

    if (floatingBallWin.isVisible()) {
      floatingBallWin.hide();
    } else {
      floatingBallWin.show();
      floatingBallWin.setAlwaysOnTop(true, "screen-saver");
    }
  }

  /**
   * 创建悬浮窗
   */
  async createFloatingDialog(route = "", opt = null) {
    if (!route) return;

    const configField =
      await Services.get("database.jsondb").getAllConfigField();

    const windowTitle = "FloatingDialog";
    const windowName = "FloatingDialog";

    let addr = "http://localhost:8080/";
    if (Ps.isProd()) {
      const mainServer = Conf.getValue("mainServer");
      if (Conf.isFileProtocol(mainServer)) {
        addr =
          mainServer.protocol +
          path.join(Ps.getHomeDir(), mainServer.indexPath);
      } else {
        addr = mainServer.protocol + mainServer.host + ":" + mainServer.port;
      }
    }
    let contentUrl = addr + route;

    let option = {
      title: windowTitle,
      width: dialogWidth,
      height: dialogHeight,
      show: false,
      frame: false, //要创建无边框窗口
      resizable: false, //禁止窗口大小缩放
      maximizable: false,
      minimizable: false,
      fullscreenable: false,
      alwaysOnTop: true, //窗口是否总是显示在其他窗口之前
      skipTaskbar: true,
      transparent: true, //设置透明
      hasShadow: false, //不显示阴影
      webPreferences: {
        // preload,
        devTools: true, //关闭调试工具
      },
    };
    if (opt) {
      Object.assign(option, opt);
    }

    const dialogWin = Addon.get("window").create(windowName, option);
    const winContentsId = dialogWin.webContents.id;

    // load page
    dialogWin.loadURL(contentUrl);

    dialogWin.on("ready-to-show", () => {
      if (configField.openDevTools) {
        dialogWin.webContents.openDevTools();
      }
    });

    return dialogWin;
  }

  showOrHideSoftwareDialog(code) {
    const dialogWin = code === 1 ? floatingDialogWin : floatingDialogWin2;
    if (!dialogWin) return;

    const [ballLeft, ballTop] = floatingBallWin.getPosition();
    const [ballWidth, ballHeight] = floatingBallWin.getSize();
    const [dialogWidth, dialogHeight] = dialogWin.getSize();
    // 计算对话框位置：左侧10px，垂直居中
    const targetLeft = ballLeft - dialogWidth + 10;

    const targetTop = ballTop + Math.floor(ballHeight / 2 - dialogHeight / 2);

    // 确保不会超出屏幕左边
    const minDialogLeft = 0;
    const safeLeft = Math.max(targetLeft, minDialogLeft);

    // 可选：确保不会超出屏幕顶部或底部
    const screenHeight = screen.getPrimaryDisplay().workAreaSize.height;
    const safeTop = Math.max(
      0,
      Math.min(targetTop, screenHeight - dialogHeight)
    );

    dialogWin.setPosition(safeLeft, safeTop);

    if (dialogWin.isVisible()) {
      dialogWin.hide();
    } else {
      dialogWin.show();
      dialogWin.setAlwaysOnTop(true, "screen-saver");
    }
  }

  /**
   * 悬浮球/悬浮窗拖拽移动
   */
  getMoveParams(target) {
    let moveWin, moveWinWidth, moveWinHeight;
    if (target === "ballWin") {
      moveWin = floatingBallWin;
      [moveWinWidth, moveWinHeight] = [winWidth, winHeight];
    } else if (target === "dialogWin") {
      moveWin = floatingDialogWin;
      [moveWinWidth, moveWinHeight] = [dialogWidth, dialogHeight];
    } else if (target === "dialogWin2") {
      moveWin = floatingDialogWin2;
      [moveWinWidth, moveWinHeight] = [dialogWidth, dialogHeight];
    }
    return {
      moveWin,
      moveWinWidth,
      moveWinHeight,
    };
  }
  winMove({ x, y, target }) {
    const { moveWin, moveWinWidth, moveWinHeight } = this.getMoveParams(target);
    const [screenWidth, screenHeight] = [
      screen.getPrimaryDisplay().workAreaSize.width,
      screen.getPrimaryDisplay().workAreaSize.height,
    ];

    // 处理边界
    const newX = Math.min(Math.max(x, 0), screenWidth - moveWinWidth);
    const newY = Math.min(Math.max(y, 0), screenHeight - moveWinHeight);

    moveWin.setBounds({
      x: newX,
      y: newY,
      width: moveWinWidth,
      height: moveWinHeight,
    });
  }

  /**
   * 加载扩展程序
   */
  // async loadExtension (args) {
  //   const crxFile = args[0];
  //   if (_.isEmpty(crxFile)) {
  //     return false;
  //   }
  //   const extensionId = path.basename(crxFile, '.crx');
  //   const chromeExtensionDir = chromeExtension.getDirectory();
  //   const extensionDir = path.join(chromeExtensionDir, extensionId);

  //   Log.info("[api] [example] [loadExtension] extension id:", extensionId);
  //   unzip(crxFile, extensionDir).then(() => {
  //     Log.info("[api] [example] [loadExtension] unzip success!");
  //     chromeExtension.load(extensionId);
  //   });

  //   return true;
  // }

  /**
   * 获取窗口contents id
   */
  getWCid(args) {
    // 主窗口的name默认是main，其它窗口name开发者自己定义
    const name = args;
    const id = Addon.get("window").getWCid(name);

    return id;
  }

  /**
   * 创建展示窗口
   */
  createViewDialog(param) {
    Log.info(param);
    let contentUrl = null;
    const windowTitle = "ViewDialog";
    const windowName = "ViewDialog";
    const content = "#/desktop/viewdialog?param=" + param;
    let addr = "http://localhost:8080/";
    if (Ps.isProd()) {
      const mainServer = Conf.getValue("mainServer");
      if (Conf.isFileProtocol(mainServer)) {
        addr =
          mainServer.protocol +
          path.join(Ps.getHomeDir(), mainServer.indexPath);
      } else {
        addr = mainServer.protocol + mainServer.host + ":" + mainServer.port;
      }
    }

    contentUrl = addr + content;
    let opt = {
      title: windowTitle,
      width: dialogWidth,
      height: dialogHeight,
      show: true,
      frame: false, //要创建无边框窗口
      resizable: false, //禁止窗口大小缩放
      maximizable: false,
      minimizable: false,
      fullscreenable: false,
      alwaysOnTop: true, //窗口是否总是显示在其他窗口之前
      skipTaskbar: true,
      transparent: true, //设置透明
      hasShadow: false, //不显示阴影
      webPreferences: {
        // preload,
        devTools: false, //关闭调试工具
      },
    };
    viewDialogWin = Addon.get("window").create(windowName, opt);
    const winContentsId = viewDialogWin.webContents.id;

    // load page
    viewDialogWin.loadURL(contentUrl);

    viewDialogWin.on("ready-to-show", () => {
      if (!floatingDialogWin) return;

      const [ballLeft, ballTop] = floatingBallWin.getPosition();
      const [ballWidth, ballHeight] = floatingBallWin.getSize();
      const [dialogWidth, dialogHeight] = viewDialogWin.getSize();
      // 计算对话框位置：左侧15px，垂直居中
      const targetLeft = ballLeft - dialogWidth + 15;
      const targetTop = ballTop + Math.floor(ballHeight / 2 - dialogHeight / 2);
      // 确保不会超出屏幕左边
      const minDialogLeft = 0;
      const safeLeft = Math.max(targetLeft, minDialogLeft);

      // 可选：确保不会超出屏幕顶部或底部
      const screenHeight = screen.getPrimaryDisplay().workAreaSize.height;
      const safeTop = Math.max(
        0,
        Math.min(targetTop, screenHeight - dialogHeight)
      );

      viewDialogWin.setPosition(safeLeft, safeTop);
      viewDialogWin.show();
      // if (globalSetting.isBallShow) {
      // viewDialogWin.webContents.openDevTools();
      // }
    });

    return winContentsId;
  }
  closeViewDialog() {
    if (viewDialogWin) {
      viewDialogWin.close();
      viewDialogWin = null;
    }
  }
}

FloatingController.toString = () => "[class FloatingController]";
module.exports = FloatingController;
