// /**
//  * 文件下载器
//  * @class
//  */

const Conf = require("ee-core/config");
const Ps = require("ee-core/ps");
const Services = require("ee-core/services");
const Addon = require("ee-core/addon");
const { Controller } = require("ee-core");

class FileManagerController extends Controller {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 检查是否有新版本
   */
  getDownloadList() {
    return Services.get("filemanager").getDownloadStore();
  }
  delDownloadFileById(id) {
    return Services.get("filemanager").delDownloadFileById(id);
  }
  pauseOrResumeDownloadFile(id) {
    return Services.get("filemanager").pauseOrResumeDownloadFile(id);
  }
  downloadFile(params) {
    return Services.get("filemanager").downloadFile(params);
  }
}

FileManagerController.toString = () => "[class FileManagerController]";
module.exports = FileManagerController;
