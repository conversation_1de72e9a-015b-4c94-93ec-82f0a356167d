import { defineStore } from "pinia";
import { File } from "../interface/index";
import piniaPersistConfig from "../helper/persist";

export const useFileStore = defineStore("lark-file", {
  state: (): File => ({
    downloadList: [],
    uploadList: [],
    badge: 0
  }),
  getters: {},
  actions: {
    //下载
    async updateDwonloadFiles(fileObj: any) {
      const index = this.downloadList.findIndex(item => item.file_id === fileObj.file_id);
      if (index !== -1) {
        this.downloadList[index] = { ...this.downloadList[index], ...fileObj };
      } else {
        //this.badge++;
        this.downloadList.unshift(fileObj);
      }
    },
    // 进度
    setProgress(id: any, progress: any) {
      let index = this.downloadList.findIndex((item: any) => item.id == id);
      this.downloadList[index].progress = progress;
    },
    // badge归零
    setBadge() {
      this.badge = 0;
    }
  },
  persist: piniaPersistConfig("lark-file-session")
});
