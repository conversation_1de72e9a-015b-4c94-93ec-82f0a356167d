"use strict";
const { Service } = require("ee-core");
const Services = require("ee-core/services");
const knex = require("../../database/knex");
const Log = require("ee-core/log");

/**
 * 最近联系人 数据存储
 * @class
 */
class ContactService extends Service {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 获取当前用户userid
   */
  getUserId() {
    return Services.get("database.jsondb").getUserId();
  }

  /**
   * 获取联系人列表
   */
  async getContactList() {
    const userId = await this.getUserId();
    if (!userId) return [];

    // 根据updateTime,isAt,isTop 倒序查询
    const rows = await knex("recent_contact")
      .select("*")
      .where({ userId })
      .orderByRaw("isTop DESC, isAt DESC, updateTime DESC");

    // 返回解析后的content
    return rows.map((o) => JSON.parse(o.content));
  }

  /**
   * 添加最近联系人
   * @param {string} content - JSON对象
   */
  async addContact(content) {
    const userId = await this.getUserId();
    const { contactId, updateTime, isAt, isTop } = JSON.parse(content);

    // 检查是否存在
    const existing = await knex("recent_contact")
      .where({ userId, contactId })
      .first();
    if (existing) return;

    return knex("recent_contact").insert({
      userId,
      contactId,
      updateTime,
      isAt,
      isTop,
      content,
    });
  }

  /**
   * 更新最近联系人
   * @param {string} content - JSON对象
   */
  async updateContact(content) {
    const userId = await this.getUserId();
    const { contactId, updateTime, isAt, isTop } = JSON.parse(content);
    return knex("recent_contact").where({ userId, contactId }).update({
      updateTime,
      isAt,
      isTop,
      content,
    });
  }

  /**
   * 删除单个联系人
   * @param {string} contactId - 联系人id
   */
  async removeContact(contactId) {
    const userId = await this.getUserId();
    return knex("recent_contact").where({ userId, contactId }).del();
  }

  /**
   * 移除全部联系人
   */
  async removeAllContact() {
    const userId = await this.getUserId();
    return knex("recent_contact").where({ userId }).del();
  }

  /**
   * 重新登录同步联系人
   * @param {string} contactData - JSON数组
   */
  async syncContactData(contactData) {
    if (!contactData) return;
    try {
      const userId = await this.getUserId();
      if (!userId) return;

      // 解析并格式化 contactData
      const parsedContactData = JSON.parse(contactData).map((o) => ({
        userId,
        contactId: o.contactId,
        updateTime: o.updateTime,
        isAt: o.isAt,
        isTop: o.isTop,
        content: JSON.stringify(o),
      }));
      if (!parsedContactData.length) return;

      // 批量插入/更新
      await knex("recent_contact")
        .insert(parsedContactData)
        .onConflict(["userId", "contactId"])
        .merge();
    } catch (error) {
      Log.error("Error syncing contact data:", error);
    }
  }
}

ContactService.toString = () => "[class ContactService]";
module.exports = ContactService;
