{"name": "Lark-Desktop", "version": "4.0.0", "description": "易知云雀", "main": "main.js", "scripts": {"dev": "ee-bin dev", "dev-frontend": "ee-bin dev --serve=frontend", "dev-electron": "ee-bin dev --serve=electron", "dev-go": "ee-bin exec --cmds=go", "dev-go-w": "ee-bin exec --cmds=go_w", "dev-python": "ee-bin exec --cmds=python", "build-frontend": "ee-bin build --cmds=frontend && ee-bin move --flag=frontend_dist", "build-go-w": "ee-bin move --flag=go_static,go_config,go_package,go_images && ee-bin build --cmds=go_w", "build-go-m": "ee-bin move --flag=go_static,go_config,go_package,go_images && ee-bin build --cmds=go_m", "build-go-l": "ee-bin move --flag=go_static,go_config,go_package,go_images && ee-bin build --cmds=go_l", "build-python": "ee-bin build --cmds=python && ee-bin move --flag=python_dist", "start": "ee-bin start", "move": "ee-bin move --flag=go_static,go_config,go_package,go_images", "rd": "ee-bin move --flag=frontend_dist", "encrypt": "ee-bin encrypt", "clean": "ee-bin clean", "icon": "ee-bin icon", "reload": "nodemon --config ./electron/config/nodemon.json", "rebuild": "electron-rebuild", "re-sqlite": "electron-rebuild -f -w better-sqlite3", "build-w": "electron-builder --config=./electron/config/builder.json -w=nsis --x64", "build-w-32": "electron-builder --config=./electron/config/builder.json -w=nsis --ia32", "build-w-64": "electron-builder --config=./electron/config/builder.json -w=nsis --x64", "build-w-arm64": "electron-builder --config=./electron/config/builder.json -w=nsis --arm64", "build-we": "electron-builder --config=./electron/config/builder.json -w=portable --x64", "build-wz": "electron-builder --config=./electron/config/builder.json -w=7z --x64", "build-wz-32": "electron-builder --config=./electron/config/builder.json -w=7z --ia32", "build-wz-64": "electron-builder --config=./electron/config/builder.json -w=7z --x64", "build-wz-arm64": "electron-builder --config=./electron/config/builder.json -w=7z --arm64", "build-m": "electron-builder --config=./electron/config/builder.json -m", "build-m-arm64": "electron-builder --config=./electron/config/builder.json -m --arm64", "build-l": "electron-builder --config=./electron/config/builder.json -l=deb --x64", "build-l-32": "electron-builder --config=./electron/config/builder.json -l=deb --ia32", "build-l-64": "electron-builder --config=./electron/config/builder.json -l=deb --x64", "build-l-arm64": "electron-builder --config=./electron/config/builder.json -l=deb --arm64", "build-l-armv7l": "electron-builder --config=./electron/config/builder.json -l=deb --armv7l", "build-lr-64": "electron-builder --config=./electron/config/builder.json -l=rpm --x64", "build-lr-arm64": "electron-builder --config=./electron/config/builder.json -l=rpm --arm64", "build-lp-64": "electron-builder --config=./electron/config/builder.json -l=pacman --x64", "test": "set DEBUG=* && electron . --env=local"}, "repository": "", "keywords": ["Electron", "electron-egg", "ElectronEgg"], "author": "云雀团队 @2024", "license": "Apache", "devDependencies": {"@electron/rebuild": "^3.2.13", "debug": "^4.3.3", "ee-bin": "^1.8.3", "electron": "^21.4.4", "electron-builder": "^23.6.0", "electron-rebuild": "^3.2.0", "eslint": "^5.13.0", "eslint-plugin-prettier": "^3.0.1", "node-gyp": "^11.2.0", "nodemon": "^2.0.16"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "better-sqlite3": "^8.4.0", "dayjs": "^1.10.7", "ee-core": "^2.12.0", "electron-store": "^7.0.0", "electron-updater": "^5.3.0", "g": "^2.0.1", "knex": "3.1.0", "koffi": "^2.11.0", "lodash": "^4.17.21", "spark-md5": "^3.0.2", "uuid": "^11.1.0", "axios": "^1.6.7"}}