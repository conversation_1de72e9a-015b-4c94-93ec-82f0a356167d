const Conf = require("ee-core/config");
const Ps = require("ee-core/ps");
const Services = require("ee-core/services");
const Addon = require("ee-core/addon");
const { Controller } = require("ee-core");

class EveryThingController extends Controller {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * test
   */
  async search(searchStr) {
    const result = await Services.get("everything").search(searchStr);
    // Log.info("EveryThingController search:", result);

    return result;
  }
}

EveryThingController.toString = () => "[class EveryThingController]";
module.exports = EveryThingController;
