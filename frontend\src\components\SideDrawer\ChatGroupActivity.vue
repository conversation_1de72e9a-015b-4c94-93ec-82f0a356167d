<template>
  <div class="drawer-box">
    <slot :open="openDrawer" :badge="count"></slot>
    <el-drawer v-model="visible" size="400px" @close="closeDrawer" class="custom-drawer">
      <template #header>
        <span>群组活动</span>
      </template>
      <template #default>
        <div class="filter-tabs">
          <div class="slider-tabs">
            <div class="slider-track">
              <div class="slider-indicator" :style="sliderStyle"></div>
            </div>
            <div
              v-for="(tab, index) in tabs"
              :key="tab.value"
              class="slider-tab"
              :class="{ active: activeTab === tab.value }"
              @click="selectTab(tab.value, index)"
            >
              <el-badge :value="tab.badge" :hidden="tab.badge == 0" :offset="[8, 0]">
                <span>
                  <font-awesome-icon :icon="tab.icon" class="mr-1.5 w-4 h-4"></font-awesome-icon>
                </span>
                <span>{{ tab.label }}</span>
              </el-badge>
            </div>
          </div>
        </div>

        <div class="content-area">
          <el-scrollbar v-if="activeTab === '1'">
            <div
              v-for="item in voteList"
              :key="item.id"
              @click="openDialog(item)"
              class="flex items-center justify-between px-4 py-2 border-b border-gray-200/70 hover:bg-blue-50 cursor-pointer"
            >
              <h4 class="text-sm text-gray-900">{{ item.title }}</h4>
              <span class="text-sm text-gray-600">{{ item.status == "0" ? "进行中" : "已结束" }}</span>
            </div>
            <NoData v-if="voteList.length == 0" />
          </el-scrollbar>

          <el-scrollbar v-else-if="activeTab === '2'">
            <div
              v-for="item in relayList"
              :key="item.id"
              @click="openDialog(item)"
              class="flex items-center justify-between px-4 py-2 border-b border-gray-200/70 hover:bg-blue-50 cursor-pointer"
            >
              <h4 class="text-sm text-gray-900">{{ item.title }}</h4>
              <span class="text-sm text-gray-600">{{ item.status == "0" ? "进行中" : "已结束" }}</span>
            </div>
            <NoData v-if="relayList.length == 0" />
          </el-scrollbar>

          <el-scrollbar v-else>
            <div
              v-for="item in officialDocList"
              :key="item.id"
              @click="openDialog(item)"
              class="flex items-center justify-between px-4 py-2 border-b border-gray-200/70 hover:bg-blue-50 cursor-pointer"
            >
              <h4 class="text-sm text-gray-900">{{ item.title }}</h4>
              <span class="text-sm text-gray-600">{{ item.status == "0" ? "未学习" : "已学习" }}</span>
            </div>
            <NoData v-if="officialDocList.length == 0" />
          </el-scrollbar>
        </div>
        <el-dialog v-model="listVisible" title="群组活动" class="custom-dialog">
          <div v-if="listVisible">
            <MessageVote :vote-id="String(current.id)" v-if="activeTab == '1'" />
            <MessageRelay :relay-id="String(current.id)" v-if="activeTab == '2'" />
            <MessageOfficialDoc :doc-id="String(current.id)" v-if="activeTab == '3'" />
          </div>
        </el-dialog>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useHistoryStore } from "@/stores/modules/history";
import MessageVote from "@/views/chat/components/ChatContent/MessageVote.vue";
import MessageRelay from "@/views/chat/components/ChatContent/MessageRelay.vue";
import MessageOfficialDoc from "@/views/chat/components/ChatContent/MessageOfficialDoc.vue";
import NoData from "@/components/NoData/index.vue";

const historyStore = useHistoryStore();

const visible = ref(false);
const openDrawer = () => {
  visible.value = true;
};
const closeDrawer = () => {
  visible.value = false;
};

const activeTab = ref("1");
const activeTabIndex = ref(0);
const voteList = computed(() => historyStore.activity.votesList || []);
const relayList = computed(() => historyStore.activity.relayList || []);
const officialDocList = computed(() => historyStore.activity.messageOfficialList || []);
const votesBadge = computed(() => {
  let num = 0;
  voteList.value?.forEach((item: any) => {
    if (item.participationStatus === "1" && item.status === "0") num++;
  });
  return num;
});
const relayBadge = computed(() => {
  let num = 0;
  relayList.value?.forEach((item: any) => {
    if (item.participationStatus === "1" && item.status === "0") num++;
  });
  return num;
});
const messageOfficialBadge = computed(() => {
  let num = 0;
  officialDocList.value?.forEach((item: any) => {
    if (item.participationStatus === "1" && item.status === "0") num++;
  });
  return num;
});
const tabs = computed(() => [
  { label: "投票", value: "1", badge: votesBadge.value, icon: ["fas", "poll"] },
  { label: "接龙", value: "2", badge: relayBadge.value, icon: ["fas", "list"] },
  { label: "公文", value: "3", badge: messageOfficialBadge.value, icon: ["fas", "book"] }
]);

const sliderStyle = computed(() => {
  const tabWidth = 100 / tabs.value.length;
  return {
    width: `${tabWidth}%`,
    transform: `translateX(${activeTabIndex.value * 100}%)`
  };
});

const selectTab = (value: string, index: number) => {
  activeTab.value = value;
  activeTabIndex.value = index;
};
const count = computed(() => {
  let num = 0;
  [...voteList.value, ...relayList.value, ...officialDocList.value]?.forEach((item: any) => {
    if (item.participationStatus === "1" && item.status === "0") num++;
  });
  return num;
});

const listVisible = ref(false);
const current: any = ref({});
const openDialog = (data: any) => {
  current.value = data;
  listVisible.value = true;
};
</script>

<style lang="scss" scoped>
.el-badge {
  display: flex !important;
  align-items: center;
}

.drawer-box {
  /* 筛选标签容器 */
  .filter-tabs {
    @apply mb-6;

    .slider-tabs {
      @apply relative flex w-full bg-gray-100 rounded-lg p-1;

      .slider-track {
        @apply absolute inset-1 pointer-events-none;

        .slider-indicator {
          @apply h-full bg-white rounded-md shadow-sm transition-transform duration-300 ease-out;
        }
      }

      .slider-tab {
        @apply relative z-10 flex items-center justify-center py-2 px-3 text-xs font-medium cursor-pointer transition-colors duration-200;
        color: #6b7280;
        // display: inline-block;
        width: 33.33%;

        &:hover {
          color: #374151;
        }

        &.active {
          color: #3b82f6;
          font-weight: 600;
        }
      }
    }
  }

  .content-area {
    .el-scrollbar {
      height: calc(100vh - 210px);
    }
  }
}

/* 暗色主题样式 */
.dark .drawer-box {
  .filter-tabs .slider-tabs {
    @apply bg-gray-800;

    .slider-indicator {
      @apply bg-gray-700;
    }

    .slider-tab {
      color: #9ca3af;

      &:hover {
        color: #f3f4f6;
      }

      &.active {
        color: #60a5fa;
      }
    }
  }
}
</style>
