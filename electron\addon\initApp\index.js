const Services = require("ee-core/services");
const Log = require("ee-core/log");
const { ipcMain } = require("electron");
const CoreWindow = require("ee-core/electron/window");
const Ps = require("ee-core/ps");
const knex = require("../../database/knex");

/**
 * 初始化json数据库
 * todo 后续会存储本地消息
 * @class
 */
class InitApp {
  constructor() {
    this.initAppData = {
      url: "http://************",
      version: "3.0.1",
    };
  }

  /**
   * 初始化数据
   */
  async create() {
    Log.info("[addon:initAppData] load");
    await Services.get("database.jsondb").createInitAppData(this.initAppData);

    const mainWindow = CoreWindow.getMainWindow();
    ipcMain.on("window-op-minimize", () => {
      mainWindow.minimize();
    });
    ipcMain.on("window-op-togglemaximize", () => {
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize();
      } else {
        mainWindow.maximize();
      }
    });
    ipcMain.on("window-op-close", () => {
      mainWindow.close();
    });
  }
}

InitApp.toString = () => "[class InitApp]";
module.exports = InitApp;
