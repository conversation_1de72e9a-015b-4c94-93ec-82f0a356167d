<template>
  <div class="chat-head">
    <div class="flex items-center">
      <div class="mr-3 flex size-10 shrink-0 items-center justify-center">
        <DynamicAvatar
          :id="contact.contactId"
          :data-info="contact"
          :relation-name="contact?.contactName"
          :type-avatar="contact.chatType"
          :size="40"
        />
      </div>
      <div>
        <div class="mb-1 flex items-center">
          <h2 class="text-base font-medium text-gray-900">{{ contact?.contactName }}</h2>
          <Icon
            :icon="medalRibbonsStarBoldDuotone"
            width="21"
            height="21"
            style="color: #eaa800"
            class="ml-2"
            v-if="contact?.chatType == 2"
          />
        </div>
        <div class="flex items-center gap-2">
          <LevelBtn
            v-if="contact?.secret"
            :message="contact?.secret"
            :data-type="contact?.chatType == 0 ? 'user' : 'obj'"
          ></LevelBtn>
          <span
            class="group-tag border border-blue-200 rounded-md bg-blue-100 px-2 text-xs font-medium text-blue-800"
            v-if="contact?.chatType !== 0"
          >
            {{ contact?.chatType == 1 ? "普通群" : contact?.chatType == 2 ? "官方群" : "" }}
          </span>
          <span class="group-info text-xs text-gray-500" v-if="contact?.chatType !== 0">
            {{ contactStore.groupMembers[talkStore.activeChatId]?.length }} 成员
          </span>
        </div>
      </div>
    </div>
    <div class="flex items-center space-x-2">
      <ChatHistoryDrawer>
        <template #default="{ open }">
          <button @click="open" class="chat-head-btn" title="聊天记录">
            <Icon :icon="documentBoldDuotone" width="21" height="21" />
          </button>
        </template>
      </ChatHistoryDrawer>

      <ChatGroupMember v-if="contact?.chatType">
        <template #default="{ open }">
          <button @click="open" class="chat-head-btn" title="群组成员">
            <Icon :icon="usersGroupRoundedBoldDuotone" width="21" height="21" />
          </button>
        </template>
      </ChatGroupMember>

      <ChatGroupInfo v-if="contact?.chatType">
        <template #default="{ open }">
          <button @click="open" class="chat-head-btn" title="群组信息">
            <Icon :icon="userIdBoldDuotone" width="21" height="21" />
          </button>
        </template>
      </ChatGroupInfo>

      <ChatGroupActivity v-if="contact?.chatType">
        <template #default="{ open, badge }">
          <el-badge :value="badge" :hidden="badge == 0" v-if="contact?.chatType">
            <button @click="open" class="chat-head-btn" title="群组活动">
              <Icon :icon="checklistMinimalisticBoldDuotone" width="21" height="21" />
            </button>
          </el-badge>
        </template>
      </ChatGroupActivity>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useTalkStore } from "@/stores/modules/talk";
import { useContactStore } from "@/stores/modules/friend";
import LevelBtn from "@/components/LevelBtn/index.vue";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import ChatHistoryDrawer from "@/components/SideDrawer/ChatHistoryDrawer.vue";
import ChatGroupMember from "@/components/SideDrawer/ChatGroupMember.vue";
import ChatGroupInfo from "@/components/SideDrawer/ChatGroupInfo.vue";
import ChatGroupActivity from "@/components/SideDrawer/ChatGroupActivity.vue";
import { Icon } from "@iconify/vue";

// 导入离线图标
import medalRibbonsStarBoldDuotone from "@iconify-icons/solar/medal-ribbons-star-bold-duotone";
import documentBoldDuotone from "@iconify-icons/solar/document-bold-duotone";
import usersGroupRoundedBoldDuotone from "@iconify-icons/solar/users-group-rounded-bold-duotone";
import userIdBoldDuotone from "@iconify-icons/solar/user-id-bold-duotone";
import checklistMinimalisticBoldDuotone from "@iconify-icons/solar/checklist-minimalistic-bold-duotone";
const talkStore = useTalkStore();
const contactStore = useContactStore();

const contact: any = computed(() => talkStore.activeContact);
</script>

<style scoped lang="scss">
.chat-head {
  height: 61px;
  @apply absolute top-0 left-0 right-0 flex items-center justify-between border-b border-gray-200 bg-white px-4;

  .chat-head-btn {
    @apply flex items-center justify-center w-8 h-8 rounded-full text-gray-600 transition-all hover:bg-gray-100 cursor-pointer;
  }
}

.dark {
  .chat-head {
    @apply border-gray-700 bg-gray-800;

    h2 {
      @apply text-white;
    }

    .group-info {
      @apply text-gray-400;
    }

    .group-tag {
      @apply bg-blue-900/30 text-blue-500 border-blue-800/50;
    }

    .chat-head-btn {
      @apply text-gray-400 hover:bg-gray-700;
    }
  }
}
</style>
