const Store = require("electron-store");
const { Service } = require("ee-core");

class LarkFileStoreService extends Service {
  constructor(ctx) {
    super(ctx);
    // 使用 electron-store 存储到 "lark_file.json" 文件
    this.store = new Store({ name: "lark_file" });
    // 初始化存储结构，使用 "files" 键存放所有记录（数组）
    if (!this.store.has("files")) {
      this.store.set("files", []);
    }
  }

  // 获取所有 lark_file 记录
  getAllLarkFiles() {
    return this.store.get("files", []);
  }

  // 保存所有记录
  saveAllLarkFiles(files) {
    this.store.set("files", files);
  }

  /**
   * 新增 lark_file 数据
   * @param {Object} data - 文件记录对象
   * @param {string} data.userId 下载人
   * @param {string} data.file_id 文件id（唯一）
   * @param {string} data.file_name 文件名称
   * @param {string} data.download_url 文件下载地址
   * @param {string} data.download_status 文件下载状态（0:未开始 1:下载中 2:已完成 3:已取消 4:下载失败）
   * @param {number} data.download_time 文件下载时间（时间戳）
   * @param {string} data.save_path 文件保存路径
   * @param {number} data.file_size 文件大小（单位：字节）
   * @returns {boolean}
   */
  addLarkFile(data) {
    const files = this.getAllLarkFiles();
    // 检查是否已存在相同 file_id 的记录
    const exists = files.find((item) => item.file_id === data.file_id);
    if (exists) {
      throw new Error(`File with file_id "${data.file_id}" already exists.`);
    }
    files.push(data);
    this.saveAllLarkFiles(files);
    return true;
  }

  /**
   * 修改 lark_file 数据（根据 file_id 更新记录）
   * @param {Object} data - 文件记录对象，必须包含 file_id 字段
   * @returns {boolean}
   */
  updateLarkFile(data) {
    if (!data.file_id) {
      throw new Error("file_id 是必需的");
    }
    const files = this.getAllLarkFiles();
    const index = files.findIndex((item) => item.file_id === data.file_id);
    if (index === -1) {
      throw new Error(`File with file_id "${data.file_id}" does not exist.`);
    }
    // 合并更新，仅修改传入的属性，其它保持不变
    files[index] = { ...files[index], ...data };
    this.saveAllLarkFiles(files);
    return true;
  }

  /**
   * 根据 file_id 查询 lark_file 记录，如果 file_id 为空，则返回所有记录
   * @param {string} [file_id]
   * @returns {Array} 查询结果数组
   */
  getLarkFileByFileId(file_id) {
    const files = this.getAllLarkFiles();
    if (file_id) {
      return files.filter((item) => item.file_id === file_id);
    }
    return files;
  }

  /**
   * 根据 userId 查询 lark_file 记录，如果 userId 为空，则返回所有记录
   * @param {string} [userId]
   * @returns {Array} 查询结果数组
   */
  getLarkFileByUserId(userId) {
    const files = this.getAllLarkFiles();
    if (userId) {
      return files.filter((item) => item.userId === userId);
    }
    return files;
  }

  /**
   * 根据 file_id 删除 lark_file 记录
   * @param {string} file_id - 文件id
   * @returns {boolean}
   */
  deleteLarkFile(file_id) {
    if (!file_id) {
      throw new Error("file_id 是必需的");
    }
    let files = this.getAllLarkFiles();
    const newFiles = files.filter((item) => item.file_id !== file_id);
    if (newFiles.length === files.length) {
      // throw new Error(`No file found with file_id "${file_id}"`);
      return
    }
    this.saveAllLarkFiles(newFiles);
    return true;
  }
}

LarkFileStoreService.toString = () => "[class LarkFileStoreService]";
module.exports = LarkFileStoreService;
