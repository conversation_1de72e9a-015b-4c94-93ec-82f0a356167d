<template>
  <el-scrollbar>
    <ul class="user-list" ref="userList">
      <li
        v-for="(user, index) in props.filteredUsers"
        :key="user.id"
        :class="{ active: Number(index) == props.activeIndex }"
        @click="onSelect(user)"
      >
        <DynamicAvatar :id="user.member" :data-info="user" :relation-name="user.memberName" :size="40" :type-avatar="0" />
        <span class="username">
          {{ user.memberName }}
        </span>
      </li>
    </ul>
  </el-scrollbar>
</template>
<script setup lang="ts">
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";

const emit = defineEmits(["select-user"]);

const props = defineProps({
  activeIndex: {
    type: Number
  },
  filteredUsers: {
    type: Object
  }
});
const onSelect = (user: any) => {
  emit("select-user", user);
};
</script>
<style scoped lang="scss">
.el-scrollbar {
  height: 300px;
}
.user-list {
  @apply rounded-lg;
}

.user-list li {
  padding: 8px 16px;
  cursor: pointer;
  list-style-type: none;
  display: flex;
  align-items: center;
}

.user-list li.active {
  background-color: #f0f0f0;
}

.member-avatar {
  background: #4da6fa;
}

.username {
  font-size: 14px;
  margin-left: 12px;
}
</style>
