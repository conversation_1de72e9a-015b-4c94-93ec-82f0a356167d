export declare type ISoftwareSetting = {
  theme: "light" | "dark";
  isAutoStartup: boolean;
  isOpenAtStartup: boolean;
  isShowTrayIcon: boolean;

  isShowSoftwareName: boolean;
  isShowSoftwareNameTooltip: boolean;
  softwareSize: number;

  isBallShow: boolean;
  isBallAlwaysOnTop: boolean;
  ballTransparency: number;
  ballContent: "icon" | "time" | "clock";

  dialogTransparency: number;
};

export declare type IShortcut = {
  [key: string]: any;
  showMainWin: any;
  showSoftwareDialog: any;
  showFloatingBall: any;
};
