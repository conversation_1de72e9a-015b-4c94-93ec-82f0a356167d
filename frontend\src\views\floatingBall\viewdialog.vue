<template>
  <div class="p-6 min-h-screen text-white">
    <!-- 关闭按钮 -->
    <el-button class="absolute top-4 right-4" type="danger" circle size="small" @click="handleClose">
      <el-icon><Close /></el-icon>
    </el-button>
    <!-- 文档摘要 -->
    <el-card class="mb-6" shadow="hover">
      <template #header>
        <div class="text-lg font-semibold">📄 文档摘要</div>
      </template>
      <div>
        <p>{{ summary }}</p>
        <p>{{ summaryText }}</p>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { ipc } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { useRoute } from "vue-router";
import { onMounted } from "vue";
import { ElMessage } from "element-plus";

const route = useRoute();
const summaryText = ref("");

onMounted(() => {
  const rawParam = route.query.param as string;
  let parsedParam = {};

  try {
    // parsedParam = JSON.parse(rawParam);
    summaryText.value = rawParam.fileName;
  } catch (e:any) {
    ElMessage.error("参数解析失败", e);
  }
});

const summary = ref(
  "本系统旨在帮助用户快速了解文档内容，并支持演示相关模块或功能。使用 Vue 3 + TypeScript + Element Plus 构建，具有良好的用户体验与可扩展性。"
);

function handleClose() {
  ipc.send(ipcApiRoute.closeViewDialog);
}
</script>

<style scoped></style>
